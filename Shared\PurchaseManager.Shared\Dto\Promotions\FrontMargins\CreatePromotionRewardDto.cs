﻿using PurchaseManager.Constants.Enum;
namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
///     DTO for promotion reward
/// </summary>
public class CreatePromotionRewardDto
{
    public string ConditionItemNumber { get; set; } = string.Empty;
    public FrontMarginCalculationType RewardType { get; set; }

    // For percentage discount
    public decimal? DiscountPercent { get; set; }

    // For fixed amount discount
    public decimal? DiscountAmount { get; set; }

    // For gift items
    public string? RewardItemNumber { get; set; }
    public decimal? Quantity { get; set; }
    public string? UnitOfMeasure { get; set; }
}
