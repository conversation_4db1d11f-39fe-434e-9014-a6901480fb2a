namespace PurchaseManager.Server.Services.Promotions.Interface;

/// <summary>
///     Service for monitoring and optimizing Front Margin performance
/// </summary>
public interface IFrontMarginPerformanceService
{
    /// <summary>
    ///     Monitor calculation performance
    /// </summary>
    Task<PerformanceMetrics> MonitorCalculationPerformanceAsync(string vendorCode, int itemCount);

    /// <summary>
    ///     Get performance statistics
    /// </summary>
    Task<FrontMarginPerformanceStats> GetPerformanceStatsAsync();

    /// <summary>
    ///     Optimize database queries
    /// </summary>
    Task OptimizeDatabaseAsync();

    /// <summary>
    ///     Get slow query analysis
    /// </summary>
    Task<List<SlowQueryInfo>> GetSlowQueriesAsync();

    /// <summary>
    ///     Benchmark calculation engine
    /// </summary>
    Task<BenchmarkResult> BenchmarkCalculationEngineAsync();
}
