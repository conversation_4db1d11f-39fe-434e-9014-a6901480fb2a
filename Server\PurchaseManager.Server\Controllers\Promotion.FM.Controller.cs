﻿using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Server.Controllers;

public partial class PromotionController
{
    [HttpPost("front-margin")]
    public async Task<ApiResponse> CreatePromotionFrontMargin([FromBody] CreatePromotionFrontMarginDto createDto)
    {
        return await _promotionService.CreatePromotionFrontMarginAsync(createDto);
    }

    [HttpPut("front-margin/{number}")]
    public async Task<ApiResponse> UpdatePromotionFrontMargin(string number, [FromBody] UpdatePromotionFrontMarginDto updateDto)
    {
        return await _promotionService.UpdatePromotionFrontMarginAsync(number, updateDto);
    }

    [HttpDelete("front-margin")]
    public async Task<ApiResponse> DeletePromotionFrontMargin([FromBody] List<string> numbers)
    {
        return await _promotionManager.DeletePromotionFrontMarginAsync(numbers);
    }
}
