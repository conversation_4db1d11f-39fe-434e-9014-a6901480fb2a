﻿@page "/dashboard"
@attribute [Authorize]
@inject AppState appState

<PageTitle>Dashboard</PageTitle>

<MudStack Spacing="6" Class="mt-6" Row="true" StretchItems="StretchItems.All">
    <MudPaper Class="pa-4">
        <MudStack Row="true">
            <MudAvatar Size="Size.Large" Color="Color.Primary">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                     stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"
                     transform="rotate(0)"><!--!-->
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M16.7 8a3 3 0 0 0 -2.7 -2h-4a3 3 0 0 0 0 6h4a3 3 0 0 1 0 6h-4a3 3 0 0 1 -2.7 -2"></path>
                    <path d="M12 3v3m0 12v3"></path>
                </svg>
            </MudAvatar>
            <MudStack Justify="Justify.Center" Spacing="0">
                <MudText Typo="Typo.body1"><b>132 Sales</b></MudText>
                <MudText Typo="Typo.body2" Color="Color.Surface">12 waiting payments</MudText>
            </MudStack>
        </MudStack>
    </MudPaper>
    <MudPaper Class="pa-4">
        <MudStack Row="true">
            <MudAvatar Size="Size.Large" Color="Color.Success">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                     stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"
                     transform="rotate(0)"><!--!-->
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M16.7 8a3 3 0 0 0 -2.7 -2h-4a3 3 0 0 0 0 6h4a3 3 0 0 1 0 6h-4a3 3 0 0 1 -2.7 -2"></path>
                    <path d="M12 3v3m0 12v3"></path>
                </svg>
            </MudAvatar>
            <MudStack Justify="Justify.Center" Spacing="0">
                <MudText Typo="Typo.body1"><b>1234 Order</b></MudText>
                <MudText Typo="Typo.body2" Color="Color.Surface">892 Shipped</MudText>
            </MudStack>
        </MudStack>
    </MudPaper>
    <MudPaper Class="pa-4">
        <MudStack Row="true">
            <MudAvatar Size="Size.Large" Color="Color.Warning">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                     stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"
                     transform="rotate(0)"><!--!-->
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M7 10v4h3v7h4v-7h3l1 -4h-4v-2a1 1 0 0 1 1 -1h3v-4h-3a5 5 0 0 0 -5 5v2h-3"></path>
                </svg>
            </MudAvatar>
            <MudStack Justify="Justify.Center" Spacing="0">
                <MudText Typo="Typo.body1"><b>132 Like</b></MudText>
                <MudText Typo="Typo.body2" Color="Color.Surface">21 to day</MudText>
            </MudStack>
        </MudStack>
    </MudPaper>
    <MudPaper Class="pa-4">
        <MudStack Row="true">
            <MudAvatar Size="Size.Large" Color="Color.Info">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" stroke-width="2"
                     stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"
                     transform="rotate(0)"><!--!-->
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M9 7m-4 0a4 4 0 1 0 8 0a4 4 0 1 0 -8 0"></path>
                    <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"></path>
                </svg>
            </MudAvatar>
            <MudStack Justify="Justify.Center" Spacing="0">
                <MudText Typo="Typo.body1"><b>876 Members</b></MudText>
                <MudText Typo="Typo.body2" Color="Color.Surface">87 Registered today</MudText>
            </MudStack>
        </MudStack>
    </MudPaper>
</MudStack>
<MudStack Spacing="6" Class="mt-6" Row="true" StretchItems="StretchItems.All">
    <MudPaper Class="pa-4">
        <MudChart ChartType="ChartType.Donut" Width="300px" Height="300px" InputData="@data" InputLabels="@labels">
            <CustomGraphics>
                <text class="donut-inner-text" x="47%" y="35%" dominant-baseline="middle" text-anchor="middle"
                      fill="black" font-family="Helvetica" font-size="2">Total
                </text>
                <text class="donut-inner-text" x="47%" y="50%" dominant-baseline="middle" text-anchor="middle"
                      fill="black" font-family="Helvetica" font-size="5">@data.Sum().ToString()</text>
            </CustomGraphics>
        </MudChart>
    </MudPaper>
    <MudPaper Class="pa-4">
        <MudChart ChartType="ChartType.Pie" InputData="@data" SelectedIndex="0" InputLabels="@labels" Width="300px"
                  Height="300px"/>
    </MudPaper>
</MudStack>

@code {
    public double[] data = { 25, 77, 28, 5 };
    public string[] labels = { "Oil", "Coal", "Gas", "Biomass" };

    protected override async Task OnInitializedAsync()
    {
    }

}
