namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

public class GetPromotionFrontMarginDto
{
    public string Number { get; set; } = null!;

    public string ProgramNumber { get; set; } = null!;

    public int LineNumber { get; set; }

    public string ItemNumber { get; set; } = null!;

    public string ItemName { get; set; } = null!;

    public string UnitOfMeasure { get; set; } = null!;

    /// <summary>
    /// Loại chiết khấu: 1=Percentage, 2=FixedAmount, 3=SameItemGift, 4=DifferentItemGift
    /// </summary>
    public int DiscountType { get; set; } = 1;

    // CASE I.1: Percentage Discount
    public decimal DiscountPercentage { get; set; } = 0;

    // CASE I.2: Fixed Amount Discount
    public decimal? FixedDiscountAmount { get; set; }

    // CASE II: Same Item Gift (Buy X Get Y Free)
    public decimal? BuyQuantity { get; set; }
    public decimal? GiftQuantity { get; set; }

    // CASE III: Different Item Gift
    public string? GiftItemNumber { get; set; }
    public string? GiftItemName { get; set; }
    public string? GiftItemUOM { get; set; }
    public decimal? GiftItemQuantity { get; set; }

    // Conditions
    public decimal? MinimumQuantity { get; set; }
    public decimal? MinimumAmount { get; set; }
    public decimal? MaximumDiscountAmount { get; set; }

    public int Status { get; set; } = 1; // 1 = Active, 2 = Inactive

    public string? Notes { get; set; }

    // Helper properties for UI and display (mapped by AutoMapper)
    public string DiscountTypeName { get; set; } = string.Empty;
    public bool IsPercentageDiscount { get; set; }
    public bool IsFixedAmountDiscount { get; set; }
    public bool IsSameItemGift { get; set; }
    public bool IsDifferentItemGift { get; set; }

    // Computed properties for display
    public string DisplayText => GetDisplayText();
    public string DiscountSummary => GetDiscountSummary();
    public bool HasValidConfiguration => ValidateConfiguration();

    /// <summary>
    /// Get display text for UI
    /// </summary>
    private string GetDisplayText()
    {
        return DiscountType switch
        {
            1 => $"{ItemName} - Chiết khấu {DiscountPercentage}%",
            2 => $"{ItemName} - Chiết khấu {FixedDiscountAmount:N0} VND",
            3 => $"{ItemName} - Mua {BuyQuantity} tặng {GiftQuantity}",
            4 => $"{ItemName} - Tặng {GiftItemQuantity} {GiftItemName}",
            _ => $"{ItemName} - Loại chiết khấu không xác định"
        };
    }

    /// <summary>
    /// Get discount summary for display
    /// </summary>
    private string GetDiscountSummary()
    {
        return DiscountType switch
        {
            1 => $"{DiscountPercentage}% off",
            2 => $"{FixedDiscountAmount:N0} VND off",
            3 => $"Buy {BuyQuantity} get {GiftQuantity} free",
            4 => $"Free {GiftItemQuantity} {GiftItemName}",
            _ => "Unknown discount"
        };
    }

    /// <summary>
    /// Validate configuration based on discount type
    /// </summary>
    private bool ValidateConfiguration()
    {
        return DiscountType switch
        {
            1 => DiscountPercentage > 0 && DiscountPercentage <= 100,
            2 => FixedDiscountAmount.HasValue && FixedDiscountAmount.Value > 0,
            3 => BuyQuantity.HasValue && BuyQuantity.Value > 0 &&
                 GiftQuantity.HasValue && GiftQuantity.Value > 0,
            4 => !string.IsNullOrEmpty(GiftItemNumber) &&
                 !string.IsNullOrEmpty(GiftItemName) &&
                 GiftItemQuantity.HasValue && GiftItemQuantity.Value > 0,
            _ => false
        };
    }
}
