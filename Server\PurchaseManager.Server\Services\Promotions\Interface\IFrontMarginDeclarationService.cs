using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Server.Services.Promotions.Interface;

public interface IFrontMarginDeclarationService
{
    /// <summary>
    ///     Validate Front Margin declaration before saving
    /// </summary>
    Task<FrontMarginDeclarationResult> ValidateDeclarationAsync(CreatePromotionFrontMarginDto dto);

    /// <summary>
    ///     Register Front Margin for approval workflow
    /// </summary>
    Task<FrontMarginDeclarationResult> RegisterForApprovalAsync(CreatePromotionFrontMarginDto dto, string createdBy);

    /// <summary>
    ///     Bulk register multiple Front Margins
    /// </summary>
    Task<FrontMarginBulkDeclarationResult> BulkRegisterAsync(List<CreatePromotionFrontMarginDto> dtos, string createdBy);

    /// <summary>
    ///     Get declaration status and workflow information
    /// </summary>
    Task<FrontMarginDeclarationStatus> GetDeclarationStatusAsync(string number);

    /// <summary>
    ///     Check for conflicts with existing promotions
    /// </summary>
    Task<List<FrontMarginConflict>> CheckConflictsAsync(CreatePromotionFrontMarginDto dto);

    /// <summary>
    ///     Preview calculation impact before registration
    /// </summary>
    Task<FrontMarginImpactPreview> PreviewImpactAsync(CreatePromotionFrontMarginDto dto);
}
