{"info": {"name": "Front Margin API Tests", "description": "Comprehensive test collection for Front Margin API endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "https://localhost:7001", "type": "string"}, {"key": "testProgramNumber", "value": "TEST_FM_2025_001", "type": "string"}, {"key": "createdFrontMarginNumber", "value": "", "type": "string"}], "item": [{"name": "1. Get Discount Types", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/FrontMargin/discount-types", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin", "discount-types"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has discount types', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.be.an('array');", "    pm.expect(jsonData.data.length).to.equal(4);", "});"]}}]}, {"name": "2. <PERSON> Filtered - All", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/FrontMargin/filtered?pageSize=10&pageIndex=0", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin", "filtered"], "query": [{"key": "pageSize", "value": "10"}, {"key": "pageIndex", "value": "0"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has paged result structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('data');", "    pm.expect(jsonData.data).to.have.property('currentPage');", "    pm.expect(jsonData.data).to.have.property('pageSize');", "    pm.expect(jsonData.data).to.have.property('rowCount');", "});"]}}]}, {"name": "3. <PERSON>gins Filtered - By Program", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/FrontMargin/filtered?programNumber={{testProgramNumber}}&pageSize=10&pageIndex=0", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin", "filtered"], "query": [{"key": "programNumber", "value": "{{testProgramNumber}}"}, {"key": "pageSize", "value": "10"}, {"key": "pageIndex", "value": "0"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filtered results contain correct program number', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.data.data.length > 0) {", "        jsonData.data.data.forEach(item => {", "            pm.expect(item.programNumber).to.include(pm.variables.get('testProgramNumber'));", "        });", "    }", "});"]}}]}, {"name": "4. <PERSON>gins Filtered - By Discount Type", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/FrontMargin/filtered?discountType=1&pageSize=10&pageIndex=0", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin", "filtered"], "query": [{"key": "discountType", "value": "1"}, {"key": "pageSize", "value": "10"}, {"key": "pageIndex", "value": "0"}]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Filtered results contain correct discount type', function () {", "    const jsonData = pm.response.json();", "    if (jsonData.data.data.length > 0) {", "        jsonData.data.data.forEach(item => {", "            pm.expect(item.discountType).to.equal(1);", "        });", "    }", "});"]}}]}, {"name": "5. Validate Front Margin - Valid Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"programNumber\": \"{{testProgramNumber}}\",\n  \"lineNumber\": 999,\n  \"itemNumber\": \"API_TEST_ITEM\",\n  \"itemName\": \"API Test Item\",\n  \"unitOfMeasure\": \"PCS\",\n  \"discountType\": 1,\n  \"discountPercentage\": 15.5,\n  \"minimumQuantity\": 10,\n  \"minimumAmount\": 100000,\n  \"status\": 1,\n  \"notes\": \"API validation test\"\n}"}, "url": {"raw": "{{baseUrl}}/api/FrontMargin/validate", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin", "validate"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Validation passes for valid data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.isValid).to.be.true;", "});"]}}]}, {"name": "6. Validate Front Margin - Invalid Data", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"programNumber\": \"\",\n  \"lineNumber\": 0,\n  \"itemNumber\": \"\",\n  \"itemName\": \"\",\n  \"unitOfMeasure\": \"\",\n  \"discountType\": 1,\n  \"discountPercentage\": 150,\n  \"status\": 1\n}"}, "url": {"raw": "{{baseUrl}}/api/FrontMargin/validate", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin", "validate"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Validation fails for invalid data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data.isValid).to.be.false;", "    pm.expect(jsonData.data.errors).to.be.an('array');", "    pm.expect(jsonData.data.errors.length).to.be.greaterThan(0);", "});"]}}]}, {"name": "7. <PERSON><PERSON> <PERSON> - Percentage Discount", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"programNumber\": \"{{testProgramNumber}}\",\n  \"lineNumber\": 1001,\n  \"itemNumber\": \"API_CREATE_TEST_001\",\n  \"itemName\": \"API Created Item - Percentage\",\n  \"unitOfMeasure\": \"PCS\",\n  \"discountType\": 1,\n  \"discountPercentage\": 12.5,\n  \"minimumQuantity\": 20,\n  \"minimumAmount\": 200000,\n  \"status\": 1,\n  \"notes\": \"Created via API test - Percentage discount\"\n}"}, "url": {"raw": "{{baseUrl}}/api/FrontMargin", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON>gin created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data).to.have.property('number');", "    pm.collectionVariables.set('createdFrontMarginNumber', jsonData.data.number);", "});"]}}]}, {"name": "8. <PERSON> by Number", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/FrontMargin/{{createdFrontMarginNumber}}", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin", "{{createdFrontMarginNumber}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Retrieved Front Margin matches created data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.number).to.equal(pm.collectionVariables.get('createdFrontMarginNumber'));", "    pm.expect(jsonData.data.discountType).to.equal(1);", "    pm.expect(jsonData.data.discountPercentage).to.equal(12.5);", "});"]}}]}, {"name": "9. Update Front Margin", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"itemNumber\": \"API_CREATE_TEST_001_UPDATED\",\n  \"itemName\": \"API Updated Item - Percentage\",\n  \"unitOfMeasure\": \"PCS\",\n  \"discountType\": 1,\n  \"discountPercentage\": 18.0,\n  \"minimumQuantity\": 25,\n  \"minimumAmount\": 250000,\n  \"status\": 1,\n  \"notes\": \"Updated via API test - Percentage discount\"\n}"}, "url": {"raw": "{{baseUrl}}/api/FrontMargin/{{createdFrontMarginNumber}}", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin", "{{createdFrontMarginNumber}}"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('<PERSON> Margin updated successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.discountPercentage).to.equal(18.0);", "    pm.expect(jsonData.data.itemName).to.include('Updated');", "});"]}}]}, {"name": "10. <PERSON><PERSON> <PERSON> - Fixed Amount", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"programNumber\": \"TEST_FM_2025_002\",\n  \"lineNumber\": 1002,\n  \"itemNumber\": \"API_CREATE_TEST_002\",\n  \"itemName\": \"API Created Item - Fixed Amount\",\n  \"unitOfMeasure\": \"PCS\",\n  \"discountType\": 2,\n  \"discountPercentage\": 0,\n  \"fixedDiscountAmount\": 75000,\n  \"minimumAmount\": 500000,\n  \"maximumDiscountAmount\": 200000,\n  \"status\": 1,\n  \"notes\": \"Created via API test - Fixed amount discount\"\n}"}, "url": {"raw": "{{baseUrl}}/api/FrontMargin", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Fixed amount Front Margin created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.discountType).to.equal(2);", "    pm.expect(jsonData.data.fixedDiscountAmount).to.equal(75000);", "});"]}}]}, {"name": "11. <PERSON><PERSON> <PERSON> Margin - Same Item Gift", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"programNumber\": \"TEST_FM_2025_003\",\n  \"lineNumber\": 1003,\n  \"itemNumber\": \"API_CREATE_TEST_003\",\n  \"itemName\": \"API Created Item - Same Item Gift\",\n  \"unitOfMeasure\": \"PCS\",\n  \"discountType\": 3,\n  \"discountPercentage\": 0,\n  \"buyQuantity\": 10,\n  \"giftQuantity\": 2,\n  \"minimumQuantity\": 10,\n  \"status\": 1,\n  \"notes\": \"Created via API test - Buy 10 get 2 free\"\n}"}, "url": {"raw": "{{baseUrl}}/api/FrontMargin", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Same item gift <PERSON> Margin created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.discountType).to.equal(3);", "    pm.expect(jsonData.data.buyQuantity).to.equal(10);", "    pm.expect(jsonData.data.giftQuantity).to.equal(2);", "});"]}}]}, {"name": "12. <PERSON><PERSON> <PERSON> - Different Item Gift", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"programNumber\": \"TEST_FM_2025_004\",\n  \"lineNumber\": 1004,\n  \"itemNumber\": \"API_CREATE_TEST_004\",\n  \"itemName\": \"API Created Item - Different Item Gift\",\n  \"unitOfMeasure\": \"PCS\",\n  \"discountType\": 4,\n  \"discountPercentage\": 0,\n  \"giftItemNumber\": \"GIFT_API_001\",\n  \"giftItemName\": \"API Gift Item\",\n  \"giftItemUOM\": \"PCS\",\n  \"giftItemQuantity\": 5,\n  \"minimumQuantity\": 50,\n  \"status\": 1,\n  \"notes\": \"Created via API test - Different item gift\"\n}"}, "url": {"raw": "{{baseUrl}}/api/FrontMargin", "host": ["{{baseUrl}}"], "path": ["api", "FrontMargin"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Different item gift Front Margin created successfully', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.success).to.be.true;", "    pm.expect(jsonData.data.discountType).to.equal(4);", "    pm.expect(jsonData.data.giftItemNumber).to.equal('GIFT_API_001');", "    pm.expect(jsonData.data.giftItemQuantity).to.equal(5);", "});"]}}]}]}