-- =============================================
-- Create Back Margin Tables Migration Script
-- Date: 2025-01-11
-- Description: Creates all tables for Back Margin system
-- =============================================

USE [PurchaseManager] -- Change to your database name
GO

PRINT 'Starting Back Margin Tables Creation...'
PRINT 'Database: ' + DB_NAME()
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
PRINT '============================================='

-- Check if Front Margin tables exist (prerequisite)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionFrontMargins')
BEGIN
    PRINT 'ERROR: Front Margin tables not found. Please run Front Margin migration first.'
    RETURN
END

-- =============================================
-- 1. Create PromotionBackMargins Table
-- =============================================
PRINT 'Creating PromotionBackMargins table...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionBackMargins')
BEGIN
    CREATE TABLE PromotionBackMargins (
        Number NVARCHAR(50) NOT NULL PRIMARY KEY,
        ProgramNumber NVARCHAR(50) NOT NULL,
        LineNumber INT NOT NULL,
        ItemNumber NVARCHAR(50) NULL, -- NULL for program-level discounts
        ItemName NVARCHAR(250) NULL,
        UnitOfMeasure NVARCHAR(50) NULL,

        -- Discount Type: 1=Sales Progressive, 2=Sales Tiered, 3=Qty Progressive, 4=Qty Tiered, 5=Early Payment
        DiscountType INT NOT NULL,

        -- Calculation Period
        CalculationPeriod NVARCHAR(20) NOT NULL, -- MONTHLY, QUARTERLY, YEARLY
        PeriodStartDate DATE NULL,
        PeriodEndDate DATE NULL,

        -- Payment Terms (for Early Payment Discount)
        StandardPaymentDays INT NULL,
        EarlyPaymentDays INT NULL,
        EarlyPaymentDiscountPercentage DECIMAL(5,2) NULL,

        -- Multiple Payment Methods Support
        SupportedPaymentMethods NVARCHAR(500) NULL, -- JSON array: ["CASH", "TRANSFER", "CHECK"]

        -- Status and Audit
        Status INT NOT NULL DEFAULT 1, -- 1=Draft, 2=Active, 3=Inactive, 4=Expired
        Notes NVARCHAR(MAX) NULL,
        CreatedBy NVARCHAR(100) NULL,
        CreatedAt DATETIME2 NULL,
        LastModifiedBy NVARCHAR(100) NULL,
        LastModifiedAt DATETIME2 NULL,
        ModificationStatus INT NOT NULL DEFAULT 1, -- 1=Active, 2=Deleted

        -- Constraints
        CONSTRAINT FK_PromotionBackMargins_PromotionHeaders
            FOREIGN KEY (ProgramNumber) REFERENCES PromotionHeaders(ProgramNumber),
        CONSTRAINT CK_PromotionBackMargins_DiscountType
            CHECK (DiscountType IN (1, 2, 3, 4, 5)),
        CONSTRAINT CK_PromotionBackMargins_CalculationPeriod
            CHECK (CalculationPeriod IN ('MONTHLY', 'QUARTERLY', 'YEARLY')),
        CONSTRAINT UQ_PromotionBackMargins_ProgramLine
            UNIQUE (ProgramNumber, LineNumber),
        CONSTRAINT CK_PromotionBackMargins_PeriodDates
            CHECK (PeriodStartDate IS NULL OR PeriodEndDate IS NULL OR PeriodStartDate <= PeriodEndDate),
        CONSTRAINT CK_PromotionBackMargins_EarlyPaymentConfig
            CHECK (
                DiscountType != 5 OR (
                    StandardPaymentDays IS NOT NULL AND
                    EarlyPaymentDays IS NOT NULL AND
                    EarlyPaymentDiscountPercentage IS NOT NULL AND
                    EarlyPaymentDays < StandardPaymentDays
                )
            )
    );

    PRINT 'PromotionBackMargins table created successfully.'
END
ELSE
BEGIN
    PRINT 'PromotionBackMargins table already exists.'
END

-- =============================================
-- 2. Create PromotionBackMarginTiers Table
-- =============================================
PRINT 'Creating PromotionBackMarginTiers table...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionBackMarginTiers')
BEGIN
    CREATE TABLE PromotionBackMarginTiers (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,
        BackMarginNumber NVARCHAR(50) NOT NULL,
        TierLevel INT NOT NULL,

        -- Threshold Values
        MinimumAmount DECIMAL(18,2) NULL, -- For sales-based discounts
        MaximumAmount DECIMAL(18,2) NULL,
        MinimumQuantity DECIMAL(18,4) NULL, -- For quantity-based discounts
        MaximumQuantity DECIMAL(18,4) NULL,

        -- Discount Configuration
        DiscountPercentage DECIMAL(5,2) NOT NULL,
        FixedDiscountAmount DECIMAL(18,2) NULL,
        MaximumDiscountAmount DECIMAL(18,2) NULL,

        -- Tier Type: PROGRESSIVE or TIERED
        TierType NVARCHAR(20) NOT NULL,

        -- Status
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),

        -- Constraints
        CONSTRAINT FK_PromotionBackMarginTiers_BackMargins
            FOREIGN KEY (BackMarginNumber) REFERENCES PromotionBackMargins(Number),
        CONSTRAINT CK_PromotionBackMarginTiers_TierType
            CHECK (TierType IN ('PROGRESSIVE', 'TIERED')),
        CONSTRAINT UQ_PromotionBackMarginTiers_BackMarginTier
            UNIQUE (BackMarginNumber, TierLevel)
    );

    PRINT 'PromotionBackMarginTiers table created successfully.'
END
ELSE
BEGIN
    PRINT 'PromotionBackMarginTiers table already exists.'
END

-- =============================================
-- 3. Create PromotionBackMarginCalculations Table
-- =============================================
PRINT 'Creating PromotionBackMarginCalculations table...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionBackMarginCalculations')
BEGIN
    CREATE TABLE PromotionBackMarginCalculations (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,
        BackMarginNumber NVARCHAR(50) NOT NULL,
        VendorCode NVARCHAR(50) NOT NULL,
        CalculationPeriod NVARCHAR(20) NOT NULL,
        PeriodStartDate DATE NOT NULL,
        PeriodEndDate DATE NOT NULL,

        -- Purchase Data
        TotalPurchaseAmount DECIMAL(18,2) NOT NULL,
        TotalPurchaseQuantity DECIMAL(18,4) NOT NULL,
        TotalInvoiceCount INT NOT NULL,

        -- Payment Data
        AveragePaymentDays DECIMAL(5,2) NULL,
        EarlyPaymentCount INT NULL,
        TotalPaymentCount INT NULL,

        -- Calculation Results
        AppliedTierLevel INT NULL,
        DiscountPercentage DECIMAL(5,2) NOT NULL,
        CalculatedDiscountAmount DECIMAL(18,2) NOT NULL,
        FinalDiscountAmount DECIMAL(18,2) NOT NULL, -- After applying maximum limits

        -- Calculation Details (JSON)
        CalculationDetails NVARCHAR(MAX) NULL,

        -- Status
        CalculationStatus NVARCHAR(20) NOT NULL, -- CALCULATED, APPROVED, PAID
        CalculatedBy NVARCHAR(100) NOT NULL,
        CalculatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        ApprovedBy NVARCHAR(100) NULL,
        ApprovedAt DATETIME2 NULL,

        -- Constraints
        CONSTRAINT FK_PromotionBackMarginCalculations_BackMargins
            FOREIGN KEY (BackMarginNumber) REFERENCES PromotionBackMargins(Number),
        CONSTRAINT CK_PromotionBackMarginCalculations_Status
            CHECK (CalculationStatus IN ('CALCULATED', 'APPROVED', 'PAID', 'CANCELLED'))
    );

    PRINT 'PromotionBackMarginCalculations table created successfully.'
END
ELSE
BEGIN
    PRINT 'PromotionBackMarginCalculations table already exists.'
END

-- =============================================
-- 4. Create PromotionBackMarginPayments Table
-- =============================================
PRINT 'Creating PromotionBackMarginPayments table...'

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionBackMarginPayments')
BEGIN
    CREATE TABLE PromotionBackMarginPayments (
        Id BIGINT IDENTITY(1,1) PRIMARY KEY,
        CalculationId BIGINT NOT NULL,
        PaymentNumber NVARCHAR(50) NOT NULL,
        VendorCode NVARCHAR(50) NOT NULL,

        -- Payment Details
        PaymentAmount DECIMAL(18,2) NOT NULL,
        PaymentMethod NVARCHAR(50) NOT NULL, -- CASH, TRANSFER, CHECK, CREDIT_NOTE
        PaymentDate DATE NOT NULL,
        PaymentReference NVARCHAR(100) NULL,

        -- Bank Details (for transfers)
        BankAccount NVARCHAR(50) NULL,
        BankName NVARCHAR(100) NULL,
        TransactionReference NVARCHAR(100) NULL,

        -- Status
        PaymentStatus NVARCHAR(20) NOT NULL, -- PENDING, COMPLETED, FAILED, CANCELLED
        ProcessedBy NVARCHAR(100) NOT NULL,
        ProcessedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),

        -- Constraints
        CONSTRAINT FK_PromotionBackMarginPayments_Calculations
            FOREIGN KEY (CalculationId) REFERENCES PromotionBackMarginCalculations(Id),
        CONSTRAINT CK_PromotionBackMarginPayments_Method
            CHECK (PaymentMethod IN ('CASH', 'TRANSFER', 'CHECK', 'CREDIT_NOTE')),
        CONSTRAINT CK_PromotionBackMarginPayments_Status
            CHECK (PaymentStatus IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED'))
    );

    PRINT 'PromotionBackMarginPayments table created successfully.'
END
ELSE
BEGIN
    PRINT 'PromotionBackMarginPayments table already exists.'
END

-- =============================================
-- 5. Create Indexes for Performance
-- =============================================
PRINT 'Creating indexes...'

-- Main table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMargins_DiscountType')
    CREATE INDEX IX_PromotionBackMargins_DiscountType ON PromotionBackMargins(DiscountType);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMargins_Status')
    CREATE INDEX IX_PromotionBackMargins_Status ON PromotionBackMargins(Status);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMargins_CalculationPeriod')
    CREATE INDEX IX_PromotionBackMargins_CalculationPeriod ON PromotionBackMargins(CalculationPeriod);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMargins_ItemNumber')
    CREATE INDEX IX_PromotionBackMargins_ItemNumber ON PromotionBackMargins(ItemNumber);

-- Tier table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMarginTiers_BackMarginNumber')
    CREATE INDEX IX_PromotionBackMarginTiers_BackMarginNumber ON PromotionBackMarginTiers(BackMarginNumber);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMarginTiers_TierLevel')
    CREATE INDEX IX_PromotionBackMarginTiers_TierLevel ON PromotionBackMarginTiers(TierLevel);

-- Calculation table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMarginCalculations_VendorCode')
    CREATE INDEX IX_PromotionBackMarginCalculations_VendorCode ON PromotionBackMarginCalculations(VendorCode);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMarginCalculations_Period')
    CREATE INDEX IX_PromotionBackMarginCalculations_Period ON PromotionBackMarginCalculations(PeriodStartDate, PeriodEndDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMarginCalculations_Status')
    CREATE INDEX IX_PromotionBackMarginCalculations_Status ON PromotionBackMarginCalculations(CalculationStatus);

-- Payment table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMarginPayments_VendorCode')
    CREATE INDEX IX_PromotionBackMarginPayments_VendorCode ON PromotionBackMarginPayments(VendorCode);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMarginPayments_PaymentDate')
    CREATE INDEX IX_PromotionBackMarginPayments_PaymentDate ON PromotionBackMarginPayments(PaymentDate);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionBackMarginPayments_Status')
    CREATE INDEX IX_PromotionBackMarginPayments_Status ON PromotionBackMarginPayments(PaymentStatus);

PRINT 'Indexes created successfully.'

-- =============================================
-- 6. Update PromotionHeaders for Back Margin Support
-- =============================================
PRINT 'Updating PromotionHeaders for Back Margin support...'

-- Add constraint to support Back Margin program type
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PromotionHeaders_ProgramType_BackMargin')
BEGIN
    ALTER TABLE PromotionHeaders DROP CONSTRAINT CK_PromotionHeaders_ProgramType;
    ALTER TABLE PromotionHeaders ADD CONSTRAINT CK_PromotionHeaders_ProgramType_BackMargin
        CHECK (ProgramType IN (1, 2)); -- 1=Front Margin, 2=Back Margin
    PRINT 'PromotionHeaders constraint updated for Back Margin support.'
END

-- =============================================
-- 7. Create Views for Reporting
-- =============================================
PRINT 'Creating reporting views...'

-- View for Back Margin summary
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.VIEWS WHERE TABLE_NAME = 'vw_BackMarginSummary')
BEGIN
    EXEC('
    CREATE VIEW vw_BackMarginSummary AS
    SELECT
        bm.Number,
        bm.ProgramNumber,
        ph.ProgramName,
        ph.VendorCode,
        v.VendorName,
        bm.DiscountType,
        CASE bm.DiscountType
            WHEN 1 THEN ''Sales Progressive''
            WHEN 2 THEN ''Sales Tiered''
            WHEN 3 THEN ''Quantity Progressive''
            WHEN 4 THEN ''Quantity Tiered''
            WHEN 5 THEN ''Early Payment''
        END as DiscountTypeName,
        bm.CalculationPeriod,
        bm.Status,
        COUNT(bt.Id) as TierCount,
        bm.CreatedAt,
        bm.CreatedBy
    FROM PromotionBackMargins bm
    INNER JOIN PromotionHeaders ph ON bm.ProgramNumber = ph.ProgramNumber
    LEFT JOIN Vendors v ON ph.VendorCode = v.VendorCode
    LEFT JOIN PromotionBackMarginTiers bt ON bm.Number = bt.BackMarginNumber AND bt.IsActive = 1
    WHERE bm.ModificationStatus = 1
    GROUP BY bm.Number, bm.ProgramNumber, ph.ProgramName, ph.VendorCode, v.VendorName,
             bm.DiscountType, bm.CalculationPeriod, bm.Status, bm.CreatedAt, bm.CreatedBy
    ');
    PRINT 'vw_BackMarginSummary view created.'
END

-- =============================================
-- 8. Verification
-- =============================================
PRINT 'Verifying table creation...'

DECLARE @TableCount INT = 0;

SELECT @TableCount = COUNT(*)
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_NAME IN ('PromotionBackMargins', 'PromotionBackMarginTiers', 'PromotionBackMarginCalculations', 'PromotionBackMarginPayments');

IF @TableCount = 4
BEGIN
    PRINT 'SUCCESS: All 4 Back Margin tables created successfully!'

    -- Display table information
    SELECT
        TABLE_NAME as 'Table Name',
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as 'Column Count'
    FROM INFORMATION_SCHEMA.TABLES t
    WHERE TABLE_NAME IN ('PromotionBackMargins', 'PromotionBackMarginTiers', 'PromotionBackMarginCalculations', 'PromotionBackMarginPayments')
    ORDER BY TABLE_NAME;
END
ELSE
BEGIN
    PRINT 'ERROR: Not all tables were created. Expected 4, found ' + CAST(@TableCount AS VARCHAR(10));
END

PRINT ''
PRINT '============================================='
PRINT 'Back Margin Tables Creation Completed!'
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
PRINT '============================================='

-- =============================================
-- 9. Create Sample Test Data (Optional)
-- =============================================
PRINT ''
PRINT 'Creating sample test data...'

-- Create test promotion header for Back Margin
IF NOT EXISTS (SELECT * FROM PromotionHeaders WHERE ProgramNumber = 'BM2025001')
BEGIN
    INSERT INTO PromotionHeaders (
        ProgramNumber, ProgramName, VendorCode, ProgramType,
        StartDate, EndDate, Status, CreatedBy, CreatedAt, ModificationStatus
    ) VALUES (
        'BM2025001', 'Back Margin Test Program 2025', 'VENDOR001', 2,
        '2025-01-01', '2025-12-31', 2, 'SYSTEM', GETUTCDATE(), 1
    );
    PRINT 'Test promotion header created: BM2025001'
END

-- Create sample Back Margin configurations
IF NOT EXISTS (SELECT * FROM PromotionBackMargins WHERE Number = 'BM202501010001')
BEGIN
    -- Sales Progressive Discount
    INSERT INTO PromotionBackMargins (
        Number, ProgramNumber, LineNumber, DiscountType, CalculationPeriod,
        Status, CreatedBy, CreatedAt, ModificationStatus
    ) VALUES (
        'BM202501010001', 'BM2025001', 1, 1, 'QUARTERLY',
        2, 'SYSTEM', GETUTCDATE(), 1
    );

    -- Add tiers for Sales Progressive
    INSERT INTO PromotionBackMarginTiers (
        BackMarginNumber, TierLevel, MinimumAmount, MaximumAmount,
        DiscountPercentage, TierType
    ) VALUES
    ('BM202501010001', 1, 0, 10000000, 1.0, 'PROGRESSIVE'),
    ('BM202501010001', 2, 10000000, 50000000, 2.0, 'PROGRESSIVE'),
    ('BM202501010001', 3, 50000000, NULL, 3.0, 'PROGRESSIVE');

    PRINT 'Sample Sales Progressive Back Margin created'
END

IF NOT EXISTS (SELECT * FROM PromotionBackMargins WHERE Number = 'BM202501010002')
BEGIN
    -- Early Payment Discount
    INSERT INTO PromotionBackMargins (
        Number, ProgramNumber, LineNumber, DiscountType, CalculationPeriod,
        StandardPaymentDays, EarlyPaymentDays, EarlyPaymentDiscountPercentage,
        SupportedPaymentMethods, Status, CreatedBy, CreatedAt, ModificationStatus
    ) VALUES (
        'BM202501010002', 'BM2025001', 2, 5, 'MONTHLY',
        30, 15, 2.0, '["CASH", "TRANSFER"]',
        2, 'SYSTEM', GETUTCDATE(), 1
    );

    PRINT 'Sample Early Payment Back Margin created'
END

PRINT 'Sample test data creation completed.'
