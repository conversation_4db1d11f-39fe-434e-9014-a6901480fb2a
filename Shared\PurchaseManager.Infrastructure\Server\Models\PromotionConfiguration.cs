namespace PurchaseManager.Infrastructure.Server.Models;

/// <summary>
///     Configuration settings for the Front Margin promotion system
/// </summary>
public class PromotionConfiguration
{
    public const string ConfigurationSection = "PurchaseManager:Promotions";

    public FrontMarginConfiguration FrontMargin { get; set; } = new FrontMarginConfiguration();
}
/// <summary>
///     Front Margin promotion specific configuration
/// </summary>
public class FrontMarginConfiguration
{
    /// <summary>
    ///     Whether Front Margin promotions are enabled system-wide
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    ///     Whether to automatically apply promotions when creating PO lines
    ///     Note: This is now deprecated in favor of OnlyApplyToReleasedPO
    /// </summary>
    public bool AutoApplyOnPOCreation { get; set; } = false;

    /// <summary>
    ///     Only apply promotions to POs with status = 50 (released/sent)
    ///     When true, promotions will only be calculated after PO is approved and sent
    /// </summary>
    public bool OnlyApplyToReleasedPO { get; set; } = true;

    /// <summary>
    ///     Maximum number of gift items allowed per PO line
    /// </summary>
    public int MaxGiftItemsPerLine { get; set; } = 10;

    /// <summary>
    ///     Whether to create separate gift lines for gift items
    /// </summary>
    public bool CreateGiftLines { get; set; } = true;
}
