using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Promotion Rewards - K<PERSON>yến mãi/Phần thưởng
/// </summary>
[Table("PromotionRewards")]
public class PromotionReward : EntityBase
{

    [Required]
    [StringLength(50)]
    public string PromotionNumber { get; set; } = string.Empty;

    /// <summary>
    /// Loại khuyến mãi:
    /// FrontMargin: 1=ProductDiscount, 2=Gift, 3=Voucher
    /// BackMargin: 4=Cash, 5=Transfer, 6=DebtOffset, 7=Goods
    /// </summary>
    [Required]
    public int RewardType { get; set; }

    /// <summary>
    /// ItemNumber điều kiện (sản phẩm cần mua)
    /// </summary>
    [StringLength(50)]
    public string? ConditionItemNumber { get; set; }

    /// <summary>
    /// ItemNumber được khu<PERSON>ến mãi/tặng
    /// </summary>
    [StringLength(50)]
    public string? RewardItemNumber { get; set; }

    /// <summary>
    /// ItemName - Always lookup from Item table
    /// </summary>
    [StringLength(500)]
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Unit - Always lookup from Item table
    /// </summary>
    [StringLength(20)]
    public string UnitOfMeasure { get; set; } = string.Empty;

    /// <summary>
    /// Số lượng tặng/khuyến mãi
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? Quantity { get; set; }

    /// <summary>
    /// % Chiết khấu
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal? DiscountPercent { get; set; }

    /// <summary>
    /// Giá trị chiết khấu cố định
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? DiscountAmount { get; set; }

    /// <summary>
    /// Giá trị voucher (nếu là voucher)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? VoucherValue { get; set; }

    /// <summary>
    /// Mã voucher (nếu là voucher)
    /// </summary>
    [StringLength(100)]
    public string? VoucherCode { get; set; }

    /// <summary>
    /// Loại tặng phẩm: 1=TangPham, 2=ChietKhau, 3=Voucher
    /// </summary>
    public int? GiftType { get; set; }

    // BACK MARGIN SPECIFIC FIELDS
    /// <summary>
    /// Discount calculation type: 1=Progressive, 2=Stepwise (BackMargin only)
    /// </summary>
    public int? CalculationType { get; set; }

    /// <summary>
    /// Discount value type: 1=Percentage, 2=FixedAmount (BackMargin only)
    /// </summary>
    public int? DiscountValueType { get; set; }

    /// <summary>
    /// Tier level for stepwise calculation (BackMargin only)
    /// </summary>
    public int? TierLevel { get; set; }

    /// <summary>
    /// Maximum reward amount per period (BackMargin only)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaxRewardAmount { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    /// <summary>
    /// Điều kiện chi tiết (text mô tả)
    /// </summary>
    [StringLength(2000)]
    public string? ConditionDescription { get; set; }

    // Navigation properties
    [ForeignKey("PromotionNumber")]
    public virtual PromotionHeader PromotionHeader { get; set; } = null!;
}

/// <summary>
/// Reward Type Enum
/// </summary>
public enum PromotionRewardType
{
    ProductDiscount = 1, // Chiết khấu theo sản phẩm
    Gift = 2, // Tặng hàng
    Voucher = 3 // Voucher
}

/// <summary>
/// Gift Type Enum
/// </summary>
public enum PromotionGiftType
{
    TangPham = 1, // Tặng phẩm
    ChietKhau = 2, // Chiết khấu
    Voucher = 3 // Voucher
}
