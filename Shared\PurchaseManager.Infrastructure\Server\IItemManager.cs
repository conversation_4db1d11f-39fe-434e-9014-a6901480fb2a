using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Models.Item;

namespace PurchaseManager.Infrastructure.Server;

public interface IItemManager
{
    // Item
    IQueryable<DetailItemDto> GetAllItems(ItemFilter filter, string queryString);
    Task<ApiResponse> GetItem(string itemNumber);
    Task<ApiResponse> InsertItem(CreateItemDto item);
    public IQueryable<DetailVendorItemDto> GetAllVendorItems(VendorItemFilter filter);
    public Task<ApiResponse> CreateVendorItem(CreateVendorItemDto item);
    public Task<ApiResponse> UpdateVendorItem(UpdateVendorItemDto item);
    public Task<ApiResponse<DetailVendorItemDto>> ToggleVendorItemStatus(int rowId); // block => unblock | unblock => block
    Task<ApiResponse> UpdateItem(String number, UpdateItemDto item);
    Task<ApiResponse> SearchItemByNumberOrName(String number, String name, String queryString, CancellationToken cancellationToken = default);

    // Unit
    Task<ApiResponse> InsertUnitOfMeasure(List<CreateUnitOfMeasureDto> unitOfMeasure);
    Task<ApiResponse> UpdateUnitOfMeasure(string code, UpdateUnitOfMeasureDto unitOfMeasure);
    public IQueryable<UnitOfMeasureDto> GetAllUnitOfMeasure(UnitFilter filter, string queryString);


    // Price
    Task<ApiResponse> InsertSalesPrice(List<CreateSalesPriceDto> salesPrice);
    Task<ApiResponse> UpdateSalesPrice(String itemNumber, DateTime startDate, String unitOfMeasure, UpdateSalesPriceDto salesPrice);

    #region Item Unit of measure
    Task<ApiResponse> InsertItemUnitOfMeasure(List<CreateItemUnitOfMeasureDto> itemUnitOfMeasure);
    Task<ApiResponse> UpdateItemUnitOfMeasure(String itemNumber, String unitOfMeasureCode, UpdateItemUnitOfMeasureDto itemUnitOfMeasure);
    Task<ApiResponse> GetItemUnitOfMeasure(string itemNumber);
    #endregion
    
    Task<ApiResponse> InsertOrUpdateColors(List<InsertOrUpdateItemColorsDto> colors);
}
