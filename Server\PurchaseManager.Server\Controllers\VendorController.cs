﻿using Breeze.AspNetCore;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Localizer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Models;
using static Microsoft.AspNetCore.Http.StatusCodes;
using PurchaseManager.Infrastructure.AuthorizationDefinitions;
using GetVendorDto = PurchaseManager.Shared.Dto.Vendor.GetVendorDto;


namespace PurchaseManager.Server.Controllers;

// [OpenApiIgnore]
[Authorize]
[SecurityHeaders]
[Route("api/[controller]")]
[ApiController]
public class VendorController : ControllerBase
{
    private readonly IVendorManager _vendorManager;

    private readonly ApiResponse _invalidData;

    private readonly IStringLocalizer<Global> L;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public VendorController(IVendorManager vendorManager, IStringLocalizer<Global> l, IHttpContextAccessor httpContextAccessor)
    {
        _vendorManager = vendorManager;
        L = l;
        _invalidData = new ApiResponse(Status400BadRequest, L["InvalidData"]);
        _httpContextAccessor = httpContextAccessor;

    }

    [AllowAnonymous]
    [HttpGet("Metadata")]
    public string Metadata()
        => _vendorManager.Metadata();

    [HttpPost("BuildViewModel")]
    [AllowAnonymous]
    public async Task<ApiResponse> BuildViewModel(string number)
        => await _vendorManager.BuildViewModel(number);
    [HttpPost("toggle-status")]
    [Authorize(Policy = Policies.IsAdmin)]
    public async Task<ApiResponse> ToggleVendorStatus([FromBody] string number) => await _vendorManager.ToggleVendorStatus(number);

    [BreezeQueryFilter]
    [HttpGet("Vendors")]
    public IQueryable<Vendor> ListVendor([FromQuery] VendorFilter filter)
        => _vendorManager.GetVendors(filter, HttpContext.Request.QueryString.Value);

    [HttpGet("Search")]
    public async Task<ApiResponse> Search(string number, string name, CancellationToken cancellationToken = default)
    {
        var queryString = HttpContext.Request.QueryString.ToString();
        return await _vendorManager.SearchAutocomplete(number, name, queryString, cancellationToken);
    }

    [HttpGet("SingleVendor")]
    public async Task<ApiResponse<GetVendorDto>> SingleVendor(string number)
        => await _vendorManager.GetVendor(number);

    [HttpPut]
    [Authorize]
    public async Task<ApiResponse> UpdateVendor(Shared.Models.GetVendorDto vendorViewModel)
        => ModelState.IsValid ? await _vendorManager.UpdateVendor(vendorViewModel) : _invalidData;

    [HttpPost]
    [Authorize]
    public async Task<ApiResponse> CreateVendor(Shared.Models.GetVendorDto vendorViewModel)
        => ModelState.IsValid ? await _vendorManager.CreateVendor(vendorViewModel) : _invalidData;
}
