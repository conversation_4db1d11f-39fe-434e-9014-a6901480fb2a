using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Server.Services.Promotions.Interface;
namespace PurchaseManager.Server.Controllers;

/// <summary>
///     Controller for Front Margin performance monitoring and optimization
/// </summary>
[ApiController]
[Route("api/front-margin-performance")]
public class FrontMarginPerformanceController : ControllerBase
{
    private readonly IFrontMarginPerformanceService _performanceService;
    private readonly IFrontMarginCacheService _cacheService;

    public FrontMarginPerformanceController(IFrontMarginPerformanceService performanceService, IFrontMarginCacheService cacheService)
    {
        _performanceService = performanceService;
        _cacheService = cacheService;
    }

    /// <summary>
    ///     Monitor calculation performance for specific vendor
    /// </summary>
    [HttpPost("monitor-calculation")]
    public async Task<ApiResponse> MonitorCalculationPerformance([FromBody] PerformanceMonitorRequest request)
    {
        var metrics = await _performanceService.MonitorCalculationPerformanceAsync(request.VendorCode, request.ItemCount);

        return ApiResponse.S200("Performance monitoring completed", new
        {
            Metrics = metrics,
            Performance = new
            {
                IsGood = metrics.AverageCalculationTimePerItem < 10,// Less than 10ms per item is good
                Rating = GetPerformanceRating(metrics.AverageCalculationTimePerItem),
                Recommendations = GetPerformanceRecommendations(metrics)
            }
        });
    }

    /// <summary>
    ///     Get comprehensive performance statistics
    /// </summary>
    [HttpGet("stats")]
    public async Task<ApiResponse> GetPerformanceStats()
    {
        var stats = await _performanceService.GetPerformanceStatsAsync();

        return ApiResponse.S200("Performance statistics retrieved", new
        {
            Stats = stats,
            Summary = new
            {
                OverallHealth = GetOverallHealthStatus(stats),
                CacheEfficiency = GetCacheEfficiencyRating(stats.CacheStats.HitRatio),
                DatabaseHealth = GetDatabaseHealthStatus(stats.DatabaseStats),
                SystemHealth = GetSystemHealthStatus(stats.SystemStats)
            }
        });
    }

    /// <summary>
    ///     Optimize database performance
    /// </summary>
    [HttpPost("optimize-database")]
    public async Task<ApiResponse> OptimizeDatabase()
    {
        try
        {
            await _performanceService.OptimizeDatabaseAsync();
            return ApiResponse.S200("Database optimization completed successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse.S500($"Database optimization failed: {ex.Message}");
        }
    }

    /// <summary>
    ///     Get slow query analysis
    /// </summary>
    [HttpGet("slow-queries")]
    public async Task<ApiResponse> GetSlowQueries()
    {
        var slowQueries = await _performanceService.GetSlowQueriesAsync();

        return ApiResponse.S200("Slow query analysis retrieved", new
        {
            SlowQueries = slowQueries,
            Summary = new
            {
                TotalSlowQueries = slowQueries.Count,
                CriticalQueries = slowQueries.Count(q => q.AverageExecutionTime > 1000),// > 1 second
                NeedsAttention = slowQueries.Count(q => q.AverageExecutionTime > 500)// > 500ms
            }
        });
    }

    /// <summary>
    ///     Run benchmark tests on calculation engine
    /// </summary>
    [HttpPost("benchmark")]
    public async Task<ApiResponse> RunBenchmark()
    {
        var benchmarkResult = await _performanceService.BenchmarkCalculationEngineAsync();

        return ApiResponse.S200("Benchmark completed", new
        {
            Result = benchmarkResult,
            Analysis = new
            {
                OverallPerformance = GetBenchmarkRating(benchmarkResult),
                FastestScenario = benchmarkResult.ScenarioResults.OrderBy(s => s.AverageExecutionTime)
                    .FirstOrDefault()
                    ?.ScenarioName,
                SlowestScenario = benchmarkResult.ScenarioResults.OrderByDescending(s => s.AverageExecutionTime)
                    .FirstOrDefault()
                    ?.ScenarioName,
                Recommendations = GetBenchmarkRecommendations(benchmarkResult)
            }
        });
    }

    /// <summary>
    ///     Clear all caches
    /// </summary>
    [HttpPost("clear-cache")]
    public ApiResponse ClearCache()
    {
        _cacheService.ClearAllCache();
        return ApiResponse.S200("All caches cleared successfully");
    }

    /// <summary>
    ///     Clear cache for specific vendor
    /// </summary>
    [HttpPost("clear-vendor-cache/{vendorCode}")]
    public ApiResponse ClearVendorCache(string vendorCode)
    {
        _cacheService.ClearVendorCache(vendorCode);
        return ApiResponse.S200($"Cache cleared for vendor {vendorCode}");
    }

    /// <summary>
    ///     Warm up cache for vendors
    /// </summary>
    [HttpPost("warm-up-cache")]
    public async Task<ApiResponse> WarmUpCache([FromBody] CacheWarmUpRequest request)
    {
        await _cacheService.WarmUpCacheAsync(request.VendorCodes);
        return ApiResponse.S200($"Cache warmed up for {request.VendorCodes.Count} vendors");
    }

    /// <summary>
    ///     Get cache statistics
    /// </summary>
    [HttpGet("cache-stats")]
    public ApiResponse GetCacheStats()
    {
        var stats = _cacheService.GetCacheStats();

        return ApiResponse.S200("Cache statistics retrieved", new
        {
            Stats = stats,
            Analysis = new
            {
                Efficiency = GetCacheEfficiencyRating(stats.HitRatio),
                Status = stats.HitRatio > 80 ? "Excellent" : stats.HitRatio > 60 ? "Good" : "Needs Improvement",
                Recommendations = GetCacheRecommendations(stats)
            }
        });
    }

    /// <summary>
    ///     Get performance dashboard data
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ApiResponse> GetPerformanceDashboard()
    {
        var stats = await _performanceService.GetPerformanceStatsAsync();
        var cacheStats = _cacheService.GetCacheStats();

        var dashboard = new
        {
            Overview = new
            {
                OverallHealth = GetOverallHealthStatus(stats),
                ActivePromotions = stats.DatabaseStats.ActiveFrontMargins,
                CacheHitRatio = cacheStats.HitRatio,
                stats.SystemStats.MemoryUsage
            },
            Performance = new
            {
                DatabaseHealth = GetDatabaseHealthStatus(stats.DatabaseStats),
                CacheEfficiency = GetCacheEfficiencyRating(cacheStats.HitRatio),
                SystemHealth = GetSystemHealthStatus(stats.SystemStats),
                QueryPerformance = GetQueryPerformanceRating(stats.QueryStats.AverageQueryTime)
            },
            Metrics = new
            {
                cacheStats.TotalRequests,
                cacheStats.CacheHits,
                cacheStats.CacheMisses,
                stats.QueryStats.AverageQueryTime,
                stats.QueryStats.SlowQueryCount
            },
            Recommendations = GetDashboardRecommendations(stats, cacheStats)
        };

        return ApiResponse.S200("Performance dashboard data retrieved", dashboard);
    }

    #region Private Helper Methods
    private string GetPerformanceRating(double avgTimePerItem)
    {
        return avgTimePerItem switch
        {
            < 5 => "Excellent",
            < 10 => "Good",
            < 20 => "Fair",
            _ => "Poor"
        };
    }

    private List<string> GetPerformanceRecommendations(PerformanceMetrics metrics)
    {
        var recommendations = new List<string>();

        if (metrics.AverageCalculationTimePerItem > 20)
        {
            recommendations.Add("Consider optimizing calculation algorithms");
        }

        if (metrics.CacheLoadTime > 100)
        {
            recommendations.Add("Cache performance needs improvement");
        }

        if (metrics.PromotionCount > 1000)
        {
            recommendations.Add("Large number of promotions may impact performance");
        }

        return recommendations;
    }

    private string GetOverallHealthStatus(FrontMarginPerformanceStats stats)
    {
        var scores = new[]
        {
            GetDatabaseHealthScore(stats.DatabaseStats),
            GetCacheHealthScore(stats.CacheStats.HitRatio),
            GetSystemHealthScore(stats.SystemStats)
        };

        var avgScore = scores.Average();

        return avgScore switch
        {
            >= 80 => "Excellent",
            >= 60 => "Good",
            >= 40 => "Fair",
            _ => "Poor"
        };
    }

    private string GetCacheEfficiencyRating(double hitRatio)
    {
        return hitRatio switch
        {
            >= 90 => "Excellent",
            >= 80 => "Good",
            >= 60 => "Fair",
            _ => "Poor"
        };
    }

    private string GetDatabaseHealthStatus(DatabaseStats stats)
    {
        var activeRatio = stats.TotalFrontMargins > 0 ? (double)stats.ActiveFrontMargins / stats.TotalFrontMargins : 0;

        return activeRatio switch
        {
            >= 0.8 => "Excellent",
            >= 0.6 => "Good",
            >= 0.4 => "Fair",
            _ => "Poor"
        };
    }

    private string GetSystemHealthStatus(SystemStats stats)
    {
        return stats.MemoryUsage switch
        {
            < 500 => "Excellent",
            < 1000 => "Good",
            < 2000 => "Fair",
            _ => "Poor"
        };
    }

    private string GetBenchmarkRating(BenchmarkResult result)
    {
        if (result.ScenarioResults.Count == 0)
        {
            return "No Data";
        }

        var avgTime = result.ScenarioResults.Average(s => s.AverageExecutionTime);

        return avgTime switch
        {
            < 100 => "Excellent",
            < 500 => "Good",
            < 1000 => "Fair",
            _ => "Poor"
        };
    }

    private List<string> GetBenchmarkRecommendations(BenchmarkResult result)
    {
        var recommendations = new List<string>();

        if (result.ScenarioResults.Any(s => s.AverageExecutionTime > 1000))
        {
            recommendations.Add("Consider optimizing for large orders");
        }

        if (result.ScenarioResults.Count > 0)
        {
            var variance = result.ScenarioResults.Max(s => s.MaxExecutionTime) - result.ScenarioResults.Min(s => s.MinExecutionTime);
            if (variance > 500)
            {
                recommendations.Add("High variance in execution times - investigate inconsistencies");
            }
        }

        return recommendations;
    }

    private List<string> GetCacheRecommendations(FrontMarginCacheStats stats)
    {
        var recommendations = new List<string>();

        if (stats.HitRatio < 80)
        {
            recommendations.Add("Consider increasing cache expiration time");
        }

        if (stats.CacheMisses > stats.CacheHits)
        {
            recommendations.Add("Cache strategy needs optimization");
        }

        return recommendations;
    }

    private List<string> GetDashboardRecommendations(FrontMarginPerformanceStats stats, FrontMarginCacheStats cacheStats)
    {
        var recommendations = new List<string>();

        if (cacheStats.HitRatio < 70)
        {
            recommendations.Add("Improve cache hit ratio by optimizing cache strategy");
        }

        if (stats.SystemStats.MemoryUsage > 1000)
        {
            recommendations.Add("Monitor memory usage - consider optimization");
        }

        if (stats.QueryStats.SlowQueryCount > 5)
        {
            recommendations.Add("Address slow queries to improve performance");
        }

        return recommendations;
    }

    private int GetDatabaseHealthScore(DatabaseStats stats)
    {
        var activeRatio = stats.TotalFrontMargins > 0 ? (double)stats.ActiveFrontMargins / stats.TotalFrontMargins : 0;
        return (int)(activeRatio * 100);
    }

    private int GetCacheHealthScore(double hitRatio)
    {
        return (int)hitRatio;
    }

    private int GetSystemHealthScore(SystemStats stats)
    {
        return stats.MemoryUsage switch
        {
            < 500 => 100,
            < 1000 => 80,
            < 2000 => 60,
            _ => 40
        };
    }

    private string GetQueryPerformanceRating(double avgQueryTime)
    {
        return avgQueryTime switch
        {
            < 50 => "Excellent",
            < 100 => "Good",
            < 200 => "Fair",
            _ => "Poor"
        };
    }
    #endregion
}
#region Request Models
public class PerformanceMonitorRequest
{
    public string VendorCode { get; set; } = string.Empty;
    public int ItemCount { get; set; } = 100;
}
public class CacheWarmUpRequest
{
    public List<string> VendorCodes { get; set; } = new List<string>();
}
#endregion
