﻿using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Managers.Validations;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Server.Managers;

public partial class PromotionManager
{
    public async Task<ApiResponse> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        try
        {
            const string business = "PROMOTIONFRONTMARGIN";
            const string branch = "AL";

            // Validate the PromotionHeader exists
            var promotionHeader = await _context.PromotionHeaders
                .FirstOrDefaultAsync(p => p.Number == createDto.ProgramNumber);

            if (promotionHeader == null)
            {
                return ApiResponse.S404("Promotion header not found");
            }

            // Validate promotion header using helper method
            var validationError = PromotionValidation.ValidatePromotionHeaderForEditing(promotionHeader, requireFrontMarginType: true);
            if (validationError != null)
            {
                return validationError;
            }

            // Check if the same item already exists in this promotion
            var existingItem = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(x => x.ProgramNumber == createDto.ProgramNumber
                                          && x.ItemNumber == createDto.ItemNumber
                                          && x.UnitOfMeasure == createDto.UnitOfMeasure);

            if (existingItem != null)
            {
                return ApiResponse.S400($"Item {createDto.ItemNumber}: {createDto.UnitOfMeasure} already exists in this promotion");
            }

            // Get next line number
            var lineNumber = await _context.PromotionFrontMargins
                .OrderByDescending(x => x.LineNumber)
                .Where(x => x.ProgramNumber == createDto.ProgramNumber)
                .Select(x => x.LineNumber)
                .FirstOrDefaultAsync();

            // Map the DTO to the entity
            var promotionFrontMargin = _mapper.Map<PromotionFrontMargin>(createDto);
            promotionFrontMargin.Number = await _adminManager.CreateNumberSeries(business, branch);
            promotionFrontMargin.LineNumber = lineNumber + 1;
            // Set default status to Active
            promotionFrontMargin.Status = 1;
            promotionFrontMargin.CreatedAt = DateTime.Now;
            promotionFrontMargin.CreatedBy = _adminManager.GetUserLogin();

            _context.PromotionFrontMargins.Add(promotionFrontMargin);
            await _context.SaveChangesAsync();

            return ApiResponse.S200("Promotion Front Margin created successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }
    public async Task<ApiResponse> GetPromotionFrontMarginByNumberAsync(string number)
    {
        try
        {
            var promotionFrontMargin = await _context.PromotionFrontMargins
                .Include(x => x.PromotionHeader)
                .FirstOrDefaultAsync(x => x.Number == number);

            if (promotionFrontMargin == null)
            {
                return ApiResponse.S404("Promotion Front Margin not found");
            }

            var result = _mapper.Map<GetPromotionFrontMarginDto>(promotionFrontMargin);
            return ApiResponse.S200("Promotion Front Margin retrieved successfully", result);
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException()
                .Message);
        }
    }

    public async Task<ApiResponse> UpdatePromotionFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto)
    {
        try
        {
            var promotionFrontMargin = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(x => x.Number == number);

            if (promotionFrontMargin == null)
            {
                return ApiResponse.S404("Promotion Front Margin not found");
            }

            _mapper.Map(updateDto, promotionFrontMargin);

            await _context.SaveChangesAsync();
            return ApiResponse.S200();

        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException()
                .Message);
        }
    }
    public async Task<ApiResponse> IsDocumentLockedByAnotherUserFrontMarginAsync(string number)
    {
        try
        {
            var promotionFrontMargin = await _context.PromotionFrontMargins
                .FirstOrDefaultAsync(x => x.Number == number);

            if (promotionFrontMargin == null)
            {
                return ApiResponse.S404("Promotion Front Margin not found");
            }

            return await IsDocumentLockedByAnotherUserAsync(promotionFrontMargin.ProgramNumber);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }
    public async Task<ApiResponse> DeletePromotionFrontMarginAsync(List<string> numbers)
    {
        try
        {
            var promotionFrontMargins = _context.PromotionFrontMargins
                .Where(x => numbers.Contains(x.Number))
                .ToList();

            if (promotionFrontMargins.Count == 0)
            {
                return ApiResponse.S404("No Promotion Front Margins found for the provided numbers");
            }

            _context.PromotionFrontMargins.RemoveRange(promotionFrontMargins);
            await _context.SaveChangesAsync();

            return ApiResponse.S200("Promotion Front Margins deleted successfully");
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }
}
