using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using MudBlazor;
using ObjectCloner.Extensions;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Theme.Material.Demo.Shared.Components.Promotions;
using PurchaseManager.UI.Base.Shared.Components;

namespace PurchaseManager.Theme.Material.Demo.Pages.Promotions;

public partial class FrontMarginPage : BaseComponent, IAsyncDisposable
{
    [Parameter]
    public string PromotionNumber { get; set; }

    #region Api Clients
    [Inject]
    protected IItemApiClient ItemApiClient { get; set; }

    [Inject]
    protected IPromotionApiClient PromotionApiClient { get; set; }
    #endregion

    [CascadingParameter]
    [Inject]
    public AuthenticationStateProvider AuthStateProvider { get; set; }

    [Inject]
    protected IViewNotifier ViewNotifier { get; set; }

    [Inject]
    protected HttpClient HttpClient { get; set; }

    [Inject]
    protected IDialogService DialogService { get; set; }

    // Main data properties
    protected GetPromotionHeaderDto PromotionHeader { get; set; } = new GetPromotionHeaderDto();

    protected MudTable<GetPromotionFrontMarginDto> PromotionFrontMarginTable { get; set; }
    protected PromotionToolBar ToolBarRef { get; set; }
    // User and state properties
    protected string ErrorMessage { get; set; }
    protected List<DetailItemUnitOfMeasureDto> LsDetailItemUnitOfMeasureDtoEditing { get; set; } = [];
    protected bool IsLoad { get; set; }
    protected bool IsShowAddFrontMarginDialog { get; set; }
    protected bool IsShowEditFrontMarginDialog { get; set; }
    protected DetailItemDto SelectedItem { get; set; } = new DetailItemDto();
    protected GetPromotionFrontMarginDto CurrentLine { get; set; } = new GetPromotionFrontMarginDto();
    protected MudForm EditPromotionFrontMarginFormRef { get; set; }

    protected bool IsEdit { get; set; }

    protected override async Task OnInitializedAsync()
    {
        IsLoad = true;
        await GetPromotionHeaderAsync(PromotionNumber);
        await GetUserRolesAsync();
        await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        IsLoad = false;
        UpdateToolbarState();
        await base.OnInitializedAsync();
    }

    protected void OnStartDateChange(DateTime? dueDate)
    {
        if (dueDate is not null)
        {
            PromotionHeader.StartDate = dueDate.Value;
        }
    }

    protected async Task SaveOrEditPromotion()
    {
        if (IsEdit)
        {
            await SavePromotionAsync();
            await HandleOpenMode(false);
        }
        else
        {
            await HandleOpenMode();
            StateHasChanged();
        }
        UpdateToolbarState();
    }

    protected async Task PurchaserApprove()
    {
        try
        {
            var response = await HttpClient.PostAsync($"api/promotions/front-margin/{PromotionNumber}/approve/purchaser", null);
            if (response.IsSuccessStatusCode)
            {
                ViewNotifier.Show("Purchaser approval completed", ViewNotifierType.Success);
                await GetPromotionHeaderAsync(PromotionNumber);
                UpdateToolbarState();
                StateHasChanged();
            }
            else
            {
                ViewNotifier.Show("Failed to approve", ViewNotifierType.Error);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }

    protected Task PrintPromotion()
    {
        try
        {
            viewNotifier.Show("Print functionality not implemented yet", ViewNotifierType.Info);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }

    protected Task ExportPromotion()
    {
        try
        {
            viewNotifier.Show("Export functionality not implemented yet", ViewNotifierType.Info);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }

    protected Task SendEmail()
    {
        try
        {
            viewNotifier.Show("Email functionality not implemented yet", ViewNotifierType.Info);
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }

    protected void UpdateToolbarState()
    {
        var hasData = PromotionHeader.FrontMargins?.Count > 0;
        var status = GetCurrentStatus();
        ToolBarRef?.SetFrontMarginState(IsEdit, status, hasData);
    }

    protected int GetCurrentStatus()
    {
        // Map from PromotionHeader.Status to dropdown values
        // Assuming PromotionHeader has a Status field
        return PromotionHeader.Status switch
        {
            1 => 1, // Draft
            2 => 2, // Active
            3 => 3, // Inactive
            4 => 4, // Expired
            _ => 1  // Default to Draft
        };
    }

    protected async Task UpdatePromotionStatus(int newStatus)
    {
        try
        {
            // Update the promotion status via API
            var response = await HttpClient.PutAsJsonAsync($"api/promotions/{PromotionNumber}/status", new { Status = newStatus });

            if (response.IsSuccessStatusCode)
            {
                PromotionHeader.Status = newStatus;
                ViewNotifier.Show($"Promotion status updated to {GetStatusText(newStatus)}", ViewNotifierType.Success);
                UpdateToolbarState();
                StateHasChanged();
            }
            else
            {
                ViewNotifier.Show("Failed to update promotion status", ViewNotifierType.Error);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }

    private string GetStatusText(int status)
    {
        return status switch
        {
            1 => "Draft",
            2 => "Active",
            3 => "Inactive",
            4 => "Expired",
            _ => "Unknown"
        };
    }

    // Condition management methods
    protected void AddCondition()
    {
        CurrentLine = new GetPromotionFrontMarginDto();
        IsShowAddFrontMarginDialog = true;
    }

    protected async Task DeletePromotionFrontMarginsAsync()
    {
        if (PromotionFrontMarginTable.SelectedItems != null)
        {
            var confirm = await DialogService.ShowMessageBox(
            "Confirm Deletion",
            "Are you sure you want to delete the selected front margins?",
            "Delete",
            cancelText: "Cancel");

            if (confirm.GetValueOrDefault())
            {
                try
                {
                    // Get list of numbers from selected front margins
                    var numbers = PromotionFrontMarginTable.SelectedItems.Select(x => x.Number)
                        .ToList();

                    var response = await PromotionApiClient.DeletePromotionFrontMarginAsync(numbers);

                    if (response.IsSuccessStatusCode)
                    {
                        ViewNotifier.Show($"Successfully deleted {numbers.Count} front margin(s)",
                        ViewNotifierType.Success);
                        PromotionFrontMarginTable.SelectedItems.Clear();
                        await GetDetailPromotionAsync(PromotionNumber);
                    }
                    else
                    {
                        ViewNotifier.Show(response.Message, ViewNotifierType.Error);
                    }
                }
                catch (Exception ex)
                {
                    ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
                }
            }
        }
    }

    protected Task OnItemSelectedInAutoComplete(DetailItemDto dto)
    {
        try
        {
            SelectedItem = dto;
            CurrentLine.ItemName = dto.Name;
            CurrentLine.ItemNumber = dto.Number;
            LsDetailItemUnitOfMeasureDtoEditing = dto.ItemUnitOfMeasures;
            if (dto.Status < 2)
            {
                ViewNotifier.Show("Item is editing.", ViewNotifierType.Warning);
            }
            if (dto.Blocked == 1)
            {
                ViewNotifier.Show("Item is blocked", ViewNotifierType.Warning);
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Get Last Unit Cost has error: " + e.GetBaseException()
                .Message, ViewNotifierType.Error);
        }
        return Task.CompletedTask;
    }

    protected async Task OnEditPOLine(GetPromotionFrontMarginDto dto)
    {
        try
        {
            CurrentLine = new GetPromotionFrontMarginDto();
            if (!IsEdit)
            {
                return;
            }
            var itemNumber = dto.ItemNumber;

            // Get item unit of measure by itemNumber
            var apiResponse = await ItemApiClient.GetItemUnitOfMeasure(itemNumber);
            if (apiResponse.IsSuccessStatusCode)
            {
                LsDetailItemUnitOfMeasureDtoEditing = apiResponse.Result;
            }
            CurrentLine = dto.DeepClone();
            IsShowEditFrontMarginDialog = true;
            IsShowAddFrontMarginDialog = false;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    protected async Task HandleOpenMode(bool open = true)
    {
        if (open)
        {
            var openResult = await PromotionApiClient.OpenDocumentAsync(PromotionNumber);
            if (!openResult.IsSuccessStatusCode)
            {
                ViewNotifier.Show(openResult.Message, ViewNotifierType.Error);
                return;
            }

            IsEdit = true;
            PromotionFrontMarginTable.SelectedItems?.Clear();
        }
        else
        {
            var closeResult = await PromotionApiClient.CloseDocumentAsync(PromotionNumber);
            if (!closeResult.IsSuccessStatusCode)
            {
                ViewNotifier.Show(closeResult.Message, ViewNotifierType.Error);
                return;
            }

            IsEdit = false;
        }
    }

    // Item search for conditions and rewards
    protected async Task<IEnumerable<DetailItemDto>> ItemSearch(string value, CancellationToken cancellationToken)
    {
        try
        {
            IEnumerable<DetailItemDto> result = new List<DetailItemDto>();

            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrEmpty(value))
            {
                value = string.Empty;
            }
            var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<List<DetailItemDto>>>(
            $"api/data/SearchItem?number={value.Trim()}", cancellationToken);

            if (!apiResponse.IsSuccessStatusCode)
            {
                return result;
            }

            result = apiResponse.Result;

            return result;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return new List<DetailItemDto>();
        }
    }
    protected void OnCloseEditPOLineDialog()
    {
        IsShowAddFrontMarginDialog = false;
        IsShowEditFrontMarginDialog = false;
    }

    protected async Task OnSavePOLineAsync()
    {
        try
        {
            if (SelectedItem.Status < 2)
            {
                ViewNotifier.Show("Item is editing.", ViewNotifierType.Warning);
                return;
            }
            if (SelectedItem.Blocked == 1)
            {
                ViewNotifier.Show("Item is blocked", ViewNotifierType.Warning);
                return;
            }
            if (IsShowAddFrontMarginDialog)
            {
                await CreatePromotionFrontMarginAsync();
                return;
            }

            await EditPromotionFrontMarginFormRef.Validate();
            if (EditPromotionFrontMarginFormRef.IsValid)
            {
                var rq = Mapper.Map<UpdatePromotionFrontMarginDto>(CurrentLine);
                var apiResponse = await PromotionApiClient.UpdatePromotionFrontMarginAsync(CurrentLine.Number, rq);
                if (!apiResponse.IsSuccessStatusCode)
                {
                    ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
                }
                else
                {
                    ViewNotifier.Show(L["Update success"], ViewNotifierType.Success);
                    OnCloseEditPOLineDialog();
                    await GetDetailPromotionAsync(PromotionNumber);
                }
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.GetBaseException()
                .Message, ViewNotifierType.Error);
        }
    }


    public async ValueTask DisposeAsync()
    {
        if (IsEdit)
        {
            await PromotionApiClient.CloseDocumentAsync(PromotionNumber);
        }
    }
}
