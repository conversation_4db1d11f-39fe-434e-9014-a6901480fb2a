namespace PurchaseManager.Server.Services.Promotions.Interface;

public interface IFrontMarginApprovalService
{
    /// <summary>
    ///     Get Front Margins pending approval
    /// </summary>
    Task<List<FrontMarginApprovalItem>> GetPendingApprovalsAsync(string? vendorCode = null);

    /// <summary>
    ///     Approve Front Margin
    /// </summary>
    Task<FrontMarginApprovalResult> ApproveAsync(string number, string approvedBy, string? comments = null);

    /// <summary>
    ///     Reject Front Margin
    /// </summary>
    Task<FrontMarginApprovalResult> RejectAsync(string number, string rejectedBy, string reason);

    /// <summary>
    ///     Bulk approve multiple Front Margins
    /// </summary>
    Task<FrontMarginBulkApprovalResult> BulkApproveAsync(List<string> numbers, string approvedBy);

    /// <summary>
    ///     Get approval history for Front Margin
    /// </summary>
    Task<List<FrontMarginApprovalHistory>> GetApprovalHistoryAsync(string number);

    /// <summary>
    ///     Check if user can approve Front Margin
    /// </summary>
    Task<bool> CanApproveAsync(string number, string userId);
}
