@inject NavigationManager NavigationManager
@inject IStringLocalizer<Global> L
@implements IDynamicComponent

<MudNavLink Href="@NavigationManager.ToAbsoluteUri(" ").AbsoluteUri" Match="NavLinkMatch.All">
    <MudText>
        <strong>@L["AppNavHome"]</strong>
    </MudText>
</MudNavLink>
<AuthorizeView Policy="@Policies.IsPurchaseUser">
    <MudNavGroup Title="@L["AppNavPurchase"]">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("po").AbsoluteUri" Match="NavLinkMatch.All">
            @L["AppNavPurchaseOrder"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/create").AbsoluteUri" Disabled="false"
                    Match="NavLinkMatch.All">
            @L["AppNavManualOrder"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/create-consigned").AbsoluteUri" Disabled="false"
                    Match="NavLinkMatch.All">
            <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
                      AlignItems="AlignItems.Center">
                @L["AppNavConsignedSkusOrder"]
            </MudStack>
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/create-promotional").AbsoluteUri"
                    Match="NavLinkMatch.All">
            <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
                      AlignItems="AlignItems.Center">
                @L["AppNavPromotionalOrders"]
            </MudStack>
        </MudNavLink>
    </MudNavGroup>
    <MudNavGroup Title="Demand">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/demand").AbsoluteUri" Disabled="false"
                    Match="NavLinkMatch.All">
            @L["AppNavDemand"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/demand/v2").AbsoluteUri" Disabled="false"
                    Match="NavLinkMatch.All">
            @L["AppNavDemandV2"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/purchase-suggest-payment/create-po").AbsoluteUri"
                    Disabled="false" Match="NavLinkMatch.All">
            @L["AppNavPoFromSuggest"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/purchase-suggest-payment").AbsoluteUri" Disabled="false"
                    Match="NavLinkMatch.All">
            @L["AppNavPurchaseSuggestedPayment"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/purchase-suggest-payment/reports").AbsoluteUri"
                    Disabled="false" Match="NavLinkMatch.All">
            @L["AppNavPurchaseSuggestedPaymentReports"]
        </MudNavLink>
    </MudNavGroup>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/replenishment").AbsoluteUri" Match="NavLinkMatch.All">
        @L["AppNavReplenishment"]
    </MudNavLink>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/schedule").AbsoluteUri" Match="NavLinkMatch.All"
                Disabled="@(!isLoggedIn)">
        @L["AppNavLeadTime"]
    </MudNavLink>
</AuthorizeView>
<MudNavGroup Title="Promotion">
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("promotions").AbsoluteUri" Match="NavLinkMatch.All">
        <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
                  AlignItems="AlignItems.Center">
            @L["Promotions"]
        </MudStack>
    </MudNavLink>
</MudNavGroup>
<AuthorizeView Policy="@Policies.IsPurchaseManager">
    <MudNavGroup Title="Category">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("purchase-price-upload").AbsoluteUri"
                    Match="NavLinkMatch.All">
            <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
                      AlignItems="AlignItems.Center">
                @L["UploadPurchasePrice"]
            </MudStack>
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("purchase-price").AbsoluteUri" Match="NavLinkMatch.All">
            <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
                      AlignItems="AlignItems.Center">
                @L["PurchasePriceConvert"]
            </MudStack>
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/vendors").AbsoluteUri" Match="NavLinkMatch.All">
            <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
                      AlignItems="AlignItems.Center">
                @L["Vendor Accounts"]
            </MudStack>
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("vendors").AbsoluteUri" Match="NavLinkMatch.All"
                    Disabled="@(!isLoggedIn)">
            @L["AppNavVendors"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("items").AbsoluteUri" Match="NavLinkMatch.All"
                    Disabled="@(!isLoggedIn)">
            @L["AppNavItemsSKUs"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("unit-of-measures").AbsoluteUri" Match="NavLinkMatch.All"
                    Disabled="@(!isLoggedIn)">
            @L["AppNavUnitOfMeasure"]
        </MudNavLink>
    </MudNavGroup>
</AuthorizeView>
<AuthorizeView Policy="@Policies.IsVendor">
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/create").AbsoluteUri" Disabled="false"
        Match="NavLinkMatch.All">
        @L["AppNavManualOrder"]
    </MudNavLink>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/vendor/items").AbsoluteUri" Match="NavLinkMatch.All">
        @L["AppNavVendorItems"]
    </MudNavLink>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/vendor/info").AbsoluteUri" Match="NavLinkMatch.All">
        @L["Information"]
    </MudNavLink>
    <MudNavGroup Title="Supplier">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("po").AbsoluteUri" Match="NavLinkMatch.All"
            Disabled="@(!isLoggedIn)">
            @L["AppNavOrder"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("vendor-document").AbsoluteUri" Match="NavLinkMatch.All"
            Disabled="@(!isLoggedIn)">
            @L["AppNavDocuments"]
        </MudNavLink>
    </MudNavGroup>
</AuthorizeView>

<AuthorizeView Policy="@Policies.IsMKT">
    <MudNavGroup Title="@L["AppNavPurchase"]">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("po").AbsoluteUri" Match="NavLinkMatch.All">
            @L["AppNavPurchaseOrder"]
        </MudNavLink>

        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/create").AbsoluteUri" Disabled="false"
            Match="NavLinkMatch.All">
            @L["AppNavManualOrder"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/po/create-consigned").AbsoluteUri" Disabled="false"
            Match="NavLinkMatch.All">
            <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
                AlignItems="AlignItems.Center">
                @L["AppNavConsignedSkusOrder"]
            </MudStack>
        </MudNavLink>
    </MudNavGroup>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/schedule").AbsoluteUri" Match="NavLinkMatch.All"
        Disabled="@(!isLoggedIn)">
        @L["AppNavLeadTime"]
    </MudNavLink>
    <MudNavGroup Title="Supplier">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("po").AbsoluteUri" Match="NavLinkMatch.All"
            Disabled="@(!isLoggedIn)">
            @L["AppNavOrder"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("vendor-document").AbsoluteUri" Match="NavLinkMatch.All"
            Disabled="@(!isLoggedIn)">
            @L["AppNavDocuments"]
        </MudNavLink>
    </MudNavGroup>
</AuthorizeView>


<AuthorizeView Policy="@Policies.IsWarehouseUser">
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/schedule").AbsoluteUri" Match="NavLinkMatch.All"
        Disabled="@(!isLoggedIn)">
        @L["AppNavLeadTime"]
    </MudNavLink>
    <MudNavGroup Title="Stock">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("stock-order").AbsoluteUri" Match="NavLinkMatch.All"
            Disabled="@(!isLoggedIn)" Icon="@Icons.Material.Outlined.Inventory">
            Stock Order
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("over-stock").AbsoluteUri" Match="NavLinkMatch.All"
            Disabled="true" Icon="@Icons.Material.Outlined.Inventory2">
            <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
                AlignItems="AlignItems.Center">
                Over Stock
                <MudChip T="String" Size="Size.Small" Variant="Variant.Text" Color="Color.Success">
                    Coming soon
                </MudChip>
            </MudStack>
        </MudNavLink>
    </MudNavGroup>
</AuthorizeView>
<AuthorizeView Policy="@Policies.IsVendorContact">
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("po").AbsoluteUri" Match="NavLinkMatch.All">
        @L["AppNavPurchaseOrder"]
    </MudNavLink>
</AuthorizeView>
@* <MudNavGroup Title="Reports"> *@
@* </MudNavGroup> *@
<MudNavGroup Title="Return">
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("over-stock").AbsoluteUri" Match="NavLinkMatch.All"
        Disabled="true" Icon="@Icons.Material.Outlined.Inventory2">
        <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
            AlignItems="AlignItems.Center">
            SKUs Returned
            <MudChip T="String" Disabled="true" Size="Size.Small" Variant="Variant.Text" Color="Color.Success">
                Coming Soon
            </MudChip>
        </MudStack>
    </MudNavLink>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("over-stock").AbsoluteUri" Match="NavLinkMatch.All"
        Disabled="true" Icon="@Icons.Material.Outlined.Inventory2">
        <MudStack Row="true" StretchItems="StretchItems.All" Justify="Justify.SpaceBetween"
            AlignItems="AlignItems.Center">
            @L["AppNavPOReturned"]
            <MudChip T="String" Disabled="true" Size="Size.Small" Variant="Variant.Text" Color="Color.Success">
                Coming Soon
            </MudChip>
        </MudStack>
    </MudNavLink>
</MudNavGroup>

@code {
    public Int32 Order
    {
        get
        {
            return 1;
        }
    }
    public String IntoComponent
    {
        get
        {
            return "NavMenu";
        }
    }
    public Boolean isLoggedIn;
    [CascadingParameter] Task<AuthenticationState> AuthenticationStateTask { get; set; }
    protected override async Task OnParametersSetAsync()
    {
        isLoggedIn = false;

        var user = (await AuthenticationStateTask).User;
        if (user.Identity is { IsAuthenticated: true })
        {
            isLoggedIn = true;
        }
    }
}