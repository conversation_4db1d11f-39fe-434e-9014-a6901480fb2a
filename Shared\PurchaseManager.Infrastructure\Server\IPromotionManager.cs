using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Models.Promotions;

namespace PurchaseManager.Infrastructure.Server;

public interface IPromotionManager
{
    Task<ApiResponse> GetPromotionsAsync();
    Task<ApiResponse> GetAllPromotionsAsync(PromotionFilter filter);
    Task<ApiResponse> GetPromotionByNumberAsync(string number);
    Task<ApiResponse> CreatePromotionAsync(CreatePromotionHeaderDto createDto);
    Task<ApiResponse> UpdatePromotionAsync(string number, UpdatePromotionHeaderDto updateDto);
    Task<ApiResponse> DeletePromotionAsync(List<string> numbers);

    Task<ApiResponse> IsDocumentLockedByAnotherUserAsync(string documentNumber);
    /// <summary>
    /// Mở chứng từ
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <returns></returns>
    Task<ApiResponse> OpenDocumentAsync(string documentNumber);
    /// <summary>
    /// Đóng chứng từ
    /// </summary>
    /// <param name="documentNumber"></param>
    /// <returns></returns>
    Task<ApiResponse> CloseDocumentAsync(string documentNumber);

    #region Front margins
    Task<ApiResponse> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponse> GetPromotionFrontMarginByNumberAsync(string number);
    Task<ApiResponse> UpdatePromotionFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto);
    Task<ApiResponse> IsDocumentLockedByAnotherUserFrontMarginAsync(string number);
    Task<ApiResponse> DeletePromotionFrontMarginAsync(List<string> numbers);
    #endregion
}
