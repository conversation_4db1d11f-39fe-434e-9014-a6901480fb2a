@using Settings=PurchaseManager.Constants.Settings
@inherits DynamicComponentContainer
@inject NavigationManager NavigationManager
@inject HttpClient Http
@inject IStringLocalizer<Global> L

<MudNavMenu Style="flex: 1">
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri(" ").AbsoluteUri" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.Home">
        Frontend
    </MudNavLink>

    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin").AbsoluteUri" Match="NavLinkMatch.All" Icon="@Icons.Material.Filled.BarChart">
        @L["Dashboard"]
    </MudNavLink>
    <MudNavGroup Title="@L["Settings"]" Icon="@Icons.Material.Filled.Settings">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/settings").AbsoluteUri" Match="NavLinkMatch.All"
                    Icon="@Icons.Material.Filled.SettingsApplications">
            @L["MainSettings"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/settings/email").AbsoluteUri" Icon="@Icons.Material.Filled.Email">
            @L["Email"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/admin/settings/numberseries").AbsoluteUri" Icon="@Icons.Material.Filled.LibraryBooks">
            @L["NumberSeries"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("/admin/settings/numberseriesline").AbsoluteUri" Icon="@Icons.Material.Filled.LibraryBooks">
            @L["NumberSeriesLine"]
        </MudNavLink>
    </MudNavGroup>
    <MudNavGroup Title="@L["Localization"]" Icon="@Icons.Material.Filled.Public">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/translations").AbsoluteUri" Icon="@Icons.Material.Filled.Translate">
            @L["Translations"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/pluralizationrules").AbsoluteUri" Icon="@Icons.Material.Filled.Group">
            @L["Pluralization rules"]
        </MudNavLink>
    </MudNavGroup>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/users").AbsoluteUri" Icon="@Icons.Material.Filled.SupervisorAccount">
        @L["Users"]
    </MudNavLink>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("quartz").AbsoluteUri" Icon="@Icons.Material.Filled.AutoMode">
        @L["Jobs"]
    </MudNavLink>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/reasons").AbsoluteUri"
        Icon="@Icons.Material.Filled.NoteAlt">
        @L["Reasons"]
    </MudNavLink>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/contacts").AbsoluteUri"
        Icon="@Icons.Material.Filled.NoteAlt">
        @L["Contact"]
    </MudNavLink>
    <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/roles").AbsoluteUri" Icon="@Icons.Material.Filled.SupervisorAccount">
        @L["Roles"]
    </MudNavLink>
    @if (_tenant.Id == Settings.DefaultTenantId)
    {
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/multitenancy").AbsoluteUri" Icon="@Icons.Material.Filled.AccountBalance">
            @L["MultiTenancy"]
        </MudNavLink>
    }
    <MudNavGroup Title="@L["AppAdminNavMonitoring"]" Icon="@Icons.Material.Filled.Monitor">
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/apilog").AbsoluteUri" Icon="@Icons.Material.Filled.ChangeHistory">
            @L["AppAdminNavApiAuditLog"]
        </MudNavLink>
        <MudNavLink Href="@NavigationManager.ToAbsoluteUri("admin/dblog").AbsoluteUri" Icon="@Icons.Material.Filled.Notes">
            @L["AppAdminNavDBLoggingView"]
        </MudNavLink>
    </MudNavGroup>
    @foreach (var component in components)
    {
        @CreateDynamicComponent(component);
    }
</MudNavMenu>

@code {
    TenantDto _tenant = new TenantDto();

    [CascadingParameter]
    Task<AuthenticationState> AuthenticationStateTask { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        var user = (await AuthenticationStateTask).User;

        if (user.Identity is { IsAuthenticated: true })
        {
            var apiResponse = await Http.GetNewtonsoftJsonAsync<ApiResponseDto<TenantDto>>("api/admin/tenant");
            if (apiResponse.StatusCode == Status200OK)
            {
                _tenant = apiResponse.Result;
            }
        }
    }
}
