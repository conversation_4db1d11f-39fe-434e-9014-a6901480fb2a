using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Localizer;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

/// <summary>
/// Controller for Front Margin declaration and approval workflow
/// </summary>
[ApiController]
[Route("api/front-margin-declaration")]
public class FrontMarginDeclarationController : ControllerBase
{
    private readonly IFrontMarginDeclarationService _declarationService;
    private readonly IFrontMarginApprovalService _approvalService;
    private readonly ApiResponse _invalidData;

    public FrontMarginDeclarationController(
        IFrontMarginDeclarationService declarationService,
        IFrontMarginApprovalService approvalService,
        IStringLocalizer<Global> i18N)
    {
        _declarationService = declarationService;
        _approvalService = approvalService;
        _invalidData = new ApiResponse(Status400BadRequest, i18N["InvalidData"]);
    }

    /// <summary>
    /// Validate Front Margin declaration before saving
    /// </summary>
    [HttpPost("validate")]
    public async Task<ApiResponse> ValidateDeclaration([FromBody] CreatePromotionFrontMarginDto dto)
    {
        if (!ModelState.IsValid)
            return _invalidData;

        var result = await _declarationService.ValidateDeclarationAsync(dto);

        return ApiResponse.S200("Validation completed", new
        {
            IsValid = result.IsValid,
            ValidationErrors = result.ValidationErrors,
            Warnings = result.Warnings,
            Conflicts = result.Conflicts,
            HeaderInfo = result.HeaderInfo
        });
    }

    /// <summary>
    /// Register Front Margin for approval workflow
    /// </summary>
    [HttpPost("register")]
    public async Task<ApiResponse> RegisterForApproval([FromBody] CreatePromotionFrontMarginDto dto)
    {
        if (!ModelState.IsValid)
            return _invalidData;

        var createdBy = "SYSTEM"; // TODO: Get from current user context
        var result = await _declarationService.RegisterForApprovalAsync(dto, createdBy);

        if (!result.IsValid)
        {
            return ApiResponse.S400("Registration failed", result.ValidationErrors);
        }

        return ApiResponse.S200("Front Margin registered successfully", new
        {
            Number = result.CreatedNumber,
            InitialStatus = result.InitialStatus,
            RequiresApproval = result.RequiresApproval,
            Warnings = result.Warnings,
            Conflicts = result.Conflicts
        });
    }

    /// <summary>
    /// Bulk register multiple Front Margins
    /// </summary>
    [HttpPost("bulk-register")]
    public async Task<ApiResponse> BulkRegister([FromBody] List<CreatePromotionFrontMarginDto> dtos)
    {
        if (!ModelState.IsValid)
            return _invalidData;

        var createdBy = "SYSTEM"; // TODO: Get from current user context
        var result = await _declarationService.BulkRegisterAsync(dtos, createdBy);

        return ApiResponse.S200("Bulk registration completed", new
        {
            TotalItems = result.TotalItems,
            SuccessCount = result.SuccessCount,
            FailureCount = result.FailureCount,
            SuccessfulItems = result.SuccessfulItems.Select(s => new
            {
                Number = s.CreatedNumber,
                RequiresApproval = s.RequiresApproval
            }),
            FailedItems = result.FailedItems.Select(f => new
            {
                ValidationErrors = f.ValidationErrors,
                Warnings = f.Warnings
            })
        });
    }

    /// <summary>
    /// Get declaration status and workflow information
    /// </summary>
    [HttpGet("status/{number}")]
    public async Task<ApiResponse> GetDeclarationStatus(string number)
    {
        var status = await _declarationService.GetDeclarationStatusAsync(number);
        return ApiResponse.S200("Declaration status retrieved", status);
    }

    /// <summary>
    /// Check for conflicts with existing promotions
    /// </summary>
    [HttpPost("check-conflicts")]
    public async Task<ApiResponse> CheckConflicts([FromBody] CreatePromotionFrontMarginDto dto)
    {
        if (!ModelState.IsValid)
            return _invalidData;

        var conflicts = await _declarationService.CheckConflictsAsync(dto);

        return ApiResponse.S200("Conflict check completed", new
        {
            HasConflicts = conflicts.Any(),
            ConflictCount = conflicts.Count,
            Conflicts = conflicts
        });
    }

    /// <summary>
    /// Preview calculation impact before registration
    /// </summary>
    [HttpPost("preview-impact")]
    public async Task<ApiResponse> PreviewImpact([FromBody] CreatePromotionFrontMarginDto dto)
    {
        if (!ModelState.IsValid)
            return _invalidData;

        var preview = await _declarationService.PreviewImpactAsync(dto);
        return ApiResponse.S200("Impact preview generated", preview);
    }

    #region Approval Endpoints

    /// <summary>
    /// Get Front Margins pending approval
    /// </summary>
    [HttpGet("pending-approvals")]
    public async Task<ApiResponse> GetPendingApprovals([FromQuery] string? vendorCode = null)
    {
        var pendingItems = await _approvalService.GetPendingApprovalsAsync(vendorCode);

        return ApiResponse.S200("Pending approvals retrieved", new
        {
            TotalCount = pendingItems.Count,
            HighPriorityCount = pendingItems.Count(p => p.Priority == "HIGH"),
            Items = pendingItems
        });
    }

    /// <summary>
    /// Approve Front Margin
    /// </summary>
    [HttpPost("approve/{number}")]
    public async Task<ApiResponse> Approve(string number, [FromBody] ApprovalRequest request)
    {
        var approvedBy = "SYSTEM"; // TODO: Get from current user context
        var result = await _approvalService.ApproveAsync(number, approvedBy, request.Comments);

        if (!result.Success)
        {
            return ApiResponse.S400(result.Message);
        }

        return ApiResponse.S200(result.Message, new
        {
            Number = number,
            NewStatus = result.NewStatus,
            ApprovedBy = result.ApprovedBy,
            ApprovedAt = result.ApprovedAt
        });
    }

    /// <summary>
    /// Reject Front Margin
    /// </summary>
    [HttpPost("reject/{number}")]
    public async Task<ApiResponse> Reject(string number, [FromBody] RejectionRequest request)
    {
        if (string.IsNullOrEmpty(request.Reason))
        {
            return ApiResponse.S400("Rejection reason is required");
        }

        var rejectedBy = "SYSTEM"; // TODO: Get from current user context
        var result = await _approvalService.RejectAsync(number, rejectedBy, request.Reason);

        if (!result.Success)
        {
            return ApiResponse.S400(result.Message);
        }

        return ApiResponse.S200(result.Message, new
        {
            Number = number,
            NewStatus = result.NewStatus,
            RejectedBy = result.ApprovedBy,
            RejectedAt = result.ApprovedAt,
            Reason = request.Reason
        });
    }

    /// <summary>
    /// Bulk approve multiple Front Margins
    /// </summary>
    [HttpPost("bulk-approve")]
    public async Task<ApiResponse> BulkApprove([FromBody] BulkApprovalRequest request)
    {
        if (!request.Numbers.Any())
        {
            return ApiResponse.S400("No Front Margin numbers provided");
        }

        var approvedBy = "SYSTEM"; // TODO: Get from current user context
        var result = await _approvalService.BulkApproveAsync(request.Numbers, approvedBy);

        return ApiResponse.S200("Bulk approval completed", new
        {
            TotalItems = result.TotalItems,
            SuccessCount = result.SuccessCount,
            FailureCount = result.FailureCount,
            SuccessfulItems = result.SuccessfulItems,
            FailedItems = result.FailedItems
        });
    }

    /// <summary>
    /// Get approval history for Front Margin
    /// </summary>
    [HttpGet("approval-history/{number}")]
    public async Task<ApiResponse> GetApprovalHistory(string number)
    {
        var history = await _approvalService.GetApprovalHistoryAsync(number);
        return ApiResponse.S200("Approval history retrieved", history);
    }

    /// <summary>
    /// Check if current user can approve Front Margin
    /// </summary>
    [HttpGet("can-approve/{number}")]
    public async Task<ApiResponse> CanApprove(string number)
    {
        var userId = "SYSTEM"; // TODO: Get from current user context
        var canApprove = await _approvalService.CanApproveAsync(number, userId);

        return ApiResponse.S200("Approval permission checked", new
        {
            Number = number,
            CanApprove = canApprove,
            UserId = userId
        });
    }

    #endregion

    #region Dashboard Endpoints

    /// <summary>
    /// Get Front Margin declaration dashboard data
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ApiResponse> GetDashboard([FromQuery] string? vendorCode = null)
    {
        var pendingApprovals = await _approvalService.GetPendingApprovalsAsync(vendorCode);

        var dashboard = new
        {
            PendingApprovals = new
            {
                Total = pendingApprovals.Count,
                HighPriority = pendingApprovals.Count(p => p.Priority == "HIGH"),
                MediumPriority = pendingApprovals.Count(p => p.Priority == "MEDIUM"),
                LowPriority = pendingApprovals.Count(p => p.Priority == "LOW")
            },
            ByDiscountType = pendingApprovals
                .GroupBy(p => p.DiscountType)
                .Select(g => new
                {
                    DiscountType = g.Key,
                    DiscountTypeName = g.First().DiscountTypeName,
                    Count = g.Count()
                }),
            ByVendor = pendingApprovals
                .GroupBy(p => p.VendorCode)
                .Select(g => new
                {
                    VendorCode = g.Key,
                    VendorName = g.First().VendorName,
                    Count = g.Count()
                }),
            RecentItems = pendingApprovals
                .OrderByDescending(p => p.CreatedAt)
                .Take(10)
        };

        return ApiResponse.S200("Dashboard data retrieved", dashboard);
    }

    #endregion
}

#region Request Models

public class ApprovalRequest
{
    public string? Comments { get; set; }
}

public class RejectionRequest
{
    public string Reason { get; set; } = string.Empty;
}

public class BulkApprovalRequest
{
    public List<string> Numbers { get; set; } = new();
    public string? Comments { get; set; }
}

#endregion
