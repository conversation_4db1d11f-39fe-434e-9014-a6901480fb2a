using PurchaseManager.Infrastructure.Storage.DataModels;
namespace PurchaseManager.Server.Services.Promotions.Interface;

/// <summary>
///     Caching service for Front Margin promotions to improve performance
/// </summary>
public interface IFrontMarginCacheService
{
    /// <summary>
    ///     Get active Front Margins for vendor with caching
    /// </summary>
    Task<List<PromotionFrontMargin>> GetActivePromotionsAsync(string vendorCode);

    /// <summary>
    ///     Get Front Margins for specific item with caching
    /// </summary>
    Task<List<PromotionFrontMargin>> GetPromotionsForItemAsync(string vendorCode, string itemNumber);

    /// <summary>
    ///     Clear cache for vendor
    /// </summary>
    void ClearVendorCache(string vendorCode);

    /// <summary>
    ///     Clear all Front Margin cache
    /// </summary>
    void ClearAllCache();

    /// <summary>
    ///     Warm up cache for frequently accessed vendors
    /// </summary>
    Task WarmUpCacheAsync(List<string> vendorCodes);

    /// <summary>
    ///     Get cache statistics
    /// </summary>
    FrontMarginCacheStats GetCacheStats();
}
