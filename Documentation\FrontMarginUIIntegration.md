# Front Margin UI Integration Guide

## 🎯 Overview
This guide shows how to integrate Front Margin functionality into PO creation UI using the provided components and services.

## 📋 Components Created

### 1. FrontMarginUIService
- **Purpose**: API client service for Front Margin operations in UI
- **Location**: `Shared\PurchaseManager.Shared\Services\FrontMarginUIService.cs`
- **Key Methods**:
  - `HasActiveFrontMarginAsync(vendorCode)` - Check if vendor has active FM
  - `GetLineDiscountAsync(vendorCode, itemNumber, orderDate)` - Get discount for specific item
  - `GetApplicablePromotionsAsync(vendorCode, orderDate)` - Get all applicable promotions
  - `PreviewPOImpactAsync(poHeader, poLines)` - Preview FM impact on entire PO

### 2. FrontMarginIndicator Component
- **Purpose**: Shows Front Margin availability for vendor
- **Location**: `Shared\Modules\PurchaseManager.Theme.MudBlazor.Demo\Shared\Components\PO\FrontMarginIndicator.razor`
- **Usage**: Display in vendor selection area

### 3. POLineWithFrontMargin Component
- **Purpose**: Enhanced PO line component with Front Margin integration
- **Location**: `Shared\Modules\PurchaseManager.Theme.MudBlazor.Demo\Shared\Components\PO\POLineWithFrontMargin.razor`
- **Features**:
  - Auto-apply Front Margin discounts
  - Show original vs discounted prices
  - Disable manual discount input when FM applied
  - Front Margin details dialog

## 🔧 Integration Steps

### Step 1: Update PO Creation Page

```razor
@* In your PO creation page (e.g., POForm.razor) *@
@using PurchaseManager.Shared.Services
@inject IFrontMarginUIService FrontMarginService

<MudCard>
    <MudCardHeader>
        <CardHeaderContent>
            <MudText Typo="Typo.h6">Purchase Order</MudText>
        </CardHeaderContent>
        <CardHeaderActions>
            @* Add Front Margin Indicator *@
            <FrontMarginIndicator VendorCode="@POHeader.BuyFromVendorNumber" 
                                  OrderDate="@POHeader.OrderDate"
                                  ShowDetails="true"
                                  OnFrontMarginStatusChanged="OnFrontMarginStatusChanged" />
        </CardHeaderActions>
    </MudCardHeader>
    
    <MudCardContent>
        @* Vendor Selection *@
        <MudSelect @bind-Value="POHeader.BuyFromVendorNumber" 
                   Label="Vendor"
                   ValueChanged="OnVendorChanged">
            @foreach (var vendor in Vendors)
            {
                <MudSelectItem Value="@vendor.Number">@vendor.Name</MudSelectItem>
            }
        </MudSelect>

        @* PO Lines Table *@
        <MudTable Items="@POLines" Hover="true" Breakpoint="Breakpoint.Sm">
            <HeaderContent>
                <MudTh>Item</MudTh>
                <MudTh>Description</MudTh>
                <MudTh>Quantity</MudTh>
                <MudTh>Unit Cost</MudTh>
                <MudTh>Discount</MudTh>
                <MudTh>Amount</MudTh>
                <MudTh>Actions</MudTh>
            </HeaderContent>
            <RowTemplate>
                @* Use enhanced PO line component *@
                <POLineWithFrontMargin POLine="@context" 
                                       VendorCode="@POHeader.BuyFromVendorNumber"
                                       OrderDate="@POHeader.OrderDate"
                                       IsReadOnly="@IsReadOnly"
                                       OnLineChanged="OnPOLineChanged"
                                       OnLineDeleted="OnPOLineDeleted" />
            </RowTemplate>
        </MudTable>
    </MudCardContent>
</MudCard>

@code {
    private POHeaderGetDto POHeader = new();
    private List<POLineGetDto> POLines = new();
    private bool HasFrontMargin = false;
    private bool IsReadOnly = false;

    private async Task OnVendorChanged(string vendorCode)
    {
        POHeader.BuyFromVendorNumber = vendorCode;
        
        // Check Front Margin availability
        HasFrontMargin = await FrontMarginService.HasActiveFrontMarginAsync(vendorCode);
        
        // Refresh existing lines to apply Front Margin
        if (HasFrontMargin && POLines.Any())
        {
            await RefreshPOLinesWithFrontMargin();
        }
        
        StateHasChanged();
    }

    private async Task OnFrontMarginStatusChanged(bool hasFrontMargin)
    {
        HasFrontMargin = hasFrontMargin;
        
        if (hasFrontMargin)
        {
            // Show notification
            Snackbar.Add("Front Margin promotions available for this vendor!", Severity.Success);
        }
    }

    private async Task OnPOLineChanged(POLineGetDto line)
    {
        // Update the line in collection
        var index = POLines.FindIndex(l => l.LineNumber == line.LineNumber);
        if (index >= 0)
        {
            POLines[index] = line;
        }
        
        // Recalculate totals
        await CalculatePOTotals();
        StateHasChanged();
    }

    private async Task OnPOLineDeleted(POLineGetDto line)
    {
        POLines.Remove(line);
        await CalculatePOTotals();
        StateHasChanged();
    }

    private async Task RefreshPOLinesWithFrontMargin()
    {
        var updatedLines = await FrontMarginService.ApplyFrontMarginToLinesAsync(
            POLines, POHeader.BuyFromVendorNumber, POHeader.OrderDate);
        
        POLines = updatedLines;
    }

    private async Task CalculatePOTotals()
    {
        // Calculate PO totals including Front Margin savings
        POHeader.TotalAmount = POLines.Sum(l => l.Amount);
        
        if (HasFrontMargin)
        {
            var totalSavings = POLines.Where(l => l.HasFrontMargin)
                                     .Sum(l => l.FrontMarginDiscountAmount);
            
            // Show savings information
            if (totalSavings > 0)
            {
                Snackbar.Add($"Total Front Margin savings: {totalSavings:N2}", Severity.Info);
            }
        }
    }
}
```

### Step 2: Update PO Line Addition Logic

```csharp
private async Task AddPOLine(string itemNumber)
{
    var newLine = new POLineGetDto
    {
        LineNumber = POLines.Count + 1,
        ItemNumber = itemNumber,
        Quantity = 1,
        UnitCost = await GetItemUnitCost(itemNumber),
        // ... other properties
    };

    // Check and apply Front Margin if available
    if (HasFrontMargin)
    {
        var discount = await FrontMarginService.GetLineDiscountAsync(
            POHeader.BuyFromVendorNumber, itemNumber, POHeader.OrderDate);
        
        if (discount != null)
        {
            newLine.OriginalUnitCost = newLine.UnitCost;
            newLine.HasFrontMargin = true;
            newLine.FrontMarginNumber = discount.PromotionNumber;
            newLine.FrontMarginDiscountPercent = discount.DiscountPercentage;
            newLine.DiscountSource = "Front Margin";
            newLine.CanEditDiscount = false;
            
            // Apply discount
            if (discount.DiscountType == 1) // Percentage
            {
                newLine.UnitCost = newLine.OriginalUnitCost * (1 - discount.DiscountPercentage / 100);
            }
            
            newLine.FrontMarginDiscountAmount = (newLine.OriginalUnitCost - newLine.UnitCost) * newLine.Quantity;
        }
    }

    newLine.Amount = newLine.UnitCost * newLine.Quantity;
    POLines.Add(newLine);
    
    await CalculatePOTotals();
    StateHasChanged();
}
```

### Step 3: Disable Manual Discount When Front Margin Applied

```razor
@* In discount input field *@
<MudNumericField @bind-Value="POLine.LineDiscountPercent" 
                 Label="Discount %"
                 Disabled="@(POLine.HasFrontMargin || IsReadOnly)"
                 Adornment="@(POLine.HasFrontMargin ? Adornment.End : Adornment.None)"
                 AdornmentIcon="@(POLine.HasFrontMargin ? Icons.Material.Filled.LocalOffer : null)"
                 AdornmentColor="@(POLine.HasFrontMargin ? Color.Success : Color.Default)"
                 HelperText="@(POLine.HasFrontMargin ? "Front Margin Applied" : "")" />
```

## 🎨 UI Enhancements

### Visual Indicators
- ✅ **Green chip** for Front Margin applied lines
- ✅ **Offer icon** next to items with Front Margin
- ✅ **Strikethrough** original price when discounted
- ✅ **Savings amount** display
- ✅ **Disabled state** for manual discount input

### User Experience
- ✅ **Auto-apply** Front Margin when vendor/item selected
- ✅ **Real-time preview** of discounts
- ✅ **Details dialog** for Front Margin information
- ✅ **Notifications** for savings and availability

## 🔧 API Endpoints Used

The UI components call these API endpoints:
- `GET /api/FrontMargin/available/{vendorCode}` - Get available promotions
- `GET /api/FrontMargin/discount/{vendorCode}/{itemNumber}` - Get item discount
- `GET /api/FrontMargin/applicable/{vendorCode}/{itemNumber}` - Check applicability
- `POST /api/POFrontMargin/preview` - Preview PO impact

## 🎯 Next Steps

1. **Integrate components** into existing PO creation pages
2. **Test Front Margin** auto-apply functionality
3. **Customize styling** to match your theme
4. **Add error handling** for API failures
5. **Implement caching** for better performance

## 📝 Notes

- Front Margin is automatically applied when vendor and item match active promotions
- Manual discount input is disabled when Front Margin is applied
- Users can view Front Margin details through the info dialog
- All Front Margin logic is handled server-side for security
- UI components are reactive and update in real-time
