﻿using Microsoft.AspNetCore.Components;
using MudBlazor;
using PurchaseManager.Constants.Enum;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Promotions;
using PurchaseManager.UI.Base.Shared.Components;
using NavigationManagerExtensions=PurchaseManager.Shared.Extensions.NavigationManagerExtensions;

namespace PurchaseManager.Theme.Material.Demo.Pages.Promotions;

public class PromotionIndex : BaseComponent
{
    #region Parameter queries
    [Parameter]
    [SupplyParameterFromQuery]
    public DateTime FromDate { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public DateTime ToDate { get; set; }

    [Parameter]
    [SupplyParameterFromQuery(Name = "programCode")]
    public string ProgramCodeQuery { get; set; } = string.Empty;

    [Parameter]
    [SupplyParameterFromQuery(Name = "description")]
    public string DescriptionQuery { get; set; } = string.Empty;

    [Parameter]
    [SupplyParameterFromQuery(Name = "vendorNumber")]
    public string VendorNumberQuery { get; set; } = string.Empty;

    [Parameter]
    [SupplyParameterFromQuery(Name = "status")]
    public string StatusQuery { get; set; } = string.Empty;

    [Parameter]
    [SupplyParameterFromQuery(Name = "vendorName")]
    public string VendorNameQuery { get; set; } = string.Empty;
    #endregion
    public Dictionary<string, string> QueryParams
    {
        get;
    } = [];
    [Inject]
    protected IDialogService DialogService { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    protected bool IsLoading { get; set; }
    [Inject]
    public IPromotionApiClient PromotionApiClient { get; set; }
    [Inject]
    public IVendorApiClient VendorApiClient { get; set; }
    protected int SelectedStatus { get; set; } = 99;
    protected MudTable<GetPromotionHeaderDto> Table { get; set; }
    protected List<GetPromotionHeaderDto> PromotionsList { get; set; } = [];
    protected HashSet<GetPromotionHeaderDto> SelectedPromotions { get; set; } = [];
    private int PageSize { get; set; } = 10;
    private int PageIndex { get; set; }
    protected GetVendorDto VendorFilter { get; set; } = new GetVendorDto();
    private int TotalItemsCount { get; set; }
    protected bool IsShowCreatePromotionDialog { get; set; }
    protected CreatePromotionPage CreatePromotionDialog { get; set; }

    protected override async Task OnInitializedAsync()
    {
        IsLoading = true;
        await base.OnInitializedAsync();
        IsLoading = false;
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Table is not null)
        {
            await Reload();
        }

        await base.OnParametersSetAsync();
    }

    protected void OnSearchByProgramCode(string value)
    {
        ProgramCodeQuery = value;
        SetParametersToUrl();
    }

    protected void OnSearchByStatus(int status)
    {
        if (status == 99)
        {
            StatusQuery = null;
        }
        StatusQuery = status.ToString();
        SelectedStatus = status;
        SetParametersToUrl();
    }

    protected void OnClearStatusFilter()
    {
        SelectedStatus = 99;// Reset to "All" status
        StatusQuery = null;// Clear the status query
        SetParametersToUrl();
    }

    protected void OnSearchByDescription(string value)
    {
        DescriptionQuery = value;
        SetParametersToUrl();
    }

    protected void OnSearchByVendor(GetVendorDto selectedValue)
    {
        if (selectedValue == null)
        {
            VendorNameQuery = VendorNameQuery = "";
            VendorFilter = new GetVendorDto();
            VendorNumberQuery = null;
        }
        else
        {
            VendorFilter = selectedValue;
            VendorNumberQuery = selectedValue.Number;
            VendorNameQuery = selectedValue.Name;
        }
    }

    protected async Task<IEnumerable<GetVendorDto>> VendorSearch(string value, CancellationToken token)
    {
        var result = new List<GetVendorDto>();

        if (token.IsCancellationRequested)
        {
            return result;
        }

        var allItem = new GetVendorDto
        {
            Number = null, Name = L["ALL"], Blocked = 0
        };

        result.Add(allItem);

        if (string.IsNullOrEmpty(value))
        {
            return result;
        }
        var vendorNumber = value.Trim()
            .Equals(L["ALL"], StringComparison.OrdinalIgnoreCase) ? string.Empty
            : value.Split('-')[0]
                .Trim();

        var apiResponse = await VendorApiClient.GetVendorsByNumber(vendorNumber, token);

        if (!apiResponse.IsSuccessStatusCode || apiResponse.Result?.Any() != true)
        {
            return result;
        }
        foreach (var item in apiResponse.Result.Where(item => result.All(r => r.Number != item.Number)))
        {
            result.Add(item);
        }

        return result;
    }

    // Navigation and URL management
    protected void SetParametersToUrl()
    {
        var queries = BuildQueryParams();
        navigationManager.NavigateTo("/promotions" + queries);
    }

    public string BuildQueryParams()
    {
        QueryParams
            .SetParam("fromDate", FromDate.ToString("yyyy-MM-dd"))
            .SetParam("toDate", ToDate.ToString("yyyy-MM-dd"))
            .SetParam("programCode", ProgramCodeQuery)
            .SetParam("description", DescriptionQuery)
            .SetParam("vendorNumber", VendorNumberQuery)
            .SetParam("status", StatusQuery);

        return NavigationManagerExtensions.ToQueryString(QueryParams);
    }

    // Data loading
    private async Task LoadDataAsync()
    {
        try
        {
            var promotionFilter = new PromotionFilter
            {
                VendorNumber = VendorNumberQuery,
                PageIndex = PageIndex,
                Description = DescriptionQuery,
                PageSize = PageSize,
                PromotionNumber = ProgramCodeQuery,
            };
            if (StatusQuery is not null)
            {
                promotionFilter.Status = int.Parse(StatusQuery);
            }
            var response = await PromotionApiClient.GetPromotionsAsync(promotionFilter);

            PromotionsList = response.Result.Data;
            TotalItemsCount = response.Result.RowCount;
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException()
                .Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }

    protected async Task<TableData<GetPromotionHeaderDto>> ServerReload(TableState state, CancellationToken token)
    {
        OnPage(state.Page, state.PageSize);
        await LoadDataAsync();

        return new TableData<GetPromotionHeaderDto>
        {
            TotalItems = TotalItemsCount, Items = PromotionsList
        };
    }

    public async Task Reload()
    {
        await Table.ReloadServerData();
    }

    private void OnPage(int index, int size)
    {
        PageSize = size;
        PageIndex = index;
    }

    protected void PageChanged(int i)
    {
        Table.NavigateTo(i - 1);
    }

    // Navigation helpers
    protected static string NavigateToDetails(GetPromotionHeaderDto promotionHeader)
    {
        return promotionHeader.ProgramType switch
        {
            (int)PromotionProgramTypeEnum.FrontMargin => "/promotional/front-margin/" + promotionHeader.ProgramCode,
            (int)PromotionProgramTypeEnum.BackMargin => "/promotional/back-margin/" + promotionHeader.ProgramCode,
            _ => string.Empty
        };
    }

    #region Dialog Methods
    protected async Task OnCreatePromotion()
    {
        await CreatePromotionDialog.OpenDialog();
    }

    protected void OnCreatePromotionDialogVisibleChanged(bool isVisible)
    {
        IsShowCreatePromotionDialog = isVisible;
    }

    protected async Task OnPromotionSaved()
    {
        await Table.ReloadServerData();
    }

    protected async Task OnDeleteSelectedPromotions()
    {
        if (SelectedPromotions?.Count > 0)
        {
            var result = await DialogService.ShowMessageBox(
            "Confirm Delete",
            $"Are you sure you want to delete {SelectedPromotions.Count} selected promotion(s)?",
            "Delete", cancelText: "Cancel");

            if (result == true)
            {
                try
                {
                    var numbers = SelectedPromotions.Select(x => x.ProgramCode)
                        .ToList();

                    var response = await PromotionApiClient.DeletePromotionHeaderAsync(numbers);

                    if (response.IsSuccessStatusCode)
                    {
                        ViewNotifier.Show($"Successfully deleted {numbers.Count} front margin(s)",
                        ViewNotifierType.Success);
                    }
                    else
                    {
                        ViewNotifier.Show(response.Message, ViewNotifierType.Error);
                    }
                    ViewNotifier.Show($"{SelectedPromotions.Count} promotion(s) deleted successfully", ViewNotifierType.Success);
                    SelectedPromotions.Clear();
                    await Table.ReloadServerData();
                }
                catch (Exception ex)
                {
                    ViewNotifier.Show($"Error deleting promotions: {ex.Message}", ViewNotifierType.Error);
                }
            }
        }
    }

    protected async Task OnRefreshData()
    {
        await Table.ReloadServerData();
        ViewNotifier.Show("Data refreshed successfully", ViewNotifierType.Success);
    }
    #endregion
}
