using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.Contact;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Theme.Material.Demo.Shared.Components.PO;
using NavigationManagerExtensions=PurchaseManager.Shared.Extensions.NavigationManagerExtensions;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder;

public partial class POPageIndex : ComponentBase
{
    #region Parameter queries
    [Parameter]
    [SupplyParameterFromQuery]
    public DateTime FromDate { get; set; }
    [Parameter]
    [SupplyParameterFromQuery]
    public DateTime ToDate { get; set; }
    [Parameter]
    [SupplyParameterFromQuery(Name = "vendorNumber")]
    public string VendorNumberQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "contactNumber")]
    public string ContactNumberQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "vendorName")]
    public string VendorNameQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "description")]
    public string DescriptionQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "status")]
    public string StatusQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "poNumber")]
    public string PONumberQuery { get; set; } = string.Empty;
    #endregion
    private Dictionary<string, string> QueryParams
    {
        get;
    } = [];
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    private NavigationManager NavigationManager { get; set; }
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject]
    private IVendorApiClient VendorApiClient { get; set; }
    [Inject]
    private IContactApiClient ContactApiClient { get; set; }
    protected ListPOByUser ListPoByUserRef { get; set; }
    protected bool IsLoading { get; set; }
    protected bool IsParamsLoading { get; set; }
    protected UserViewModel UserViewModel { get; set; } = new UserViewModel();
    protected DateRange DateRange { get; set; } = new DateRange();
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    protected GetVendorDto VendorFilter { get; set; } = new GetVendorDto();
    private GetContactDto ContactFilter { get; set; } = new GetContactDto();
    protected bool IsPurchaseUser { get; set; }
    protected bool IsVendor { get; set; }
    protected bool IsVendorContact { get; set; }
    protected bool IsAdmin { get; set; }
    private bool IsMKT { get; set; }
    private int? StatusFilter { get; set; }
    private PurchaseOrderFilerEnum SelectedStatus { get; set; } = PurchaseOrderFilerEnum.All;
    protected override async Task OnInitializedAsync()
    {
        IsParamsLoading = true;
        IsLoading = true;
        await GetCurrentUserRoles();
        await base.OnInitializedAsync();
        IsLoading = false;
        IsParamsLoading = false;
    }

    private void SetDefaultVendorFilterByUser()
    {
        // get vendor code
        if (!string.IsNullOrEmpty(UserViewModel.VendorCode))
        {
            VendorNumberQuery = UserViewModel.VendorCode;
        }
        if (!string.IsNullOrEmpty(UserViewModel.ContactCode))
        {
            ContactNumberQuery = UserViewModel.ContactCode;
        }
    }

    private async Task GetCurrentUserRoles()
    {
        var authState = await AuthenticationStateTask;
        var user = authState.User;

        IsPurchaseUser = user.IsPurchaseUser();
        IsAdmin = user.IsAdmin();
        IsMKT = user.IsMKT();

        // if vendor or vendor contact is logged in then use default vendor code by user
        IsVendor = UserViewModel.VendorCode != null && user.IsVendor();
        IsVendorContact = user.IsVendorContact();
    }

    private async Task GetStaticData()
    {
        //date range setting
        var defaultDay = new DateTime();
        var currentDate = DateTime.Now.Date;
        if (FromDate == defaultDay)
        {
            FromDate = new DateTime(currentDate.Year, currentDate.Month, 1);
            DateRange.Start = FromDate;
        }

        if (ToDate == defaultDay)
        {
            ToDate = FromDate.AddMonths(1).AddDays(-1);
            DateRange.End = ToDate;
        }

        UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
    }

    protected override async Task OnParametersSetAsync()
    {
        IsParamsLoading = true;
        await GetStaticData();
        await GetCurrentUserRoles();
        SetDefaultVendorFilterByUser();
        switch (IsVendor)
        {
            // filter by vendor when user is not vendor or vendor contact
            case false when !IsVendorContact:
                VendorFilter.Name = VendorNameQuery;
                VendorFilter.Number = VendorNumberQuery;
                ContactFilter.Number = ContactNumberQuery;
                ContactFilter.Name = ContactNumberQuery;
                break;
            // ignore filter by vendor contact when user is vendor
            case true:
                ContactNumberQuery = null;
                break;
        }
        await base.OnParametersSetAsync();
        IsParamsLoading = false;
    }

    protected void OnCreateDateFilterChanged(DateRange dateRange)
    {
        if (dateRange.Start != null && dateRange.Start.Value == FromDate && dateRange.End != null && dateRange.End.Value == ToDate)
        {
            return;
        }

        if (dateRange.Start != null)
        {
            FromDate = dateRange.Start.Value;
        }
        if (dateRange.End != null)
        {
            ToDate = dateRange.End.Value;
        }
        SetParametersToUrl();
    }

    protected void OnSearchByVendor(GetVendorDto selectedValue)
    {
        if (selectedValue == null)
        {
            VendorNameQuery = VendorNumberQuery = "";
            VendorFilter = new GetVendorDto();
        }
        else
        {
            VendorFilter = selectedValue;
            VendorNumberQuery = selectedValue.Number;
            VendorNameQuery = selectedValue.Name;
        }
        if (!IsVendor || !IsVendorContact) SetParametersToUrl();
    }

    protected void OnSearchByContact(GetContactDto selectedValue)
    {
        if (selectedValue == null)
        {
            ContactNumberQuery = "";
            ContactFilter = new GetContactDto();
        }
        else
        {
            ContactFilter = selectedValue;
            ContactNumberQuery = selectedValue.Number;
        }
        if (!IsVendor || !IsVendorContact)
        {
            SetParametersToUrl();
        }
    }

    protected async Task Reload() => await ListPoByUserRef.Reload();

    protected async Task<IEnumerable<GetVendorDto>> VendorSearch(string value, CancellationToken token)
    {
        var result = new List<GetVendorDto>();

        if (token.IsCancellationRequested) return result;

        var allItem = new GetVendorDto
        {
            Number = null, Name = L["ALL"], Blocked = 0
        };

        result.Add(allItem);

        if (string.IsNullOrEmpty(value))
        {
            return result;
        }
        var vendorNumber = value.Trim().Equals(L["ALL"], StringComparison.OrdinalIgnoreCase) ? string.Empty
            : value.Split('-')[0].Trim();

        var apiResponse = await VendorApiClient.GetVendorsByNumber(vendorNumber, token);

        if (!apiResponse.IsSuccessStatusCode || apiResponse.Result?.Any() != true)
        {
            return result;
        }
        foreach (var item in apiResponse.Result.Where(item => result.All(r => r.Number != item.Number)))
        {
            result.Add(item);
        }

        return result;
    }

    protected async Task<IEnumerable<GetContactDto>> ContactSearch(string value, CancellationToken token)
    {
        var result = new List<GetContactDto>();

        if (token.IsCancellationRequested)
        {
            return result;
        }

        var allItem = new GetContactDto
        {
            Number = L["ALL"], Name = L["ALL"], Block = false
        };

        result.Add(allItem);

        if (string.IsNullOrEmpty(value))
        {
            return result;
        }
        var vendorNumber = value.Trim().Equals(L["ALL"], StringComparison.OrdinalIgnoreCase) ? string.Empty
            : value.Split('-')[0].Trim();

        var apiResponse = await ContactApiClient.SearchContactAsync(vendorNumber, token);

        if (!apiResponse.IsSuccessStatusCode || apiResponse.Result?.Any() != true)
        {
            return result;
        }
        foreach (var item in apiResponse.Result.Where(item => result.All(r => r.Number != item.Number)))
        {
            result.Add(item);
        }

        return result;
    }

    private void OnSearchByStatus(PurchaseOrderFilerEnum status)
    {
        StatusFilter = (int)status;
        SelectedStatus = status;
        StatusFilter = status switch
        {
            PurchaseOrderFilerEnum.All => null,
            PurchaseOrderFilerEnum.NewCreate => (int)PurchaseOrderEnum.NewCreate,
            PurchaseOrderFilerEnum.Approve => (int)PurchaseOrderEnum.Approve,
            PurchaseOrderFilerEnum.PartiallyReceived => (int)PurchaseOrderEnum.PartiallyReceived,
            PurchaseOrderFilerEnum.Completed => (int)PurchaseOrderEnum.Completed,
            _ => throw new ArgumentOutOfRangeException(nameof(status), status, null)
        };
        StateHasChanged();
    }
    private static string GetMarginLabel(PurchaseOrderFilerEnum status) => status switch
    {
        PurchaseOrderFilerEnum.All => "Tất cả",
        PurchaseOrderFilerEnum.NewCreate => "Chưa approve",
        PurchaseOrderFilerEnum.Approve => "Đã approve",
        PurchaseOrderFilerEnum.CJL => "Đã gửi sang CJL",
        PurchaseOrderFilerEnum.PartiallyReceived => "Kho đã nhận 1 phần",
        PurchaseOrderFilerEnum.Completed => "Kho đã nhận đủ",
        _ => status.ToString()
    };

    protected void OnSearchByDescription(string value)
    {
        DescriptionQuery = value;
        SetParametersToUrl();
    }
    protected void OnSearchByPONumber(string value)
    {
        PONumberQuery = value;
        SetParametersToUrl();
    }
    protected void OnClearFilterByApproveStatus()
    {
        StatusFilter = null;
        SelectedStatus = PurchaseOrderFilerEnum.All;
        StateHasChanged();
    }

    protected void SetParametersToUrl()
    {
        var queries = BuildQueryParams();
        NavigationManager.NavigateTo("/po" + queries);
    }
    private string BuildQueryParams()
    {
        QueryParams
            .SetParam("fromDate", FromDate.ToString("yyyy-MM-dd"))
            .SetParam("toDate", ToDate.ToString("yyyy-MM-dd"))
            .SetParam("description", DescriptionQuery)
            .SetParam("poNumber", PONumberQuery)
            .SetParam("contactNumber", ContactNumberQuery)
            .SetParam("status", StatusQuery);

        if (IsVendor || IsVendorContact)
        {
            return NavigationManagerExtensions.ToQueryString(QueryParams);
        }
        QueryParams.SetParam("vendorName", VendorNameQuery);
        QueryParams.SetParam("vendorNumber", VendorNumberQuery);

        return NavigationManagerExtensions.ToQueryString(QueryParams);
    }
    protected bool IsUserAuthorized()
    {
        return IsPurchaseUser || IsAdmin || IsVendor || IsVendorContact || IsMKT;
    }
}
