﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Storage.Mapping;

public class PurchaseMappingProfile : Profile
{
    public PurchaseMappingProfile()
    {
        CreateMap<POLineAddOrUpdate, PurchaseOrderLine>().ReverseMap();
        CreateMap<POLineGetDto, PurchaseOrderLine>().ReverseMap();
        CreateMap<POHeaderGetDto, PurchaseOrderHeader>().ReverseMap();

        CreateMap<POLineGetDto, POLineAddOrUpdate>().ReverseMap();
    }
}
