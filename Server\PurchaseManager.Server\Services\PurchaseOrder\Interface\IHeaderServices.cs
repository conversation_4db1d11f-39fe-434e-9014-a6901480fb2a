﻿using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Server.Services.PurchaseOrder.Interface;

public interface IHeaderServices
{
    Task<ApiResponse> CreateHeader(string vendorNumber, string contactNumber);
    Task<ApiResponse> UpdateHeader(UpdatePOHeaderDto updatePOHeader);
    Task<ApiResponse> OpenDocument(string documentNumber);
    Task<ApiResponse> ChangeDocumentStatus(string documentNumber, PurchaseOrderEnum status);
    Task<ApiResponse> POAutoCreate();
}
