-- =============================================
-- Script: Cleanup complex columns from PromotionHeader (Optional)
-- Description: Remove complex columns that don't fit simplified Front Margin design
-- Date: 2025-01-15
-- WARNING: This will permanently delete data in these columns!
-- =============================================

USE [PurchaseManager] -- Replace with your database name
GO

PRINT '⚠️  This script will remove complex columns from PromotionHeader'
PRINT '⚠️  Only run this if you want to simplify the design'
PRINT ''

-- Uncomment the line below to enable cleanup (safety measure)
-- DECLARE @ENABLE_CLEANUP BIT = 1

IF NOT EXISTS (SELECT * FROM sys.objects WHERE name = 'ENABLE_CLEANUP')
BEGIN
    PRINT '❌ Cleanup is disabled for safety.'
    PRINT '❌ To enable cleanup, uncomment the DECLARE @ENABLE_CLEANUP line above.'
    PRINT ''
    PRINT '📋 Complex columns that would be removed:'
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'PromotionHeaders'
        AND COLUMN_NAME IN (
            'MaxBudgetAmount', 'UsedAmount', 'MinOrderValue', 'MaxOrderValue', 
            'MaxDiscountAmount', 'ApplicableDocTypes', 'ApplyToAllVendorItems',
            'RequireApprovalBeforeReward', 'DefaultPaymentMethod', 'DefaultPaymentTiming',
            'EvaluationPeriod', 'SpecialConditions'
        )
    ORDER BY COLUMN_NAME
    RETURN
END

PRINT 'Starting cleanup of complex columns...'

BEGIN TRANSACTION

BEGIN TRY
    -- Remove complex columns that don't fit simplified design
    
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'SpecialConditions')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [SpecialConditions]
        PRINT '✓ Removed SpecialConditions column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'EvaluationPeriod')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [EvaluationPeriod]
        PRINT '✓ Removed EvaluationPeriod column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'DefaultPaymentTiming')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [DefaultPaymentTiming]
        PRINT '✓ Removed DefaultPaymentTiming column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'DefaultPaymentMethod')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [DefaultPaymentMethod]
        PRINT '✓ Removed DefaultPaymentMethod column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'RequireApprovalBeforeReward')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [RequireApprovalBeforeReward]
        PRINT '✓ Removed RequireApprovalBeforeReward column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApplyToAllVendorItems')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [ApplyToAllVendorItems]
        PRINT '✓ Removed ApplyToAllVendorItems column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApplicableDocTypes')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [ApplicableDocTypes]
        PRINT '✓ Removed ApplicableDocTypes column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxDiscountAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [MaxDiscountAmount]
        PRINT '✓ Removed MaxDiscountAmount column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxOrderValue')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [MaxOrderValue]
        PRINT '✓ Removed MaxOrderValue column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MinOrderValue')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [MinOrderValue]
        PRINT '✓ Removed MinOrderValue column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'UsedAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [UsedAmount]
        PRINT '✓ Removed UsedAmount column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxBudgetAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [MaxBudgetAmount]
        PRINT '✓ Removed MaxBudgetAmount column'
    END

    COMMIT TRANSACTION
    PRINT ''
    PRINT '✅ Cleanup completed successfully!'

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT ''
    PRINT '❌ Error during cleanup:'
    PRINT ERROR_MESSAGE()
    PRINT ''
END CATCH

-- Verify the cleanup
PRINT ''
PRINT 'Verifying cleanup...'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'MaxBudgetAmount', 'UsedAmount', 'MinOrderValue', 'MaxOrderValue', 
        'MaxDiscountAmount', 'ApplicableDocTypes', 'ApplyToAllVendorItems',
        'RequireApprovalBeforeReward', 'DefaultPaymentMethod', 'DefaultPaymentTiming',
        'EvaluationPeriod', 'SpecialConditions'
    )
ORDER BY COLUMN_NAME

DECLARE @RemainingComplexColumns INT
SELECT @RemainingComplexColumns = COUNT(*)
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'MaxBudgetAmount', 'UsedAmount', 'MinOrderValue', 'MaxOrderValue', 
        'MaxDiscountAmount', 'ApplicableDocTypes', 'ApplyToAllVendorItems',
        'RequireApprovalBeforeReward', 'DefaultPaymentMethod', 'DefaultPaymentTiming',
        'EvaluationPeriod', 'SpecialConditions'
    )

IF @RemainingComplexColumns = 0
BEGIN
    PRINT ''
    PRINT '✅ All complex columns have been removed successfully!'
    PRINT ''
    PRINT '📋 Remaining essential columns:'
    SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'PromotionHeaders'
        AND COLUMN_NAME IN (
            'ProgramName', 'Description', 'VendorCode', 'StartDate', 'EndDate',
            'ProgramType', 'Status', 'Priority', 'AutoApply', 'ApprovalStatus'
        )
    ORDER BY COLUMN_NAME
END
ELSE
BEGIN
    PRINT ''
    PRINT '⚠️  Warning: ' + CAST(@RemainingComplexColumns AS VARCHAR(10)) + ' complex columns still remain!'
END

PRINT ''
PRINT '📋 Simplified PromotionHeader Design:'
PRINT '✓ Front Margin: Đơn giản với 3 cases, chỉ cần Priority + AutoApply'
PRINT '✓ Back Margin: Phức tạp với 5 loại, sẽ dùng PromotionCondition + PromotionReward'
PRINT '✓ Header: Chỉ chứa thông tin cơ bản, không có budget management'
PRINT ''
PRINT '🎯 Next Steps:'
PRINT '1. Update application code to remove references to deleted columns'
PRINT '2. Update DTOs and mapping profiles'
PRINT '3. Test Front Margin PO integration'
