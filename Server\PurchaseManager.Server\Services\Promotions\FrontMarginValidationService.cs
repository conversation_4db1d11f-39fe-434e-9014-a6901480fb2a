using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Server.Services.Promotions;

public interface IFrontMarginValidationService
{
    /// <summary>
    ///     Validate Front Margin entity
    /// </summary>
    List<string> ValidateEntity(PromotionFrontMargin entity);

    /// <summary>
    ///     Validate Front Margin DTO
    /// </summary>
    List<string> ValidateDto(CreatePromotionFrontMarginDto dto);

    /// <summary>
    ///     Validate Front Margin update DTO
    /// </summary>
    List<string> ValidateUpdateDto(UpdatePromotionFrontMarginDto dto);
}
public class FrontMarginValidationService : IFrontMarginValidationService
{
    public List<string> ValidateEntity(PromotionFrontMargin entity)
    {
        var errors = new List<string>();

        // Common validations
        if (string.IsNullOrWhiteSpace(entity.ItemNumber))
        {
            errors.Add("Mã sản phẩm không được để trống");
        }

        if (string.IsNullOrWhiteSpace(entity.ItemName))
        {
            errors.Add("Tên sản phẩm không được để trống");
        }

        if (string.IsNullOrWhiteSpace(entity.UnitOfMeasure))
        {
            errors.Add("Đơn vị tính không được để trống");
        }

        if (entity.LineNumber <= 0)
        {
            errors.Add("Số dòng phải lớn hơn 0");
        }

        // Discount type specific validations
        switch (entity.DiscountType)
        {
            case 1:// Percentage
                ValidatePercentageDiscount(entity, errors);
                break;
            case 2:// Fixed Amount
                ValidateFixedAmountDiscount(entity, errors);
                break;
            case 3:// Same Item Gift
                ValidateSameItemGift(entity, errors);
                break;
            case 4:// Different Item Gift
                ValidateDifferentItemGift(entity, errors);
                break;
            default:
                errors.Add("Loại chiết khấu không hợp lệ (1-4)");
                break;
        }

        // Optional field validations
        if (entity.MinimumQuantity is <= 0)
        {
            errors.Add("Số lượng tối thiểu phải lớn hơn 0");
        }

        if (entity.MinimumAmount is <= 0)
        {
            errors.Add("Giá trị tối thiểu phải lớn hơn 0");
        }

        if (entity.MaximumDiscountAmount is <= 0)
        {
            errors.Add("Giá trị chiết khấu tối đa phải lớn hơn 0");
        }

        return errors;
    }

    public List<string> ValidateDto(CreatePromotionFrontMarginDto dto)
    {
        var errors = new List<string>();

        // Common validations
        if (string.IsNullOrWhiteSpace(dto.ProgramNumber))
        {
            errors.Add("Mã chương trình không được để trống");
        }

        if (string.IsNullOrWhiteSpace(dto.ItemNumber))
        {
            errors.Add("Mã sản phẩm không được để trống");
        }

        if (string.IsNullOrWhiteSpace(dto.ItemName))
        {
            errors.Add("Tên sản phẩm không được để trống");
        }

        if (string.IsNullOrWhiteSpace(dto.UnitOfMeasure))
        {
            errors.Add("Đơn vị tính không được để trống");
        }

        // Discount type specific validations
        switch (dto.DiscountType)
        {
            case 1:// Percentage
                ValidatePercentageDiscountDto(dto, errors);
                break;
            case 2:// Fixed Amount
                ValidateFixedAmountDiscountDto(dto, errors);
                break;
            case 3:// Same Item Gift
                ValidateSameItemGiftDto(dto, errors);
                break;
            case 4:// Different Item Gift
                ValidateDifferentItemGiftDto(dto, errors);
                break;
            default:
                errors.Add("Loại chiết khấu không hợp lệ (1-4)");
                break;
        }

        return errors;
    }

    public List<string> ValidateUpdateDto(UpdatePromotionFrontMarginDto dto)
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(dto.UnitOfMeasure))
        {
            errors.Add("Đơn vị tính không được để trống");
        }

        // Discount type specific validations
        switch (dto.DiscountType)
        {
            case 1:// Percentage
                ValidatePercentageDiscountUpdateDto(dto, errors);
                break;
            case 2:// Fixed Amount
                ValidateFixedAmountDiscountUpdateDto(dto, errors);
                break;
            case 3:// Same Item Gift
                ValidateSameItemGiftUpdateDto(dto, errors);
                break;
            case 4:// Different Item Gift
                ValidateDifferentItemGiftUpdateDto(dto, errors);
                break;
            default:
                errors.Add("Loại chiết khấu không hợp lệ (1-4)");
                break;
        }

        return errors;
    }

    #region Entity Validations
    private void ValidatePercentageDiscount(PromotionFrontMargin entity, List<string> errors)
    {
        if (entity.DiscountPercentage <= 0 || entity.DiscountPercentage > 100)
        {
            errors.Add("Phần trăm chiết khấu phải từ 0.01 đến 100");
        }

        // These fields should be null for percentage discount
        if (entity.FixedDiscountAmount.HasValue)
        {
            errors.Add("Chiết khấu theo phần trăm không được có số tiền cố định");
        }

        if (entity.BuyQuantity.HasValue || entity.GiftQuantity.HasValue)
        {
            errors.Add("Chiết khấu theo phần trăm không được có thông tin mua tặng");
        }

        if (!string.IsNullOrEmpty(entity.GiftItemNumber))
        {
            errors.Add("Chiết khấu theo phần trăm không được có sản phẩm tặng");
        }
    }

    private void ValidateFixedAmountDiscount(PromotionFrontMargin entity, List<string> errors)
    {
        if (!entity.FixedDiscountAmount.HasValue || entity.FixedDiscountAmount.Value <= 0)
        {
            errors.Add("Số tiền chiết khấu cố định phải lớn hơn 0");
        }

        // DiscountPercentage should be 0
        if (entity.DiscountPercentage != 0)
        {
            errors.Add("Chiết khấu số tiền cố định không được có phần trăm chiết khấu");
        }

        // These fields should be null
        if (entity.BuyQuantity.HasValue || entity.GiftQuantity.HasValue)
        {
            errors.Add("Chiết khấu số tiền cố định không được có thông tin mua tặng");
        }

        if (!string.IsNullOrEmpty(entity.GiftItemNumber))
        {
            errors.Add("Chiết khấu số tiền cố định không được có sản phẩm tặng");
        }
    }

    private void ValidateSameItemGift(PromotionFrontMargin entity, List<string> errors)
    {
        if (!entity.BuyQuantity.HasValue || entity.BuyQuantity.Value <= 0)
        {
            errors.Add("Số lượng mua phải lớn hơn 0");
        }

        if (!entity.GiftQuantity.HasValue || entity.GiftQuantity.Value <= 0)
        {
            errors.Add("Số lượng tặng phải lớn hơn 0");
        }

        // DiscountPercentage should be 0
        if (entity.DiscountPercentage != 0)
        {
            errors.Add("Mua hàng tặng hàng cùng loại không được có phần trăm chiết khấu");
        }

        // These fields should be null
        if (entity.FixedDiscountAmount.HasValue)
        {
            errors.Add("Mua hàng tặng hàng cùng loại không được có số tiền cố định");
        }

        if (!string.IsNullOrEmpty(entity.GiftItemNumber))
        {
            errors.Add("Mua hàng tặng hàng cùng loại không được có sản phẩm tặng khác");
        }
    }

    private void ValidateDifferentItemGift(PromotionFrontMargin entity, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(entity.GiftItemNumber))
        {
            errors.Add("Mã sản phẩm tặng không được để trống");
        }

        if (string.IsNullOrWhiteSpace(entity.GiftItemName))
        {
            errors.Add("Tên sản phẩm tặng không được để trống");
        }

        if (string.IsNullOrWhiteSpace(entity.GiftItemUOM))
        {
            errors.Add("Đơn vị tính sản phẩm tặng không được để trống");
        }

        if (!entity.GiftItemQuantity.HasValue || entity.GiftItemQuantity.Value <= 0)
        {
            errors.Add("Số lượng sản phẩm tặng phải lớn hơn 0");
        }

        // DiscountPercentage should be 0
        if (entity.DiscountPercentage != 0)
        {
            errors.Add("Tặng hàng khác loại không được có phần trăm chiết khấu");
        }

        // These fields should be null
        if (entity.FixedDiscountAmount.HasValue)
        {
            errors.Add("Tặng hàng khác loại không được có số tiền cố định");
        }

        if (entity.BuyQuantity.HasValue || entity.GiftQuantity.HasValue)
        {
            errors.Add("Tặng hàng khác loại không được có thông tin mua tặng cùng loại");
        }
    }
    #endregion

    #region DTO Validations
    private void ValidatePercentageDiscountDto(CreatePromotionFrontMarginDto dto, List<string> errors)
    {
        if (dto.DiscountPercentage <= 0 || dto.DiscountPercentage > 100)
        {
            errors.Add("Phần trăm chiết khấu phải từ 0.01 đến 100");
        }
    }

    private void ValidateFixedAmountDiscountDto(CreatePromotionFrontMarginDto dto, List<string> errors)
    {
        if (!dto.FixedDiscountAmount.HasValue || dto.FixedDiscountAmount.Value <= 0)
        {
            errors.Add("Số tiền chiết khấu cố định phải lớn hơn 0");
        }
    }

    private void ValidateSameItemGiftDto(CreatePromotionFrontMarginDto dto, List<string> errors)
    {
        if (!dto.BuyQuantity.HasValue || dto.BuyQuantity.Value <= 0)
        {
            errors.Add("Số lượng mua phải lớn hơn 0");
        }

        if (!dto.GiftQuantity.HasValue || dto.GiftQuantity.Value <= 0)
        {
            errors.Add("Số lượng tặng phải lớn hơn 0");
        }
    }

    private void ValidateDifferentItemGiftDto(CreatePromotionFrontMarginDto dto, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(dto.GiftItemNumber))
        {
            errors.Add("Mã sản phẩm tặng không được để trống");
        }

        if (string.IsNullOrWhiteSpace(dto.GiftItemName))
        {
            errors.Add("Tên sản phẩm tặng không được để trống");
        }

        if (string.IsNullOrWhiteSpace(dto.GiftItemUOM))
        {
            errors.Add("Đơn vị tính sản phẩm tặng không được để trống");
        }

        if (!dto.GiftItemQuantity.HasValue || dto.GiftItemQuantity.Value <= 0)
        {
            errors.Add("Số lượng sản phẩm tặng phải lớn hơn 0");
        }
    }

    private void ValidatePercentageDiscountUpdateDto(UpdatePromotionFrontMarginDto dto, List<string> errors)
    {
        if (dto.DiscountPercentage <= 0 || dto.DiscountPercentage > 100)
        {
            errors.Add("Phần trăm chiết khấu phải từ 0.01 đến 100");
        }
    }

    private void ValidateFixedAmountDiscountUpdateDto(UpdatePromotionFrontMarginDto dto, List<string> errors)
    {
        if (!dto.FixedDiscountAmount.HasValue || dto.FixedDiscountAmount.Value <= 0)
        {
            errors.Add("Số tiền chiết khấu cố định phải lớn hơn 0");
        }
    }

    private void ValidateSameItemGiftUpdateDto(UpdatePromotionFrontMarginDto dto, List<string> errors)
    {
        if (!dto.BuyQuantity.HasValue || dto.BuyQuantity.Value <= 0)
        {
            errors.Add("Số lượng mua phải lớn hơn 0");
        }

        if (!dto.GiftQuantity.HasValue || dto.GiftQuantity.Value <= 0)
        {
            errors.Add("Số lượng tặng phải lớn hơn 0");
        }
    }

    private void ValidateDifferentItemGiftUpdateDto(UpdatePromotionFrontMarginDto dto, List<string> errors)
    {
        if (string.IsNullOrWhiteSpace(dto.GiftItemNumber))
        {
            errors.Add("Mã sản phẩm tặng không được để trống");
        }

        if (string.IsNullOrWhiteSpace(dto.GiftItemName))
        {
            errors.Add("Tên sản phẩm tặng không được để trống");
        }

        if (string.IsNullOrWhiteSpace(dto.GiftItemUOM))
        {
            errors.Add("Đơn vị tính sản phẩm tặng không được để trống");
        }

        if (!dto.GiftItemQuantity.HasValue || dto.GiftItemQuantity.Value <= 0)
        {
            errors.Add("Số lượng sản phẩm tặng phải lớn hơn 0");
        }
    }
    #endregion
}
