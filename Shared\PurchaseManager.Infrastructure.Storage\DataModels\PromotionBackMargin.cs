﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

//TODO: Add comments and documentation for the PromotionBackMargin class
public class PromotionBackMargin : FullTrackingEntity
{
    [StringLength(50)]
    public string ProgramNumber { get; set; } = null!;

    public int LineNumber { get; set; }

    [StringLength(50)]
    public string ItemNumber { get; set; } = null!;

    [StringLength(250)]
    public string ItemName { get; set; } = null!;

    [StringLength(50)]
    public string UnitOfMeasure { get; set; } = null!;

    [StringLength(500)]
    public string? Notes { get; set; }

    [ForeignKey("ProgramNumber")]
    public PromotionHeader PromotionHeader { get; set; } = null!;
}
