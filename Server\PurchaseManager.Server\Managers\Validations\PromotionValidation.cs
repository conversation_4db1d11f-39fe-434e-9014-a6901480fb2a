﻿using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
namespace PurchaseManager.Server.Managers.Validations;

public class PromotionValidation
{
    /// <summary>
    /// Validates promotion header for editing operations
    /// </summary>
    /// <param name="promotionHeader">The promotion header to validate</param>
    /// <param name="allowedStatuses">Optional: specific statuses to allow (if null, will use default validation)</param>
    /// <param name="requireFrontMarginType">Whether to require ProgramType = 1 (FrontMargin)</param>
    /// <returns>ApiResponse with error if validation fails, null if validation passes</returns>
    public static ApiResponse? ValidatePromotionHeaderForEditing(PromotionHeader promotionHeader,
        int[]? allowedStatuses = null, bool requireFrontMarginType = false)
    {
        // Validate promotion header status - only allow editing if status is valid
        // Status: 1=Draft, 2=Active, 3=Inactive, 4=Expired
        if (allowedStatuses == null)
        {
            switch (promotionHeader.Status)
            {
                // Expired
                case 4:
                    return ApiResponse.S400("Cannot modify expired promotion");
                // Inactive
                case 3:
                    return ApiResponse.S400("Cannot modify inactive promotion");
            }

        }
        else
        {
            // Use specific allowed statuses
            if (!allowedStatuses.Contains(promotionHeader.Status))
            {
                var statusNames = GetStatusName(promotionHeader.Status);
                return ApiResponse.S400($"Cannot modify promotion with status: {statusNames}");
            }
        }

        // Check if promotion type is FrontMargin (if required)
        if (requireFrontMarginType && promotionHeader.ProgramType != 1)// 1=FrontMargin
        {
            return ApiResponse.S400("This promotion is not a Front Margin type promotion");
        }

        // Validate date range
        // if (DateTime.Now < promotionHeader.StartDate)
        // {
        //     return ApiResponse.S400("Promotion has not started yet");
        // }

        return DateTime.Now > promotionHeader.EndDate ? ApiResponse.S400("Promotion has already ended") :
            // All validations passed
            null;
    }

    /// <summary>
    /// Get status name for display purposes
    /// </summary>
    private static string GetStatusName(int status)
    {
        return status switch
        {
            1 => "Draft",
            2 => "Active",
            3 => "Inactive",
            4 => "Expired",
            _ => "Unknown"
        };
    }
}
