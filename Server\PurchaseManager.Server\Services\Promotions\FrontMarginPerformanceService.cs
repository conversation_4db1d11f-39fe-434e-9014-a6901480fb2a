using System.Diagnostics;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Services.Promotions;

public class FrontMarginPerformanceService : IFrontMarginPerformanceService
{
    private readonly ApplicationDbContext _context;
    private readonly IFrontMarginCalculationService _calculationService;
    private readonly IFrontMarginCacheService _cacheService;
    private readonly ILogger<FrontMarginPerformanceService> _logger;

    public FrontMarginPerformanceService(
        ApplicationDbContext context,
        IFrontMarginCalculationService calculationService,
        IFrontMarginCacheService cacheService,
        ILogger<FrontMarginPerformanceService> logger)
    {
        _context = context;
        _calculationService = calculationService;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<PerformanceMetrics> MonitorCalculationPerformanceAsync(string vendorCode, int itemCount)
    {
        var stopwatch = Stopwatch.StartNew();
        var metrics = new PerformanceMetrics
        {
            VendorCode = vendorCode,
            ItemCount = itemCount,
            StartTime = DateTime.UtcNow
        };

        try
        {
            // Test cache performance
            var cacheStopwatch = Stopwatch.StartNew();
            var promotions = await _cacheService.GetActivePromotionsAsync(vendorCode);
            cacheStopwatch.Stop();
            
            metrics.CacheLoadTime = cacheStopwatch.ElapsedMilliseconds;
            metrics.PromotionCount = promotions.Count;

            // Test calculation performance with sample data
            var calculationStopwatch = Stopwatch.StartNew();

            for (var i = 0; i < itemCount; i++)
            {
                var sampleLine = CreateSamplePOLine($"ITEM{i:D4}", 100, 10000);
                await _calculationService.CalculateLineDiscountAsync(sampleLine, promotions);
            }
            
            calculationStopwatch.Stop();
            metrics.CalculationTime = calculationStopwatch.ElapsedMilliseconds;
            metrics.AverageCalculationTimePerItem = (double)metrics.CalculationTime / itemCount;

            stopwatch.Stop();
            metrics.TotalTime = stopwatch.ElapsedMilliseconds;
            metrics.EndTime = DateTime.UtcNow;

            _logger.LogInformation("Performance monitoring completed for vendor {VendorCode}: {TotalTime}ms for {ItemCount} items", 
                vendorCode, metrics.TotalTime, itemCount);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error monitoring calculation performance for vendor {VendorCode}", vendorCode);
            stopwatch.Stop();
            metrics.TotalTime = stopwatch.ElapsedMilliseconds;
            metrics.ErrorMessage = ex.Message;
            return metrics;
        }
    }

    public async Task<FrontMarginPerformanceStats> GetPerformanceStatsAsync()
    {
        try
        {
            var stats = new FrontMarginPerformanceStats();

            // Database statistics
            stats.DatabaseStats = new DatabaseStats
            {
                TotalFrontMargins = await _context.PromotionFrontMargins.CountAsync(),
                ActiveFrontMargins = await _context.PromotionFrontMargins.CountAsync(p => p.Status == 2),
                TotalPromotionHeaders = await _context.PromotionHeaders.CountAsync(h => h.ProgramType == 1),
                ActivePromotionHeaders = await _context.PromotionHeaders.CountAsync(h => h.ProgramType == 1 && h.Status == 2)
            };

            // Cache statistics
            stats.CacheStats = _cacheService.GetCacheStats();

            // Query performance statistics
            stats.QueryStats = await GetQueryPerformanceStatsAsync();

            // System resource usage
            stats.SystemStats = GetSystemStats();

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting performance statistics");
            return new FrontMarginPerformanceStats
            {
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task OptimizeDatabaseAsync()
    {
        try
        {
            _logger.LogInformation("Starting database optimization for Front Margin");

            // Update statistics
            await _context.Database.ExecuteSqlRawAsync("UPDATE STATISTICS PromotionFrontMargins");
            await _context.Database.ExecuteSqlRawAsync("UPDATE STATISTICS PromotionHeaders");

            // Rebuild indexes if needed (be careful in production)
            var indexMaintenanceQueries = new[]
            {
                "ALTER INDEX IX_PromotionFrontMargins_DiscountType ON PromotionFrontMargins REORGANIZE",
                "ALTER INDEX IX_PromotionFrontMargins_GiftItemNumber ON PromotionFrontMargins REORGANIZE"
            };

            foreach (var query in indexMaintenanceQueries)
            {
                try
                {
                    await _context.Database.ExecuteSqlRawAsync(query);
                    _logger.LogInformation("Executed index maintenance: {Query}", query);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to execute index maintenance: {Query}", query);
                }
            }

            _logger.LogInformation("Database optimization completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error optimizing database");
            throw;
        }
    }

    public async Task<List<SlowQueryInfo>> GetSlowQueriesAsync()
    {
        // This would analyze slow queries related to Front Margin
        // For now, return sample data
        return new List<SlowQueryInfo>
        {
            new SlowQueryInfo
            {
                QueryType = "GetActivePromotions",
                AverageExecutionTime = 150,
                ExecutionCount = 1000,
                LastExecuted = DateTime.UtcNow.AddMinutes(-5),
                Recommendation = "Consider adding index on (VendorCode, Status, StartDate, EndDate)"
            },
            new SlowQueryInfo
            {
                QueryType = "CalculateLineDiscount",
                AverageExecutionTime = 50,
                ExecutionCount = 5000,
                LastExecuted = DateTime.UtcNow.AddMinutes(-1),
                Recommendation = "Query performance is acceptable"
            }
        };
    }

    public async Task<BenchmarkResult> BenchmarkCalculationEngineAsync()
    {
        var result = new BenchmarkResult
        {
            StartTime = DateTime.UtcNow
        };

        try
        {
            var testScenarios = new[]
            {
                new { Name = "Small Order (10 items)", ItemCount = 10, Iterations = 100 },
                new { Name = "Medium Order (50 items)", ItemCount = 50, Iterations = 50 },
                new { Name = "Large Order (200 items)", ItemCount = 200, Iterations = 10 },
                new { Name = "Bulk Order (1000 items)", ItemCount = 1000, Iterations = 5 }
            };

            foreach (var scenario in testScenarios)
            {
                var scenarioResult = await BenchmarkScenarioAsync(scenario.Name, scenario.ItemCount, scenario.Iterations);
                result.ScenarioResults.Add(scenarioResult);
            }

            result.EndTime = DateTime.UtcNow;
            result.TotalDuration = result.EndTime - result.StartTime;

            _logger.LogInformation("Benchmark completed in {Duration}", result.TotalDuration);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running benchmark");
            result.ErrorMessage = ex.Message;
            return result;
        }
    }

    #region Private Methods

    private async Task<BenchmarkScenarioResult> BenchmarkScenarioAsync(string scenarioName, int itemCount, int iterations)
    {
        var result = new BenchmarkScenarioResult
        {
            ScenarioName = scenarioName,
            ItemCount = itemCount,
            Iterations = iterations
        };

        var executionTimes = new List<long>();
        var vendorCode = "BENCHMARK_VENDOR";

        // Get sample promotions
        var promotions = await _cacheService.GetActivePromotionsAsync(vendorCode);

        for (int i = 0; i < iterations; i++)
        {
            var stopwatch = Stopwatch.StartNew();

            for (int j = 0; j < itemCount; j++)
            {
                var sampleLine = CreateSamplePOLine($"ITEM{j:D4}", 100, 10000);
                await _calculationService.CalculateLineDiscountAsync(sampleLine, promotions);
            }

            stopwatch.Stop();
            executionTimes.Add(stopwatch.ElapsedMilliseconds);
        }

        result.MinExecutionTime = executionTimes.Min();
        result.MaxExecutionTime = executionTimes.Max();
        result.AverageExecutionTime = executionTimes.Average();
        result.MedianExecutionTime = GetMedian(executionTimes);
        result.TotalExecutionTime = executionTimes.Sum();

        return result;
    }

    private POLineGetDto CreateSamplePOLine(string itemNumber, decimal quantity, decimal unitCost)
    {
        return new POLineGetDto
        {
            ItemNumber = itemNumber,
            Description = $"Sample item {itemNumber}",
            Quantity = quantity,
            UnitCost = unitCost,
            UnitOfMeasure = "PCS",
            Amount = quantity * unitCost
        };
    }

    private async Task<QueryPerformanceStats> GetQueryPerformanceStatsAsync()
    {
        // This would analyze actual query performance
        // For now, return sample data
        return new QueryPerformanceStats
        {
            AverageQueryTime = 75,
            SlowQueryCount = 2,
            TotalQueries = 10000,
            CacheHitRatio = 85.5
        };
    }

    private SystemStats GetSystemStats()
    {
        var process = Process.GetCurrentProcess();
        
        return new SystemStats
        {
            MemoryUsage = process.WorkingSet64 / 1024 / 1024, // MB
            CpuUsage = 0, // Would need performance counter for accurate CPU usage
            ThreadCount = process.Threads.Count,
            HandleCount = process.HandleCount
        };
    }

    private double GetMedian(List<long> values)
    {
        var sorted = values.OrderBy(x => x).ToList();
        int count = sorted.Count;
        
        if (count % 2 == 0)
        {
            return (sorted[count / 2 - 1] + sorted[count / 2]) / 2.0;
        }
        else
        {
            return sorted[count / 2];
        }
    }

    #endregion
}

#region Performance Models

public class PerformanceMetrics
{
    public string VendorCode { get; set; } = string.Empty;
    public int ItemCount { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public long TotalTime { get; set; }
    public long CacheLoadTime { get; set; }
    public long CalculationTime { get; set; }
    public double AverageCalculationTimePerItem { get; set; }
    public int PromotionCount { get; set; }
    public string? ErrorMessage { get; set; }
}

public class FrontMarginPerformanceStats
{
    public DatabaseStats DatabaseStats { get; set; } = new();
    public FrontMarginCacheStats CacheStats { get; set; } = new();
    public QueryPerformanceStats QueryStats { get; set; } = new();
    public SystemStats SystemStats { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

public class DatabaseStats
{
    public int TotalFrontMargins { get; set; }
    public int ActiveFrontMargins { get; set; }
    public int TotalPromotionHeaders { get; set; }
    public int ActivePromotionHeaders { get; set; }
}

public class QueryPerformanceStats
{
    public double AverageQueryTime { get; set; }
    public int SlowQueryCount { get; set; }
    public int TotalQueries { get; set; }
    public double CacheHitRatio { get; set; }
}

public class SystemStats
{
    public long MemoryUsage { get; set; }
    public double CpuUsage { get; set; }
    public int ThreadCount { get; set; }
    public int HandleCount { get; set; }
}

public class SlowQueryInfo
{
    public string QueryType { get; set; } = string.Empty;
    public double AverageExecutionTime { get; set; }
    public int ExecutionCount { get; set; }
    public DateTime LastExecuted { get; set; }
    public string Recommendation { get; set; } = string.Empty;
}

public class BenchmarkResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public TimeSpan TotalDuration { get; set; }
    public List<BenchmarkScenarioResult> ScenarioResults { get; set; } = new();
    public string? ErrorMessage { get; set; }
}

public class BenchmarkScenarioResult
{
    public string ScenarioName { get; set; } = string.Empty;
    public int ItemCount { get; set; }
    public int Iterations { get; set; }
    public long MinExecutionTime { get; set; }
    public long MaxExecutionTime { get; set; }
    public double AverageExecutionTime { get; set; }
    public double MedianExecutionTime { get; set; }
    public long TotalExecutionTime { get; set; }
}

#endregion
