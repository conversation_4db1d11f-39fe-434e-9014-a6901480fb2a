using PurchaseManager.Infrastructure.Server.Models;
namespace PurchaseManager.Infrastructure.Server;

/// <summary>
///     Interface cho CJL GR Manager - Xử lý flow GR to PO direct (logic mới: quét từ GR)
///     Không còn tạo RO, chỉ cập nhật trạng thái PO dựa trên GR data
/// </summary>
public interface ICJLGRManager
{
    /// <summary>
    ///     Cập nhật trạng thái PO dựa trên data GR (logic mới: quét từ GR để tối ưu performance)
    /// </summary>
    /// <param name="poNumber">PO Number cần cập nhật, null = quét tất cả GR và cập nhật PO tương ứng</param>
    /// <returns>Kết quả cập nhật</returns>
    Task<ApiResponse> UpdatePOStatusByGRDataAsync(string poNumber = null);

    /// <summary>
    ///     Helper method: Ki<PERSON><PERSON> tra trạng thái GR cho một PO cụ thể
    /// </summary>
    /// <param name="poNumber">PO Number cần kiểm tra</param>
    /// <returns>Thông tin chi tiết về GR status</returns>
    Task<ApiResponse> CheckGRStatusForPOAsync(string poNumber);
}
