using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Storage;

namespace PurchaseManager.Server.Services.Promotions;

public interface IFrontMarginCalculationService
{
    /// <summary>
    /// Calculate Front Margin discount for a PO line
    /// </summary>
    Task<FrontMarginCalculationResult> CalculateLineDiscountAsync(
        POLineGetDto poLine,
        List<PromotionFrontMargin> applicablePromotions);

    /// <summary>
    /// Apply Front Margin to entire PO
    /// </summary>
    Task<POFrontMarginResult> ApplyFrontMarginToPOAsync(
        POHeaderGetDto poHeader,
        List<POLineGetDto> poLines);

    /// <summary>
    /// Get applicable promotions for PO line
    /// </summary>
    Task<List<PromotionFrontMargin>> GetApplicablePromotionsAsync(
        POHeaderGetDto poHeader,
        POLineGetDto poLine);
}

public class FrontMarginCalculationService : IFrontMarginCalculationService
{
    private readonly IMapper _mapper;
    private readonly ApplicationDbContext _context;

    public FrontMarginCalculationService(IMapper mapper, ApplicationDbContext context)
    {
        _mapper = mapper;
        _context = context;
    }

    public async Task<FrontMarginCalculationResult> CalculateLineDiscountAsync(
        POLineGetDto poLine,
        List<PromotionFrontMargin> applicablePromotions)
    {
        var result = new FrontMarginCalculationResult
        {
            OriginalUnitCost = poLine.UnitCost,
            OriginalAmount = poLine.UnitCost * poLine.Quantity
        };

        foreach (var promotion in applicablePromotions)
        {
            var discountResult = promotion.DiscountType switch
            {
                1 => CalculatePercentageDiscount(poLine, promotion),
                2 => CalculateFixedAmountDiscount(poLine, promotion),
                3 => CalculateSameItemGift(poLine, promotion),
                4 => CalculateDifferentItemGift(poLine, promotion),
                _ => new DiscountCalculationResult()
            };

            result.AppliedPromotions.Add(new AppliedPromotionInfo
            {
                PromotionNumber = promotion.ProgramNumber,
                DiscountType = promotion.DiscountType,
                DiscountAmount = discountResult.DiscountAmount,
                NewUnitCost = discountResult.NewUnitCost,
                GiftLines = discountResult.GiftLines
            });

            // Apply the best discount (highest savings)
            if (discountResult.DiscountAmount > result.TotalDiscountAmount)
            {
                result.TotalDiscountAmount = discountResult.DiscountAmount;
                result.FinalUnitCost = discountResult.NewUnitCost;
                result.BestPromotionNumber = promotion.ProgramNumber;
                result.GiftLines = discountResult.GiftLines;
            }
        }

        result.FinalAmount = result.FinalUnitCost * poLine.Quantity;
        result.TotalSavings = result.OriginalAmount - result.FinalAmount;

        return result;
    }

    /// <summary>
    /// Case I.1: Percentage Discount
    /// Công thức: Giá Mua = Nguyên giá × (1 - % chiết khấu)
    /// </summary>
    private DiscountCalculationResult CalculatePercentageDiscount(
        POLineGetDto poLine,
        PromotionFrontMargin promotion)
    {
        var discountPercent = promotion.DiscountPercentage / 100;
        var newUnitCost = poLine.UnitCost * (1 - discountPercent);
        var discountAmount = (poLine.UnitCost - newUnitCost) * poLine.Quantity;

        // Apply maximum discount limit if set
        if (promotion.MaximumDiscountAmount.HasValue && discountAmount > promotion.MaximumDiscountAmount.Value)
        {
            discountAmount = promotion.MaximumDiscountAmount.Value;
            newUnitCost = poLine.UnitCost - (discountAmount / poLine.Quantity);
        }

        return new DiscountCalculationResult
        {
            NewUnitCost = newUnitCost,
            DiscountAmount = discountAmount,
            CalculationMethod = "Percentage Discount",
            Details = $"Original: {poLine.UnitCost:C}, Discount: {promotion.DiscountPercentage}%, New: {newUnitCost:C}"
        };
    }

    /// <summary>
    /// Case I.2: Fixed Amount Discount
    /// Công thức: Nguyên giá - (Tổng tiền CK / Tổng giá trị ĐH × Giá trị item)
    /// </summary>
    private DiscountCalculationResult CalculateFixedAmountDiscount(
        POLineGetDto poLine,
        PromotionFrontMargin promotion)
    {
        if (!promotion.FixedDiscountAmount.HasValue)
            return new DiscountCalculationResult();

        // For simplification, apply fixed discount proportionally to this line
        // In real implementation, this would need total PO context
        var itemValue = poLine.UnitCost * poLine.Quantity;
        var discountAmount = promotion.FixedDiscountAmount.Value;

        // Apply maximum discount limit if set
        if (promotion.MaximumDiscountAmount.HasValue && discountAmount > promotion.MaximumDiscountAmount.Value)
            discountAmount = promotion.MaximumDiscountAmount.Value;

        var newUnitCost = Math.Max(0, poLine.UnitCost - (discountAmount / poLine.Quantity));

        return new DiscountCalculationResult
        {
            NewUnitCost = newUnitCost,
            DiscountAmount = discountAmount,
            CalculationMethod = "Fixed Amount Discount",
            Details = $"Fixed discount: {promotion.FixedDiscountAmount:C}, Applied: {discountAmount:C}"
        };
    }

    /// <summary>
    /// Case II: Same Item Gift (Buy X Get Y Free)
    /// Công thức: Giá Mua = Tổng giá trị / Tổng số lượng
    /// </summary>
    private DiscountCalculationResult CalculateSameItemGift(
        POLineGetDto poLine,
        PromotionFrontMargin promotion)
    {
        if (!promotion.BuyQuantity.HasValue || !promotion.GiftQuantity.HasValue)
            return new DiscountCalculationResult();

        var buyQty = promotion.BuyQuantity.Value;
        var giftQty = promotion.GiftQuantity.Value;

        // Calculate how many gift sets can be applied
        var giftSets = Math.Floor(poLine.Quantity / buyQty);
        var totalGiftQuantity = giftSets * giftQty;

        if (totalGiftQuantity > 0)
        {
            var totalQuantityIncludingGifts = poLine.Quantity + totalGiftQuantity;
            var newUnitCost = (poLine.UnitCost * poLine.Quantity) / totalQuantityIncludingGifts;
            var discountAmount = (poLine.UnitCost - newUnitCost) * poLine.Quantity;

            // Apply maximum discount limit if set
            if (promotion.MaximumDiscountAmount.HasValue && discountAmount > promotion.MaximumDiscountAmount.Value)
            {
                discountAmount = promotion.MaximumDiscountAmount.Value;
                newUnitCost = poLine.UnitCost - (discountAmount / poLine.Quantity);
                // Recalculate gift quantity based on max discount
                totalGiftQuantity = Math.Min(totalGiftQuantity,
                    (discountAmount * totalQuantityIncludingGifts / (poLine.UnitCost * poLine.Quantity)) - poLine.Quantity);
            }

            return new DiscountCalculationResult
            {
                NewUnitCost = newUnitCost,
                DiscountAmount = discountAmount,
                CalculationMethod = "Same Item Gift",
                Details = $"Buy {buyQty}, Get {giftQty} free. Gift sets: {giftSets}, Total gifts: {totalGiftQuantity}",
                GiftLines = new List<POLineGetDto>
                {
                    new POLineGetDto
                    {
                        ItemNumber = poLine.ItemNumber,
                        Description = $"{poLine.Description} (GIFT)",
                        Quantity = totalGiftQuantity,
                        UnitOfMeasure = poLine.UnitOfMeasure,
                        UnitCost = 0,
                        UnitPrice = 0,
                        Amount = 0,
                        DocumentType = 2, // Promotional item
                        LotNo = "KM"
                    }
                }
            };
        }

        return new DiscountCalculationResult();
    }

    /// <summary>
    /// Case III: Different Item Gift
    /// Tặng hàng khác loại (không ảnh hưởng giá mua)
    /// </summary>
    private DiscountCalculationResult CalculateDifferentItemGift(
        POLineGetDto poLine,
        PromotionFrontMargin promotion)
    {
        if (string.IsNullOrEmpty(promotion.GiftItemNumber) || !promotion.GiftItemQuantity.HasValue)
            return new DiscountCalculationResult();

        // Check if minimum quantity/amount conditions are met
        if (promotion.MinimumQuantity.HasValue && poLine.Quantity < promotion.MinimumQuantity.Value)
            return new DiscountCalculationResult();

        if (promotion.MinimumAmount.HasValue && (poLine.UnitCost * poLine.Quantity) < promotion.MinimumAmount.Value)
            return new DiscountCalculationResult();

        return new DiscountCalculationResult
        {
            NewUnitCost = poLine.UnitCost, // No change to unit cost
            DiscountAmount = 0, // No monetary discount
            CalculationMethod = "Different Item Gift",
            Details = $"Gift: {promotion.GiftItemQuantity} {promotion.GiftItemUOM} {promotion.GiftItemName}",
            GiftLines = new List<POLineGetDto>
            {
                new POLineGetDto
                {
                    ItemNumber = promotion.GiftItemNumber,
                    Description = $"{promotion.GiftItemName} (GIFT)",
                    Quantity = promotion.GiftItemQuantity.Value,
                    UnitOfMeasure = promotion.GiftItemUOM ?? "",
                    UnitCost = 0,
                    UnitPrice = 0,
                    Amount = 0,
                    DocumentType = 2, // Promotional item
                    LotNo = "KM"
                }
            }
        };
    }

    public async Task<POFrontMarginResult> ApplyFrontMarginToPOAsync(
        POHeaderGetDto poHeader,
        List<POLineGetDto> poLines)
    {
        var result = new POFrontMarginResult
        {
            OriginalPOAmount = poLines.Sum(l => l.UnitCost * l.Quantity)
        };

        foreach (var line in poLines)
        {
            // Get applicable promotions for this line
            var applicablePromotions = await GetApplicablePromotionsAsync(poHeader, line);

            if (applicablePromotions.Any())
            {
                var lineResult = await CalculateLineDiscountAsync(line, applicablePromotions);

                // Update line with best discount
                line.UnitCost = lineResult.FinalUnitCost;
                line.Amount = lineResult.FinalAmount;

                result.LineResults.Add(lineResult);
                result.TotalSavings += lineResult.TotalSavings;

                // Add gift lines
                if (lineResult.GiftLines?.Any() == true)
                {
                    result.GiftLines.AddRange(lineResult.GiftLines);
                }
            }
        }

        result.FinalPOAmount = poLines.Sum(l => l.Amount);
        result.TotalDiscountPercentage = result.OriginalPOAmount > 0
            ? (result.TotalSavings / result.OriginalPOAmount) * 100
            : 0;

        return result;
    }

    public async Task<List<PromotionFrontMargin>> GetApplicablePromotionsAsync(
        POHeaderGetDto poHeader,
        POLineGetDto poLine)
    {
        var currentDate = DateTime.Now;

        // Get active Front Margin promotions for the vendor and item
        var promotions = await _context.PromotionFrontMargins
            .Include(p => p.PromotionHeader)
            .Where(p => p.PromotionHeader.VendorCode == poHeader.BuyFromVendorNumber &&
                       p.PromotionHeader.ProgramType == 1 && // Front Margin
                       p.PromotionHeader.Status == 2 && // Active
                       p.PromotionHeader.StartDate <= currentDate &&
                       p.PromotionHeader.EndDate >= currentDate &&
                       p.ItemNumber == poLine.ItemNumber &&
                       p.Status == 1 && // Active
                       p.ModificationStatus == 1) // Not deleted
            .Where(p => !p.MinimumQuantity.HasValue || poLine.Quantity >= p.MinimumQuantity.Value)
            .Where(p => !p.MinimumAmount.HasValue || (poLine.UnitCost * poLine.Quantity) >= p.MinimumAmount.Value)
            .ToListAsync();

        return promotions;
    }
}

// Result classes
public class FrontMarginCalculationResult
{
    public decimal OriginalUnitCost { get; set; }
    public decimal OriginalAmount { get; set; }
    public decimal FinalUnitCost { get; set; }
    public decimal FinalAmount { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public decimal TotalSavings { get; set; }
    public string? BestPromotionNumber { get; set; }
    public List<AppliedPromotionInfo> AppliedPromotions { get; set; } = new();
    public List<POLineGetDto>? GiftLines { get; set; }
}

public class DiscountCalculationResult
{
    public decimal NewUnitCost { get; set; }
    public decimal DiscountAmount { get; set; }
    public string CalculationMethod { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public List<POLineGetDto>? GiftLines { get; set; }
}

public class AppliedPromotionInfo
{
    public string PromotionNumber { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal NewUnitCost { get; set; }
    public List<POLineGetDto>? GiftLines { get; set; }
}

public class POFrontMarginResult
{
    public decimal OriginalPOAmount { get; set; }
    public decimal FinalPOAmount { get; set; }
    public decimal TotalSavings { get; set; }
    public decimal TotalDiscountPercentage { get; set; }
    public List<FrontMarginCalculationResult> LineResults { get; set; } = new();
    public List<POLineGetDto> GiftLines { get; set; } = new();
}
