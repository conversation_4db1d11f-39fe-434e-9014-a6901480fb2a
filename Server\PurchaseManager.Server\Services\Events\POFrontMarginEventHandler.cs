using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Server.Services.Events;

/// <summary>
///     Event handler for PO events to automatically apply Front Margin
/// </summary>
public interface IPOFrontMarginEventHandler
{
    /// <summary>
    ///     Handle PO creation event
    /// </summary>
    Task HandlePOCreatedAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Handle PO modification event
    /// </summary>
    Task HandlePOModifiedAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Handle PO line addition event
    /// </summary>
    Task HandlePOLineAddedAsync(POHeaderGetDto poHeader, POLineGetDto newLine);

    /// <summary>
    ///     Handle PO line modification event
    /// </summary>
    Task HandlePOLineModifiedAsync(POHeaderGetDto poHeader, POLineGetDto modifiedLine);

    /// <summary>
    ///     Handle vendor change event
    /// </summary>
    Task HandleVendorChangedAsync(POHeaderGetDto poHeader, string oldVendorCode, string newVendorCode);
}
public class POFrontMarginEventHandler : IPOFrontMarginEventHandler
{
    private readonly IPOFrontMarginIntegrationManager _integrationManager;
    private readonly ILogger<POFrontMarginEventHandler> _logger;

    public POFrontMarginEventHandler(
        IPOFrontMarginIntegrationManager integrationManager,
        ILogger<POFrontMarginEventHandler> logger)
    {
        _integrationManager = integrationManager;
        _logger = logger;
    }

    public async Task HandlePOCreatedAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        try
        {
            _logger.LogInformation("Handling PO created event for PO {PONumber}", poHeader.Number);

            // Auto-apply Front Margin if enabled
            var result = await _integrationManager.AutoApplyFrontMarginAsync(poHeader, poLines);

            if (result.IsSuccessStatusCode)
            {
                _logger.LogInformation("Front Margin auto-applied to new PO {PONumber}", poHeader.Number);
            }
            else
            {
                _logger.LogWarning("Failed to auto-apply Front Margin to PO {PONumber}: {Message}",
                poHeader.Number, result.Message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling PO created event for PO {PONumber}", poHeader.Number);
        }
    }

    public async Task HandlePOModifiedAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        try
        {
            _logger.LogInformation("Handling PO modified event for PO {PONumber}", poHeader.Number);

            // Check if PO has Front Margin applied
            var summary = await _integrationManager.GetPOFrontMarginSummaryAsync(poHeader.Number);

            if (summary.IsSuccessStatusCode)
            {
                // Recalculate Front Margin if it was previously applied
                var recalcResult = await _integrationManager.RecalculateFrontMarginAsync(poHeader.Number);

                if (recalcResult.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Front Margin recalculated for modified PO {PONumber}", poHeader.Number);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling PO modified event for PO {PONumber}", poHeader.Number);
        }
    }

    public async Task HandlePOLineAddedAsync(POHeaderGetDto poHeader, POLineGetDto newLine)
    {
        try
        {
            _logger.LogInformation("Handling PO line added event for PO {PONumber}, Line {LineNumber}",
            poHeader.Number, newLine.LineNumber);

            // Check if the new line is eligible for Front Margin
            var validation = await _integrationManager.ValidatePOForFrontMarginAsync(poHeader, new List<POLineGetDto>
            {
                newLine
            });

            if (validation.IsSuccessStatusCode)
            {
                // Apply Front Margin to the new line if eligible
                var applyResult = await _integrationManager.AutoApplyFrontMarginAsync(poHeader, new List<POLineGetDto>
                {
                    newLine
                });

                if (applyResult.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Front Margin applied to new line {LineNumber} in PO {PONumber}",
                    newLine.LineNumber, poHeader.Number);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling PO line added event for PO {PONumber}", poHeader.Number);
        }
    }

    public async Task HandlePOLineModifiedAsync(POHeaderGetDto poHeader, POLineGetDto modifiedLine)
    {
        try
        {
            _logger.LogInformation("Handling PO line modified event for PO {PONumber}, Line {LineNumber}",
            poHeader.Number, modifiedLine.LineNumber);

            // Recalculate Front Margin for the modified line
            var recalcResult = await _integrationManager.RecalculateFrontMarginAsync(poHeader.Number);

            if (recalcResult.IsSuccessStatusCode)
            {
                _logger.LogInformation("Front Margin recalculated for modified line {LineNumber} in PO {PONumber}",
                modifiedLine.LineNumber, poHeader.Number);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling PO line modified event for PO {PONumber}", poHeader.Number);
        }
    }

    public async Task HandleVendorChangedAsync(POHeaderGetDto poHeader, string oldVendorCode, string newVendorCode)
    {
        try
        {
            _logger.LogInformation("Handling vendor changed event for PO {PONumber}: {OldVendor} -> {NewVendor}",
            poHeader.Number, oldVendorCode, newVendorCode);

            // Remove old Front Margin if any
            await _integrationManager.RemoveFrontMarginFromPOAsync(poHeader.Number);

            // Apply new Front Margin for the new vendor
            var poLines = new List<POLineGetDto>();// This would need to be passed or retrieved
            var applyResult = await _integrationManager.AutoApplyFrontMarginAsync(poHeader, poLines);

            if (applyResult.IsSuccessStatusCode)
            {
                _logger.LogInformation("Front Margin updated for vendor change in PO {PONumber}", poHeader.Number);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling vendor changed event for PO {PONumber}", poHeader.Number);
        }
    }
}
/// <summary>
///     Background service for processing Front Margin events
/// </summary>
public class POFrontMarginEventProcessor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<POFrontMarginEventProcessor> _logger;

    public POFrontMarginEventProcessor(
        IServiceProvider serviceProvider,
        ILogger<POFrontMarginEventProcessor> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("PO Front Margin Event Processor started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // Process any queued events
                await ProcessQueuedEventsAsync();

                // Wait before next processing cycle
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in PO Front Margin Event Processor");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);// Wait before retrying
            }
        }

        _logger.LogInformation("PO Front Margin Event Processor stopped");
    }

    private async Task ProcessQueuedEventsAsync()
    {
        using var scope = _serviceProvider.CreateScope();

        try
        {
            // This would process events from a queue
            // For now, just log that we're processing
            _logger.LogDebug("Processing queued Front Margin events");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing queued events");
        }
    }
}
/// <summary>
///     Event models for PO Front Margin integration
/// </summary>
public class POEvent
{
    public string EventType { get; set; } = string.Empty;
    public string PONumber { get; set; } = string.Empty;
    public DateTime EventTime { get; set; } = DateTime.UtcNow;
    public string EventData { get; set; } = string.Empty;// JSON data
    public bool Processed { get; set; } = false;
}
public class POCreatedEvent : POEvent
{
    public POHeaderGetDto POHeader { get; set; } = null!;
    public List<POLineGetDto> POLines { get; set; } = new List<POLineGetDto>();
}
public class POModifiedEvent : POEvent
{
    public POHeaderGetDto POHeader { get; set; } = null!;
    public List<POLineGetDto> POLines { get; set; } = new List<POLineGetDto>();
    public List<string> ModifiedFields { get; set; } = new List<string>();
}
public class POLineAddedEvent : POEvent
{
    public POHeaderGetDto POHeader { get; set; } = null!;
    public POLineGetDto NewLine { get; set; } = null!;
}
public class POLineModifiedEvent : POEvent
{
    public POHeaderGetDto POHeader { get; set; } = null!;
    public POLineGetDto ModifiedLine { get; set; } = null!;
    public List<string> ModifiedFields { get; set; } = new List<string>();
}
public class VendorChangedEvent : POEvent
{
    public POHeaderGetDto POHeader { get; set; } = null!;
    public string OldVendorCode { get; set; } = string.Empty;
    public string NewVendorCode { get; set; } = string.Empty;
}
