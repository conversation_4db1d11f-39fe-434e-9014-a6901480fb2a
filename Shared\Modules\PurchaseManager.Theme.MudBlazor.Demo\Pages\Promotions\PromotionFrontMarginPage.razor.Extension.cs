using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
namespace PurchaseManager.Theme.Material.Demo.Pages.Promotions;

public partial class FrontMarginPage
{
    private async Task GetPromotionHeaderAsync(string promotionNumber)
    {
        try
        {
            var promotionHeaderResponse = await PromotionApiClient.GetPromotionHeaderByNumberAsync(promotionNumber);
            if (!promotionHeaderResponse.IsSuccessStatusCode)
            {
                viewNotifier.Show(promotionHeaderResponse.Message, ViewNotifierType.Error);
                return;
            }
            PromotionHeader = promotionHeaderResponse.Result;
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }

    private async Task CreatePromotionFrontMarginAsync()
    {
        try
        {
            var poLineInsert = Mapper.Map<CreatePromotionFrontMarginDto>(CurrentLine);
            poLineInsert.Notes = poLineInsert.Notes ??= "";
            poLineInsert.ProgramNumber = PromotionNumber;

            var apiResponseAddLine = await PromotionApiClient.CreatePromotionFrontMarginAsync(poLineInsert);

            if (!apiResponseAddLine.IsSuccessStatusCode)
            {
                ViewNotifier.Show(apiResponseAddLine.Message, ViewNotifierType.Error, L["Operation Failed"]);
                return;
            }
            await GetDetailPromotionAsync(PromotionNumber);
            ViewNotifier.Show(L["Operation Successful"], ViewNotifierType.Success);
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException()
                .Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            IsShowAddFrontMarginDialog = false;
            CurrentLine = new GetPromotionFrontMarginDto();
        }
    }

    private async Task GetDetailPromotionAsync(string number)
    {
        if (string.IsNullOrEmpty(number))
        {
            ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
            return;
        }
        try
        {
            await GetPromotionHeaderAsync(PromotionNumber);
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
        }
    }

    private async Task GetUserRolesAsync()
    {
        var authState = await authenticationStateTask;
        var user = authState.User;

        user.IsPurchaseUser();
        user.IsPurchaseManager();
        user.IsMKT();
        user.IsVendor();
        user.IsVendorContact();
        user.IsWarehouseUser();
        user.IsAdmin();
    }

    /// <summary>
    /// Update promotion header
    /// </summary>
    private async Task SavePromotionAsync()
    {
        try
        {
            var updateDto = Mapper.Map<UpdatePromotionHeaderDto>(PromotionHeader);

            var response = await PromotionApiClient.UpdatePromotionHeaderAsync(PromotionNumber, updateDto);

            if (response.IsSuccessStatusCode)
            {
                ViewNotifier.Show("Promotion updated successfully", ViewNotifierType.Success);
                IsEdit = false;
                await GetPromotionHeaderAsync(PromotionNumber);
                StateHasChanged();
            }
            else
            {
                ViewNotifier.Show("Failed to update promotion", ViewNotifierType.Error);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }
}
