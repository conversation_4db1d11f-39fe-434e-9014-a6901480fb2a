﻿using AutoMapper;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;

using NetTopologySuite.Utilities;
using NuGet.Protocol;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Db;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Storage;

using Serilog;

using System.Data.Common;
using static Microsoft.AspNetCore.Http.StatusCodes;

using Item = PurchaseManager.Infrastructure.Storage.DataModels.Item;
using ItemColors = PurchaseManager.Infrastructure.Storage.DataModels.ItemColors;
using ItemUnitOfMeasure = PurchaseManager.Infrastructure.Storage.DataModels.ItemUnitOfMeasure;
using SalesPrice = PurchaseManager.Infrastructure.Storage.DataModels.SalesPrice;
using UnitOfMeasure = PurchaseManager.Infrastructure.Storage.DataModels.UnitOfMeasure;
using VendorItem = PurchaseManager.Infrastructure.Storage.DataModels.VendorItem;

namespace PurchaseManager.Server.Managers;

// [ApiResponseException]
public class ItemManager : IItemManager
{
    private const string Business = "ITEM";
    private const string Branch = "AL";
    private readonly ApplicationPersistenceManager _persistenceManager;
    private readonly IAdminManager _adminManager;
    private readonly ApplicationDbContext _context;
    private readonly IStringLocalizer<Global> L;
    private readonly IMapper _mapper;
    public ItemManager(ApplicationPersistenceManager persistenceManager, ApplicationDbContext applicationDbContext, IStringLocalizer<Global> l,
        IMapper mapper, IAdminManager adminManager)
    {
        _persistenceManager = persistenceManager;
        _context = applicationDbContext;
        L = l;
        _mapper = mapper;
        _adminManager = adminManager;
    }

    public IQueryable<DetailItemDto> GetAllItems(ItemFilter filter, string queryString)
    {
        IQueryable<Item> queryableItems = _persistenceManager.GetEntities<Item>().AsNoTracking()
            .Include(h => h.ItemUnitOfMeasures)
            .Include(h => h.SalesPrices)
            .Include(h => h.ItemColors)
            .ThenInclude(ic => ic.ColorCodeNavigation)
            .Where(h =>
                (filter.Query == null || h.Name.Contains(filter.Query)) &&
                (filter.Number == null || h.Number.Equals(filter.Number)))
            .OrderByDescending(h => h.RowId);

        if (!queryString!.Contains("take"))
        {
            queryableItems = queryableItems.Take(10);
        }

        if (!queryString!.Contains("skip"))
        {
            queryableItems = queryableItems.Skip(0);
        }
        // get vendor item when system item is empty
        var result = _mapper.ProjectTo<DetailItemDto>(queryableItems);
        if (!result.Any())
        {
            IQueryable<VendorItem> queryVendorItem = _persistenceManager.GetEntities<VendorItem>().AsNoTracking()
                        .Where(h =>
                            filter.Query == null || h.Name.Contains(filter.Query) ||
                            filter.Number == null || h.ItemNumber.Equals(filter.Number))
                        .OrderByDescending(h => h.RowId);
            if (queryVendorItem.Any())
            {
                if (!queryString!.Contains("take"))
                {
                    queryVendorItem = queryVendorItem.Take(10);
                }

                if (!queryString!.Contains("skip"))
                {
                    queryVendorItem = queryVendorItem.Skip(0);
                }
                result = _mapper.ProjectTo<DetailItemDto>(queryVendorItem);
            }
        }
        return result;
    }

    public async Task<ApiResponse> InsertItem(CreateItemDto createItemDto)
    {
        if (createItemDto == null)
        {
            return new ApiResponse(Status404NotFound, L["Item cannot be null"]);
        }

        await using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var validationResponse = await ValidateAsync(createItemDto);
            if (validationResponse != null)
            {
                return validationResponse;
            }

            var itemMapper = _mapper.Map<Item>(createItemDto);
            var itemNumber = await _adminManager.CreateNumberSeries(Business, Branch);
            if (string.IsNullOrEmpty(itemNumber))
            {
                return new ApiResponse(Status404NotFound, L["Cannot create number series line"]);
            }
            itemMapper.Number = itemNumber;
            itemMapper.LoginId = _adminManager.GetUserLogin();
            // Add item
            await _context.Items.AddAsync(itemMapper);
            // Add list item unit of measure
            var itemUnitOfMeasures = createItemDto.UnitOfMeasureDtos!.Select((uomDto, index) => new ItemUnitOfMeasure
            {
                ItemNumber = itemNumber,// Sử dụng Id của item vừa tạo
                Code = uomDto.Code,
                QuantityPerUnitOfMeasure = index == 0 ? 1 : uomDto.QuantityPerUnitOfMeasure,
                Description = uomDto.Description,
                Status = 2,
                Type = uomDto.Type,
                Block = 0,
                LoginId = _adminManager.GetUserLogin()
            }).ToList();

            await _context.ItemUnitOfMeasures.AddRangeAsync(itemUnitOfMeasures);

            var salesPrices = createItemDto.SalesPrices!.Select(priceDto => new SalesPrice
            {
                ItemNumber = itemNumber,
                UnitOfMeasureCode = priceDto.UnitOfMeasureCode,
                StartingDate = priceDto.StartingDate,
                UnitPrice = priceDto.UnitPrice,
                EndingDate = priceDto.EndingDate,
                SourceCode = priceDto.SourceCode,
                Quantity = priceDto.Quantity,
                QuantityPerUnitOfMeasure = priceDto.QuantityPerUnitOfMeasure,
                Description = priceDto.Description,
                LoginId = _adminManager.GetUserLogin(),
                Block = 0
            }).ToList();

            await _context.SalesPrices.AddRangeAsync(salesPrices);

            // Save changes once
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return new ApiResponse(Status201Created, L["Item created successfully"], itemMapper.Number);
        }
        catch (DbException ex)
        {
            await transaction.RollbackAsync();
            Log.Error("Error inserting item: {Message}", ex.Message);
            return new ApiResponse(Status404NotFound, ex.Message);
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            Log.Error("Error inserting item: {Message}", ex.Message);
            return new ApiResponse(Status500InternalServerError, L["Error inserting item"]);
        }
    }


    public async Task<ApiResponse> UpdateItem(string number, UpdateItemDto updateItemDto)
    {
        var item = await _context.Items.SingleOrDefaultAsync(u => u.Number == number);
        if (item == null)
        {
            return new ApiResponse(Status404NotFound, L["Item not found"]);
        }

        _mapper.Map(updateItemDto, item);

        item.Number = number.ToUpper();
        item.LastDateModified = DateTime.Now;
        item.LoginId = _adminManager.GetUserLogin();
        item.LastUserModified = _adminManager.GetUserLogin();

        try
        {
            await _context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Item updated successfully"]);
        }
        catch
        {
            return new ApiResponse(Status500InternalServerError, L["Error updating Item"]);
        }
    }

    public async Task<ApiResponse> SearchItemByNumberOrName(String number, String name, String queryString, CancellationToken cancellationToken = default)
    {
        try
        {
            IQueryable<Item> query = _context.Items
                .Include(h => h.ItemColors)
                .ThenInclude(ic => ic.ColorCodeNavigation);

            switch (string.IsNullOrEmpty(number))
            {
                case false when !string.IsNullOrEmpty(name):
                    query = query.Where(item => item.Number.Contains(number) || item.Name.Contains(name));
                    break;
                case false:
                    query = query.Where(item => item.Number.Contains(number) || item.Name.Contains(number));
                    break;
                default:
                    {
                        if (!string.IsNullOrEmpty(name))
                        {
                            query = query.Where(item => item.Name.Contains(name) || item.Number.Contains(name));
                        }
                        break;
                    }
            }

            if (!queryString!.Contains("take"))
            {
                query = query.Take(100);
            }

            if (!queryString!.Contains("skip"))
            {
                query = query.Skip(0);
            }

            var result = await query
                .Include(i => i.SalesPrices)
                .Include(i => i.ItemUnitOfMeasures)
                .ToListAsync(cancellationToken);

            var data = _mapper.Map<List<DetailItemDto>>(result);
            return new ApiResponse(Status200OK, L["Search item successfully"], data);
        }
        catch
        {
            return new ApiResponse(Status404NotFound, L["Error searching item"]);
        }
    }

    public async Task<ApiResponse> InsertUnitOfMeasure(List<CreateUnitOfMeasureDto> unitOfMeasureDtos)
    {
        var unitsOfMeasureToAdd = new List<UnitOfMeasure>();

        foreach (var unitOfMeasureDto in unitOfMeasureDtos)
        {
            var existingUnit = await _context.UnitOfMeasures.FirstOrDefaultAsync(u => u.Code == unitOfMeasureDto.Code);

            if (existingUnit != null)
            {
                return new ApiResponse(Status400BadRequest, L["Unit of measure with code {0} already exists", unitOfMeasureDto.Code]);
            }

            var unitOfMeasure = _mapper.Map<UnitOfMeasure>(unitOfMeasureDto);
            unitOfMeasure.Code = unitOfMeasure.Code.ToUpper();
            unitsOfMeasureToAdd.Add(unitOfMeasure);
        }

        try
        {
            await _context.UnitOfMeasures.AddRangeAsync(unitsOfMeasureToAdd);
            await _context.SaveChangesAsync();
            return new ApiResponse(Status201Created, L["Units of measure created successfully"]);
        }
        catch
        {
            return new ApiResponse(Status500InternalServerError, L["Error inserting units of measure"]);
        }
    }


    public async Task<ApiResponse> UpdateUnitOfMeasure(string code, UpdateUnitOfMeasureDto updateUnitOfMeasureDto)
    {
        var unit = await _context.UnitOfMeasures.SingleOrDefaultAsync(u => u.Code == code);
        if (unit == null)
        {
            return new ApiResponse(Status404NotFound, L["Unit of measure not found"]);
        }

        _mapper.Map(updateUnitOfMeasureDto, unit);
        unit.Code = code.ToUpper();
        try
        {
            await _context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Unit of measure updated successfully"]);
        }
        catch
        {
            return new ApiResponse(Status500InternalServerError, L["Error updating unit of measure"]);
        }
    }


    public async Task<ApiResponse> InsertSalesPrice(List<CreateSalesPriceDto> salesPriceDtos)
    {
        var salesPricesToAdd = new List<SalesPrice>();

        foreach (var salesPriceDto in salesPriceDtos)
        {
            var existingSalesPrice = await _context.SalesPrices
                .FirstOrDefaultAsync(sp => sp.ItemNumber == salesPriceDto.ItemNumber
                                           && sp.StartingDate == salesPriceDto.StartingDate &&
                                           sp.UnitOfMeasureCode == salesPriceDto.UnitOfMeasureCode);

            if (existingSalesPrice != null)
            {
                return new ApiResponse(Status400BadRequest,
                    L["Sales price already exists"]);
            }


            var salesPrice = new SalesPrice
            {
                ItemNumber = salesPriceDto.ItemNumber,
                StartingDate = salesPriceDto.StartingDate,
                UnitOfMeasureCode = salesPriceDto.UnitOfMeasureCode,
                UnitPrice = salesPriceDto.UnitPrice,
                EndingDate = salesPriceDto.EndingDate,
                SourceCode = salesPriceDto.SourceCode,
                Quantity = salesPriceDto.Quantity,
                QuantityPerUnitOfMeasure = salesPriceDto.QuantityPerUnitOfMeasure,
                Description = salesPriceDto.Description,
                Block = salesPriceDto.Block,
                LoginId = _adminManager.GetUserLogin()
            };
            salesPricesToAdd.Add(salesPrice);
        }

        try
        {
            await _context.SalesPrices.AddRangeAsync(salesPricesToAdd);
            await _context.SaveChangesAsync();
            return new ApiResponse(Status201Created, L["Sales prices created successfully"]);
        }
        catch
        {
            return new ApiResponse(Status500InternalServerError, L["Error inserting sales prices"]);
        }
    }


    public async Task<ApiResponse> UpdateSalesPrice(string itemNumber, DateTime startDate, string unitOfMeasure,
        UpdateSalesPriceDto salesPrice)
    {
        try
        {
            var salesPriceData = await _context.SalesPrices.SingleOrDefaultAsync(u =>
                u.ItemNumber == itemNumber && u.StartingDate == startDate &&
                u.UnitOfMeasureCode == unitOfMeasure);
            if (salesPriceData == null)
            {
                return new ApiResponse(Status404NotFound, L["Sale price not found"]);
            }

            _mapper.Map(salesPrice, salesPriceData);
            salesPriceData.LastDateModified = DateTime.Now;
            salesPriceData.LoginId = _adminManager.GetUserLogin();
            // salesPriceData.LastUserModified = GetUserName();
            await _context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Sale price updated successfully"]);
        }
        catch
        {
            return new ApiResponse(Status500InternalServerError, L["Error updating sale price"]);
        }
    }

    public async Task<ApiResponse> InsertItemUnitOfMeasure(List<CreateItemUnitOfMeasureDto> itemUnitOfMeasure)
    {
        var listItemUnitOfMeasure = new List<ItemUnitOfMeasure>();
        foreach (var unitOfMeasureDto in itemUnitOfMeasure)
        {
            var existingUnit = await _context.ItemUnitOfMeasures.FirstOrDefaultAsync(u => u.Code == unitOfMeasureDto.Code
                                                                                          && u.ItemNumber == unitOfMeasureDto.ItemNumber);
            var existingItem = await _context.Items.FirstOrDefaultAsync(i => i.Number == unitOfMeasureDto.ItemNumber);
            var exitingUnitOfMeasure = await _context.UnitOfMeasures.FirstOrDefaultAsync(u => u.Code == unitOfMeasureDto.Code);
            if (existingUnit != null)
            {
                return new ApiResponse(Status400BadRequest, L["Duplicate Data"]);
            }
            if (existingItem == null)
            {
                return new ApiResponse(Status400BadRequest, L["Item not found"]);
            }
            if (exitingUnitOfMeasure == null)
            {
                return new ApiResponse(Status400BadRequest, L["Unit of measure not found"]);
            }

            var unitOfMeasure = new ItemUnitOfMeasure
            {
                Code = unitOfMeasureDto.Code.ToUpper(),
                LoginId = _adminManager.GetUserLogin(),
                ItemNumber = unitOfMeasureDto.ItemNumber,
                QuantityPerUnitOfMeasure = unitOfMeasureDto.QuantityPerUnitOfMeasure,
                Description = unitOfMeasureDto.Description,
                Type = unitOfMeasureDto.Type,
                Block = 0,
                Status = 2
            };
            listItemUnitOfMeasure.Add(unitOfMeasure);
        }

        try
        {
            await _context.ItemUnitOfMeasures.AddRangeAsync(listItemUnitOfMeasure);
            await _context.SaveChangesAsync();
            return new ApiResponse(Status201Created, L["Units of measure created successfully"]);
        }
        catch
        {
            return new ApiResponse(Status500InternalServerError, L["Error inserting item units of measure"]);
        }
    }

    public async Task<ApiResponse> UpdateItemUnitOfMeasure(string itemNumber, string unitOfMeasureCode, UpdateItemUnitOfMeasureDto itemUnitOfMeasure)
    {
        try
        {
            var itemUnitOfMeasureData = await _context.ItemUnitOfMeasures.SingleOrDefaultAsync(u =>
                u.ItemNumber == itemNumber && u.Code == unitOfMeasureCode);
            if (itemUnitOfMeasureData == null)
            {
                return new ApiResponse(Status404NotFound, L["Item Unit of measure not found"]);
            }

            _mapper.Map(itemUnitOfMeasure, itemUnitOfMeasureData);
            itemUnitOfMeasureData.LastDateModified = DateTime.Now;
            itemUnitOfMeasureData.LoginId = _adminManager.GetUserLogin();
            // itemUnitOfMeasureData.LastUserModified = GetUserName();
            await _context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Item Unit of measure updated successfully"]);
        }
        catch
        {
            return new ApiResponse(Status500InternalServerError, L["Error updating Item unit of measure"]);
        }
    }
    public async Task<ApiResponse> GetItemUnitOfMeasure(string itemNumber)
    {
        try
        {
            var itemUnit = await _context.ItemUnitOfMeasures
                .Where(x => x.ItemNumber == itemNumber).ToListAsync();

            if (itemUnit.Count == 0)
            {
                return new ApiResponse(Status404NotFound, L["Item Unit of measure not found"]);
            }
            var response = _mapper.Map<List<DetailItemUnitOfMeasureDto>>(itemUnit);

            return new ApiResponse(Status200OK, L["Get Item Unit of measure Successfully"], response);
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.GetBaseException().Message);
        }
    }

    private Task<ApiResponse> ValidateAsync(CreateItemDto createItemDto)
    {
        if (string.IsNullOrEmpty(createItemDto.Name))
        {
            return Task.FromResult(new ApiResponse(Status400BadRequest, L["Name is required"]));
        }

        if (createItemDto.UnitOfMeasureDtos == null || createItemDto.UnitOfMeasureDtos.Count == 0)
        {
            return Task.FromResult(new ApiResponse(Status400BadRequest, L["At least one unit of measure is required"]));
        }

        if (createItemDto.SalesPrices == null || createItemDto.SalesPrices.Count == 0)
        {
            return Task.FromResult(new ApiResponse(Status400BadRequest, L["At least one sales price is required"]));
        }

        if (createItemDto.UnitOfMeasureDtos!.Count == 0)
        {
            return Task.FromResult(new ApiResponse(Status400BadRequest, L["Unit Of Measure is required"]));
        }

        var firstUnitOfMeasureDto = createItemDto.UnitOfMeasureDtos[0];

        if (!createItemDto.BaseUnitOfMeasure.Equals(firstUnitOfMeasureDto.Code, StringComparison.InvariantCultureIgnoreCase))
        {
            return Task.FromResult(new ApiResponse(Status400BadRequest, L["BaseUnitOfMeasure must match the Code of the first UnitOfMeasure"]));
        }

        var validUnitCodes = createItemDto.UnitOfMeasureDtos.Select(uomDto => uomDto.Code).ToList();
        if (createItemDto.SalesPrices!.Any(salesPriceDto => !validUnitCodes.Contains(salesPriceDto.UnitOfMeasureCode)))
        {
            return Task.FromResult(new ApiResponse(Status400BadRequest, L["Invalid UnitOfMeasureCode in SalesPrices"]));
        }

        // Check for duplicate QuantityPerUnitOfMeasure
        var quantitySet = new HashSet<decimal?>();
        return Task.FromResult(createItemDto.UnitOfMeasureDtos.Any(uomDto => !quantitySet.Add(uomDto.QuantityPerUnitOfMeasure!))
            ? new ApiResponse(Status400BadRequest, L["QuantityPerUnitOfMeasure must be unique"])
            : null);
    }

    public IQueryable<UnitOfMeasureDto> GetAllUnitOfMeasure(UnitFilter filter, string queryString)
    {
        IQueryable<UnitOfMeasure> queryableUnitOfMeasures = _persistenceManager.GetEntities<UnitOfMeasure>().AsNoTracking()
             .Where(h => filter.Query == null || h.Code.Contains(filter.Query))
             .OrderByDescending(h => h.RowId);
        var result = _mapper.ProjectTo<UnitOfMeasureDto>(queryableUnitOfMeasures);
        return result;
    }

    public async Task<ApiResponse> GetItem(string itemNumber)
    {
        var item = await _persistenceManager.GetEntities<Item>().AsNoTracking()
             .Include(h => h.ItemUnitOfMeasures)
             .Include(h => h.SalesPrices)
             .Include(h => h.ItemColors)
             .ThenInclude(ic => ic.ColorCodeNavigation)
             .Where(h => h.Number == itemNumber)
             .OrderByDescending(h => h.RowId).FirstOrDefaultAsync();

        if (item == null)
        {
            return ApiResponse.S404("Item number not found");
        }
        
        var result = _mapper.Map<DetailItemDto>(item);

        return ApiResponse.S200("Get Item Successfully", result);
    }
    public async Task<ApiResponse> InsertOrUpdateColors(List<InsertOrUpdateItemColorsDto> colors)
    {
        try
        {
            var colorCodes = colors.Select(x => x.ColorCode).ToList();
            var itemNumbers = colors.Select(x => x.ItemNumber).ToList();

            var existingItems = await _context.ItemColors
                .Where(x => colorCodes.Contains(x.ColorCode) && itemNumbers.Contains(x.ItemNumber))
                .ToListAsync();

            foreach (var dto in colors)
            {
                var existingItem = existingItems
                    .FirstOrDefault(x => x.ColorCode == dto.ColorCode && x.ItemNumber == dto.ItemNumber);

                if (existingItem != null)
                {
                    existingItem.Block = dto.Block;
                }
                else
                {
                    var newItem = new ItemColors
                    {
                        ColorCode = dto.ColorCode,
                        ItemNumber = dto.ItemNumber,
                        Block = dto.Block
                    };
                    _context.ItemColors.Add(newItem);
                }
            }
            await _context.SaveChangesAsync();
            return new ApiResponse(200, L["Item SKU Attributes updated successfully."]);
        }
        catch (Exception e)
        {
            return new ApiResponse(404, e.GetBaseException().Message);
        }
    }

    public IQueryable<DetailVendorItemDto> GetAllVendorItems(VendorItemFilter filter)
    {
        IQueryable<VendorItem> queryableItems = _persistenceManager.GetEntities<VendorItem>()
                                                    .AsNoTracking()
                                                    .Include(x => x.VendorNumberNavigation);

        if (!string.IsNullOrEmpty(filter.VendorNumber))
        {
            queryableItems = queryableItems.Where(h => h.VendorNumberNavigation.Number.Equals(filter.VendorNumber));
        }

        if (!string.IsNullOrEmpty(filter.ItemNumber))
        {
            queryableItems = queryableItems.Where(h => h.ItemNumber.Contains(filter.ItemNumber));
        }

        if (!string.IsNullOrEmpty(filter.ItemName) && string.IsNullOrEmpty(filter.ItemNumber))
        {
            queryableItems = queryableItems.Where(h => h.Name.Contains(filter.ItemName));
        }

        if (filter.IsBlocked != null)
        {
            queryableItems = queryableItems.Where(h => h.Blocked.Equals(filter.IsBlocked));
        }
        // sort query
        queryableItems.OrderByDescending(h => h.RowId);
        //mapping data
        var result = _mapper.ProjectTo<DetailVendorItemDto>(queryableItems);
        return result;
    }

    public async Task<ApiResponse> CreateVendorItem(CreateVendorItemDto item)
    {
        if (item == null)
        {
            return new ApiResponse(Status404NotFound, L["Item cannot be null"]);
        }

        try
        {
            var itemMapped = _mapper.Map<VendorItem>(item);
            var existedItem = _context.VendorItems.FirstOrDefault(x => x.ItemNumber == item.ItemNumber && x.Blocked == 0);
            if (existedItem is not null)
            {
                return new ApiResponse(Status400BadRequest, L["Item `{0}` already exists", item.ItemNumber]);
            }
            itemMapped.Blocked = 0;
            itemMapped.CreatedAt = DateTime.UtcNow;
            itemMapped.ItemNumber = item.ItemNumber;
            itemMapped.CreatedBy = _adminManager.GetUserLogin();
            // Add item
            await _context.VendorItems.AddAsync(itemMapped);

            await _context.SaveChangesAsync();
            return new ApiResponse(Status201Created, L["Item `{0}` was created successfully", itemMapped.ItemNumber], itemMapped.ItemNumber);
        }
        catch (DbException ex)
        {
            Log.Error("Error creating item: {Message}", ex.Message);
            return new ApiResponse(Status400BadRequest, ex.Message);
        }
        catch (Exception ex)
        {
            Log.Error("Error inserting item: {Message}", ex.Message);
            return new ApiResponse(Status500InternalServerError, L["Error creating item"]);
        }
    }

    public async Task<ApiResponse> UpdateVendorItem(UpdateVendorItemDto item)
    {
        try
        {
            var existedItem = await _context.VendorItems.SingleOrDefaultAsync(u => u.ItemNumber == item.ItemNumber);
            if (existedItem == null)
            {
                return new ApiResponse(Status404NotFound, L["Item not found"]);
            }
            _mapper.Map(item, existedItem);
            existedItem.LastUpdatedAt = DateTime.UtcNow;
            existedItem.LastUpdatedBy = _adminManager.GetUserLogin();
            await _context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Item updated successfully"]);
        }
        catch (Exception ex)
        {
            Log.Error("Error updating vendor item: {Message}", ex.Message);
            return new ApiResponse(Status500InternalServerError, L["Error updating vendor item"]);
        }
    }

    public async Task<ApiResponse<DetailVendorItemDto>> ToggleVendorItemStatus(int rowId)
    {
        try
        {
            var isBlocked = 0;
            var existedItem = _context.VendorItems.AsNoTracking().FirstOrDefault(u => u.RowId == rowId);
            if (existedItem == null)
            {
                return new ApiResponse(Status404NotFound, L["Item not found"]);
            }
            existedItem.Blocked = isBlocked == 0 ? 1 : 0;// toggle block

            existedItem.LastUpdatedAt = DateTime.UtcNow;
            existedItem.LastUpdatedBy = _adminManager.GetUserLogin();
            _context.VendorItems.Update(existedItem);
            await _context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["{0} vendor item successfully", isBlocked == 0 ? "Unblock" : "Block"], _mapper.Map<DetailVendorItemDto>(existedItem));
        }
        catch (Exception ex)
        {
            Log.Error("Error toggle vendor item status: {Message}", ex.Message);
            return new ApiResponse(Status500InternalServerError, L["Error updating vendor item"]);
        }
    }
}
