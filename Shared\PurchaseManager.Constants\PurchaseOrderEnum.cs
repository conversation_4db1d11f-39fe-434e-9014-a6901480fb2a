namespace PurchaseManager.Constants;

public enum PurchaseOrderEnum
{
    /// <summary>
    ///     PO mới: trạng thái có thể chỉnh sửa
    /// </summary>
    NewCreate,
    /// <summary>
    /// Mở lại PO khi status = 3.
    /// </summary>
    CancelConfirm,
    /// <summary>
    ///     Đơn hàng đang chờ Approve
    /// </summary>
    Confirm,
    /// <summary>
    ///     Đơn hàng đã được Approve 2 bên: NCC và TSA
    /// </summary>
    Approve,
    /// <summary>
    ///     Kho đã nhận 1 phần
    /// </summary>
    PartiallyReceived,
    /// <summary>
    ///     Kho nhận đủ
    /// </summary>
    Completed,
    /// <summary>
    /// Document đã được gửi sang CJ
    /// </summary>
    CJStatus = 50
}
public enum PurchaseOrderFilerEnum
{
    All,
    NewCreate,
    Approve,
    PartiallyReceived,
    Completed,
    CJL = 50
}
public enum StockOrderFilerEnum
{
    All,
    Approve,
    PartiallyReceived,
    Completed
}

public enum TypeOfProviderForPOEnum
{
    Vendor,
    Contact
}
