﻿using AutoMapper;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.StockOrder;
namespace PurchaseManager.Storage.Mapping;

public class StockOrderMappingProfile : Profile
{
    public StockOrderMappingProfile()
    {
        CreateMap<StockOrder, GetStockOrderDto>().ReverseMap();
        CreateMap<CreateStockOrderDto, StockOrder>()
            .ForMember(
            destinationMember: dest => dest.CreateAt,
            memberOptions: opt => opt.MapFrom(src => DateTime.UtcNow))
            .ForMember(
            destinationMember: dest => dest.RowId,
            memberOptions: opt => opt.Ignore())
            .ForMember(
            destinationMember: dest => dest.HeaderNumberNavigation,
            memberOptions: opt => opt.Ignore())
            .ForMember(
            destinationMember: dest => dest.PurchaseOrderLine,
            memberOptions: opt => opt.Ignore());
    }
}
