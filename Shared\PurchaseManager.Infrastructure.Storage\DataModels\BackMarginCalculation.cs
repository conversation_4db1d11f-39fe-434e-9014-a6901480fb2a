using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Back Margin Calculation Results - <PERSON><PERSON><PERSON> kết quả tính toán Back Margin
/// </summary>
[Table("BackMarginCalculations")]
public class BackMarginCalculation : FullTrackingEntity
{
    /// <summary>
    /// Mã chương trình Back Margin
    /// </summary>
    [Required]
    [StringLength(50)]
    public string PromotionNumber { get; set; } = string.Empty;

    /// <summary>
    /// Mã sản phẩm
    /// </summary>
    [Required]
    [StringLength(50)]
    public string ItemNumber { get; set; } = string.Empty;

    /// <summary>
    /// Tên sản phẩm
    /// </summary>
    [StringLength(250)]
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Đơn vị tính
    /// </summary>
    [StringLength(50)]
    public string UnitOfMeasure { get; set; } = string.Empty;

    /// <summary>
    /// Mã nhà cung cấp
    /// </summary>
    [Required]
    [StringLength(20)]
    public string VendorCode { get; set; } = string.Empty;

    /// <summary>
    /// Chu kỳ tính toán (YYYY-MM hoặc YYYY-QQ hoặc YYYY)
    /// </summary>
    [Required]
    [StringLength(20)]
    public string CalculationPeriod { get; set; } = string.Empty;

    /// <summary>
    /// Ngày bắt đầu chu kỳ
    /// </summary>
    [Required]
    public DateTime PeriodStartDate { get; set; }

    /// <summary>
    /// Ngày kết thúc chu kỳ
    /// </summary>
    [Required]
    public DateTime PeriodEndDate { get; set; }

    /// <summary>
    /// Số lượng mua trong chu kỳ
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal PurchaseQuantity { get; set; }

    /// <summary>
    /// Doanh số mua trong chu kỳ (VND)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal PurchaseAmount { get; set; }

    /// <summary>
    /// Tổng doanh số mua của tất cả sản phẩm trong chương trình
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalProgramPurchaseAmount { get; set; }

    /// <summary>
    /// Mốc doanh số để đạt thưởng
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MilestoneAmount { get; set; }

    /// <summary>
    /// Doanh số tối thiểu (cho stepwise)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MinAmount { get; set; }

    /// <summary>
    /// Doanh số tối đa (cho stepwise)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaxAmount { get; set; }

    /// <summary>
    /// Loại tính toán: 1=Progressive, 2=Stepwise
    /// </summary>
    public int CalculationType { get; set; }

    /// <summary>
    /// Loại giá trị chiết khấu: 1=Percentage, 2=FixedAmount
    /// </summary>
    public int DiscountValueType { get; set; }

    /// <summary>
    /// Tỷ lệ chiết khấu (%)
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal? DiscountPercentage { get; set; }

    /// <summary>
    /// Số tiền chiết khấu cố định
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? DiscountAmount { get; set; }

    /// <summary>
    /// Số tiền Back Margin được tính cho sản phẩm này
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal CalculatedBackMargin { get; set; }

    /// <summary>
    /// Back Margin trên đơn vị
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal BackMarginPerUnit { get; set; }

    /// <summary>
    /// Trạng thái: 1=Calculated, 2=Approved, 3=Paid, 4=Cancelled
    /// </summary>
    public int Status { get; set; } = 1;

    /// <summary>
    /// Ngày tính toán
    /// </summary>
    public DateTime CalculationDate { get; set; } = DateTime.Now;

    /// <summary>
    /// Người tính toán
    /// </summary>
    [StringLength(100)]
    public string? CalculatedBy { get; set; }

    /// <summary>
    /// Ngày phê duyệt
    /// </summary>
    public DateTime? ApprovalDate { get; set; }

    /// <summary>
    /// Người phê duyệt
    /// </summary>
    [StringLength(100)]
    public string? ApprovedBy { get; set; }

    /// <summary>
    /// Ngày chi trả
    /// </summary>
    public DateTime? PaymentDate { get; set; }

    /// <summary>
    /// Hình thức chi trả
    /// </summary>
    public int? PaymentMethod { get; set; }

    /// <summary>
    /// Ghi chú
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation properties
    [ForeignKey("PromotionNumber")]
    public PromotionHeader PromotionHeader { get; set; } = null!;
}
