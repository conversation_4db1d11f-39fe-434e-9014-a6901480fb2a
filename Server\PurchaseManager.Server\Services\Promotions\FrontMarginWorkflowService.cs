using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Services.Promotions;

public class FrontMarginWorkflowService : IFrontMarginWorkflowService
{
    private readonly ApplicationDbContext _context;
    private readonly IFrontMarginDeclarationService _declarationService;
    private readonly IFrontMarginValidationService _validationService;
    private readonly ILogger<FrontMarginWorkflowService> _logger;

    public FrontMarginWorkflowService(
        ApplicationDbContext context,
        IFrontMarginDeclarationService declarationService,
        IFrontMarginValidationService validationService,
        ILogger<FrontMarginWorkflowService> logger)
    {
        _context = context;
        _declarationService = declarationService;
        _validationService = validationService;
        _logger = logger;
    }

    public async Task<FrontMarginWorkflowResult> StartRegistrationWorkflowAsync(CreatePromotionFrontMarginDto dto, string initiatedBy)
    {
        try
        {
            // Validate the declaration first
            var declarationResult = await _declarationService.ValidateDeclarationAsync(dto);

            if (!declarationResult.IsValid)
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Validation failed",
                    ValidationErrors = declarationResult.ValidationErrors,
                    Warnings = declarationResult.Warnings
                };
            }

            // Register for approval
            var registrationResult = await _declarationService.RegisterForApprovalAsync(dto, initiatedBy);

            if (!registrationResult.IsValid)
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Registration failed",
                    ValidationErrors = registrationResult.ValidationErrors
                };
            }

            // Log workflow start
            await LogWorkflowActionAsync(registrationResult.CreatedNumber!, "WORKFLOW_STARTED", initiatedBy, "Front Margin registration workflow initiated");

            return new FrontMarginWorkflowResult
            {
                Success = true,
                Message = "Registration workflow started successfully",
                Number = registrationResult.CreatedNumber,
                CurrentStatus = "DRAFT",
                NextActions = new List<string> { "SUBMIT_FOR_APPROVAL", "EDIT", "DELETE" },
                Warnings = declarationResult.Warnings
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting registration workflow");
            return new FrontMarginWorkflowResult
            {
                Success = false,
                Message = "System error occurred"
            };
        }
    }

    public async Task<FrontMarginWorkflowResult> SubmitForApprovalAsync(string number, string submittedBy)
    {
        try
        {
            var frontMargin = await GetFrontMarginAsync(number);
            if (frontMargin == null)
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin not found"
                };
            }

            if (frontMargin.Status != 1) // Must be in Draft status
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin is not in Draft status"
                };
            }

            // Final validation before submission
            var validationErrors = _validationService.ValidateEntity(frontMargin);
            if (validationErrors.Any())
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Validation failed before submission",
                    ValidationErrors = validationErrors
                };
            }

            // Update status to Pending Approval
            frontMargin.Status = 5; // Pending Approval (new status)
            frontMargin.LastModifiedBy = submittedBy;
            frontMargin.LastModifiedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            await LogWorkflowActionAsync(number, "SUBMITTED_FOR_APPROVAL", submittedBy, "Submitted for approval");

            return new FrontMarginWorkflowResult
            {
                Success = true,
                Message = "Front Margin submitted for approval successfully",
                Number = number,
                CurrentStatus = "PENDING_APPROVAL",
                NextActions = new List<string> { "APPROVE", "REJECT" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error submitting Front Margin {Number} for approval", number);
            return new FrontMarginWorkflowResult
            {
                Success = false,
                Message = "System error occurred"
            };
        }
    }

    public async Task<FrontMarginWorkflowResult> ApproveInWorkflowAsync(string number, string approvedBy, string? comments = null)
    {
        try
        {
            var frontMargin = await GetFrontMarginAsync(number);
            if (frontMargin == null)
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin not found"
                };
            }

            if (frontMargin.Status != 5) // Must be Pending Approval
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin is not pending approval"
                };
            }

            // Check if user can approve (business rules)
            if (frontMargin.CreatedBy == approvedBy)
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Cannot approve your own Front Margin"
                };
            }

            // Update status to Approved
            frontMargin.Status = 6; // Approved (new status)
            frontMargin.LastModifiedBy = approvedBy;
            frontMargin.LastModifiedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            await LogWorkflowActionAsync(number, "APPROVED", approvedBy, comments ?? "Approved");

            return new FrontMarginWorkflowResult
            {
                Success = true,
                Message = "Front Margin approved successfully",
                Number = number,
                CurrentStatus = "APPROVED",
                NextActions = new List<string> { "ACTIVATE" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving Front Margin {Number}", number);
            return new FrontMarginWorkflowResult
            {
                Success = false,
                Message = "System error occurred"
            };
        }
    }

    public async Task<FrontMarginWorkflowResult> RejectInWorkflowAsync(string number, string rejectedBy, string reason)
    {
        try
        {
            var frontMargin = await GetFrontMarginAsync(number);
            if (frontMargin == null)
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin not found"
                };
            }

            if (frontMargin.Status != 5) // Must be Pending Approval
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin is not pending approval"
                };
            }

            // Update status to Rejected
            frontMargin.Status = 7; // Rejected (new status)
            frontMargin.LastModifiedBy = rejectedBy;
            frontMargin.LastModifiedAt = DateTime.UtcNow;
            frontMargin.Notes = (frontMargin.Notes ?? "") + $"\n[REJECTED] {reason}";

            await _context.SaveChangesAsync();

            await LogWorkflowActionAsync(number, "REJECTED", rejectedBy, reason);

            return new FrontMarginWorkflowResult
            {
                Success = true,
                Message = "Front Margin rejected",
                Number = number,
                CurrentStatus = "REJECTED",
                NextActions = new List<string> { "EDIT_AND_RESUBMIT" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting Front Margin {Number}", number);
            return new FrontMarginWorkflowResult
            {
                Success = false,
                Message = "System error occurred"
            };
        }
    }

    public async Task<FrontMarginWorkflowResult> ActivateAsync(string number, string activatedBy)
    {
        try
        {
            var frontMargin = await GetFrontMarginAsync(number);
            if (frontMargin == null)
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin not found"
                };
            }

            if (frontMargin.Status != 6) // Must be Approved
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin is not approved"
                };
            }

            // Check if promotion header is active
            var header = await _context.PromotionHeaders
                .FirstOrDefaultAsync(h => h.Number == frontMargin.ProgramNumber);

            if (header?.Status != 2) // Header must be active
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Promotion program is not active"
                };
            }

            // Update status to Active
            frontMargin.Status = 2; // Active
            frontMargin.LastModifiedBy = activatedBy;
            frontMargin.LastModifiedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            await LogWorkflowActionAsync(number, "ACTIVATED", activatedBy, "Front Margin activated and ready for use");

            return new FrontMarginWorkflowResult
            {
                Success = true,
                Message = "Front Margin activated successfully",
                Number = number,
                CurrentStatus = "ACTIVE",
                NextActions = new List<string> { "SUSPEND", "DEACTIVATE" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating Front Margin {Number}", number);
            return new FrontMarginWorkflowResult
            {
                Success = false,
                Message = "System error occurred"
            };
        }
    }

    public async Task<FrontMarginWorkflowResult> SuspendAsync(string number, string suspendedBy, string reason)
    {
        try
        {
            var frontMargin = await GetFrontMarginAsync(number);
            if (frontMargin == null)
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin not found"
                };
            }

            if (frontMargin.Status != 2) // Must be Active
            {
                return new FrontMarginWorkflowResult
                {
                    Success = false,
                    Message = "Front Margin is not active"
                };
            }

            // Update status to Suspended
            frontMargin.Status = 8; // Suspended (new status)
            frontMargin.LastModifiedBy = suspendedBy;
            frontMargin.LastModifiedAt = DateTime.UtcNow;
            frontMargin.Notes = (frontMargin.Notes ?? "") + $"\n[SUSPENDED] {reason}";

            await _context.SaveChangesAsync();

            await LogWorkflowActionAsync(number, "SUSPENDED", suspendedBy, reason);

            return new FrontMarginWorkflowResult
            {
                Success = true,
                Message = "Front Margin suspended",
                Number = number,
                CurrentStatus = "SUSPENDED",
                NextActions = new List<string> { "REACTIVATE", "DEACTIVATE" }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error suspending Front Margin {Number}", number);
            return new FrontMarginWorkflowResult
            {
                Success = false,
                Message = "System error occurred"
            };
        }
    }

    public async Task<FrontMarginWorkflowStatus> GetWorkflowStatusAsync(string number)
    {
        var frontMargin = await GetFrontMarginAsync(number);
        if (frontMargin == null)
        {
            return new FrontMarginWorkflowStatus
            {
                Number = number,
                CurrentStatus = "NOT_FOUND",
                StatusDescription = "Front Margin not found"
            };
        }

        var status = GetStatusInfo(frontMargin.Status);
        var nextActions = GetNextActions(frontMargin.Status);

        return new FrontMarginWorkflowStatus
        {
            Number = number,
            CurrentStatus = status.Name,
            StatusDescription = status.Description,
            NextActions = nextActions,
            CreatedBy = frontMargin.CreatedBy,
            CreatedAt = frontMargin.CreatedAt,
            LastModifiedBy = frontMargin.LastModifiedBy,
            LastModifiedAt = frontMargin.LastModifiedAt,
            CanEdit = CanEdit(frontMargin.Status),
            CanDelete = CanDelete(frontMargin.Status)
        };
    }

    public async Task<List<FrontMarginWorkflowHistory>> GetWorkflowHistoryAsync(string number)
    {
        // In a real implementation, this would query a workflow history table
        // For now, return empty list
        return new List<FrontMarginWorkflowHistory>();
    }

    public async Task<bool> CanPerformActionAsync(string number, string userId, string action)
    {
        var frontMargin = await GetFrontMarginAsync(number);
        if (frontMargin == null) return false;

        return action.ToUpper() switch
        {
            "EDIT" => frontMargin.Status == 1 && frontMargin.CreatedBy == userId,
            "DELETE" => frontMargin.Status == 1 && frontMargin.CreatedBy == userId,
            "SUBMIT_FOR_APPROVAL" => frontMargin.Status == 1 && frontMargin.CreatedBy == userId,
            "APPROVE" => frontMargin.Status == 5 && frontMargin.CreatedBy != userId,
            "REJECT" => frontMargin.Status == 5 && frontMargin.CreatedBy != userId,
            "ACTIVATE" => frontMargin.Status == 6,
            "SUSPEND" => frontMargin.Status == 2,
            "REACTIVATE" => frontMargin.Status == 8,
            _ => false
        };
    }

    #region Private Methods

    private async Task<PromotionFrontMargin?> GetFrontMarginAsync(string number)
    {
        return await _context.PromotionFrontMargins
            .FirstOrDefaultAsync(p => p.Number == number &&
                                    p.ModificationStatus == (int)ModificationStatusEnum.ACTIVE);
    }

    private async Task LogWorkflowActionAsync(string number, string action, string actionBy, string comments)
    {
        // In a real implementation, this would log to a workflow history table
        _logger.LogInformation("Front Margin {Number}: {Action} by {ActionBy}. Comments: {Comments}",
            number, action, actionBy, comments);
    }

    private (string Name, string Description) GetStatusInfo(int status)
    {
        return status switch
        {
            1 => ("DRAFT", "Bản nháp"),
            2 => ("ACTIVE", "Đang hoạt động"),
            3 => ("INACTIVE", "Tạm ngưng"),
            4 => ("EXPIRED", "Đã hết hạn"),
            5 => ("PENDING_APPROVAL", "Chờ phê duyệt"),
            6 => ("APPROVED", "Đã phê duyệt"),
            7 => ("REJECTED", "Đã từ chối"),
            8 => ("SUSPENDED", "Tạm ngưng"),
            _ => ("UNKNOWN", "Không xác định")
        };
    }

    private List<string> GetNextActions(int status)
    {
        return status switch
        {
            1 => new List<string> { "SUBMIT_FOR_APPROVAL", "EDIT", "DELETE" },
            2 => new List<string> { "SUSPEND", "DEACTIVATE" },
            3 => new List<string> { "ACTIVATE" },
            5 => new List<string> { "APPROVE", "REJECT" },
            6 => new List<string> { "ACTIVATE" },
            7 => new List<string> { "EDIT_AND_RESUBMIT" },
            8 => new List<string> { "REACTIVATE", "DEACTIVATE" },
            _ => new List<string>()
        };
    }

    private bool CanEdit(int status) => status == 1 || status == 7; // Draft or Rejected
    private bool CanDelete(int status) => status == 1; // Only Draft

    #endregion
}

#region Result Classes

public class FrontMarginWorkflowResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? Number { get; set; }
    public string CurrentStatus { get; set; } = string.Empty;
    public List<string> NextActions { get; set; } = new();
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

public class FrontMarginWorkflowStatus
{
    public string Number { get; set; } = string.Empty;
    public string CurrentStatus { get; set; } = string.Empty;
    public string StatusDescription { get; set; } = string.Empty;
    public List<string> NextActions { get; set; } = new();
    public string? CreatedBy { get; set; }
    public DateTime? CreatedAt { get; set; }
    public string? LastModifiedBy { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public bool CanEdit { get; set; }
    public bool CanDelete { get; set; }
}

public class FrontMarginWorkflowHistory
{
    public string Number { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string ActionBy { get; set; } = string.Empty;
    public DateTime ActionAt { get; set; }
    public string Comments { get; set; } = string.Empty;
}

#endregion
