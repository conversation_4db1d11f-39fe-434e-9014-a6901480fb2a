using Microsoft.EntityFrameworkCore;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public class CJLGRManager : ICJLGRManager
{
    private readonly ApplicationDbContext _context;
    private readonly IAdminManager _adminManager;

    public CJLGRManager(ApplicationDbContext context, IAdminManager adminManager)
    {
        _context = context;
        _adminManager = adminManager;
    }

    /// <summary>
    ///     Flow mới: Cập nhật trạng thái PO Header dựa trên data GR (quét từ GR để tối ưu performance)
    ///     Quét tất cả GR, group theo PoNumber, sau đó so sánh với PO Lines và cập nhật status
    /// </summary>
    /// <param name="poNumber">PO Number cần cập nhật trạng thái</param>
    /// <returns>ApiResponse với thông tin cập nhật</returns>
    public async Task<ApiResponse> UpdatePOStatusByGRDataAsync(string poNumber = null)
    {
        var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            var grQuery = _context.CjlGrHeaders
                .Include(grh => grh.CjlGrLines)
                .Include(grh => grh.PoNumberNavigation)
                .Where(grh => !string.IsNullOrEmpty(grh.PoNumber));

            if (!string.IsNullOrEmpty(poNumber))
            {
                grQuery = grQuery.Where(grh => grh.PoNumber == poNumber);
            }

            var grHeaders = await grQuery.ToListAsync();

            if (grHeaders.Count == 0)
            {
                return new ApiResponse(200, "Không có GR nào để xử lý.");
            }

            var grGroupsByPO = grHeaders
                .GroupBy(grh => grh.PoNumber)
                .ToList();

            var updatedPOs = new List<string>();

            foreach (var grGroup in grGroupsByPO)
            {
                var poNum = grGroup.Key;

                var poHeader = await _context.PurchaseOrderHeaders
                    .FirstOrDefaultAsync(po => po.Number == poNum &&
                                               (po.Status == (int)PurchaseOrderEnum.CJStatus ||
                                                po.Status == (int)PurchaseOrderEnum.PartiallyReceived));

                if (poHeader == null)
                {
                    Console.WriteLine($"PO {poNum} không tồn tại hoặc không cần cập nhật status");
                    continue;
                }

                var poLines = await _context.PurchaseOrderLines
                    .Where(pol => pol.DocumentNumber == poNum)
                    .ToListAsync();

                if (poLines.Count == 0)
                {
                    Console.WriteLine($"PO {poNum} không có lines");
                    continue;
                }

                var allGrLines = grGroup.SelectMany(grh => grh.CjlGrLines).ToList();

                var poStatus = CalculatePOStatusFromGR(poLines, allGrLines);

                if (poHeader.Status == (int)poStatus.Status)
                {
                    continue;
                }

                poHeader.Status = (int)poStatus.Status;
                poHeader.LastModifiedTime = DateTime.Now;
                poHeader.ModifiedId = _adminManager.GetUserLogin();
                updatedPOs.Add(poHeader.Number);

                Console.WriteLine($"Cập nhật PO {poHeader.Number}: {poStatus.poStatusResult.StatusName} " +
                                  $"({poStatus.poStatusResult.ReceivedLines}/{poStatus.poStatusResult.TotalLines} lines hoàn thành)");
            }

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            var message = updatedPOs.Count > 0
                ? $"Đã cập nhật trạng thái cho {updatedPOs.Count} PO: {string.Join(", ", updatedPOs)}"
                : "Không có PO nào cần cập nhật trạng thái.";

            return new ApiResponse(200, message, new
            {
                ProcessedGRGroups = grGroupsByPO.Count, UpdatedPOs = updatedPOs
            });
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            return new ApiResponse(500, $"Error updating PO status: {ex.Message}");
        }
    }

    /// <summary>
    ///     Tính toán trạng thái PO dựa trên GR Lines
    ///     So sánh GR Lines với PO Lines để xác định trạng thái PO
    /// </summary>
    /// <param name="poLines">Danh sách PO Lines</param>
    /// <param name="grLines">Danh sách GR Lines từ tất cả GR của PO này</param>
    /// <returns>Trạng thái PO và thống kê</returns>
    private static (PurchaseOrderEnum Status, CalculatePOStatusResult poStatusResult) CalculatePOStatusFromGR(
        List<PurchaseOrderLine> poLines, List<CJLGrLine> grLines)
    {
        var totalLines = poLines.Count;
        var fullyReceivedLines = 0;
        var partiallyReceivedLines = 0;

        foreach (var poLine in poLines)
        {
            // Lấy tất cả GR có cùng ItemCode và DocumentType
            var matchingGrLines = grLines
                .Where(gr => gr.ItemCode == poLine.ItemNumber &&
                             gr.DocumentType.HasValue &&
                             gr.DocumentType.Value == poLine.DocumentType)
                .ToList();

            var totalGrQuantity = matchingGrLines.Sum(gr => gr.Quantity);

            if (totalGrQuantity >= poLine.Quantity)
            {
                fullyReceivedLines++;
                Console.WriteLine($"POLine {poLine.ItemNumber} Type={poLine.DocumentType}: " +
                                  $"Hoàn thành ({totalGrQuantity}/{poLine.Quantity})");
            }
            else if (totalGrQuantity > 0)
            {
                partiallyReceivedLines++;
                Console.WriteLine($"POLine {poLine.ItemNumber} Type={poLine.DocumentType}: " +
                                  $"Một phần ({totalGrQuantity}/{poLine.Quantity})");
            }
            else
            {
                Console.WriteLine($"POLine {poLine.ItemNumber} Type={poLine.DocumentType}: " +
                                  $"Chưa nhận ({totalGrQuantity}/{poLine.Quantity})");
            }
        }

        PurchaseOrderEnum status;
        string statusName;

        if (fullyReceivedLines == totalLines)
        {
            status = PurchaseOrderEnum.Completed;
            statusName = "Completed - Tất cả lines đã hoàn thành";
        }
        else if (fullyReceivedLines > 0 || partiallyReceivedLines > 0)
        {
            status = PurchaseOrderEnum.PartiallyReceived;
            statusName = "PartiallyReceived - Một số lines đã nhận hàng";
        }
        else
        {
            status = PurchaseOrderEnum.CJStatus;
            statusName = "CJStatus - Đã gửi CJ nhưng chưa nhận hàng";
        }
        var poStatusResult = new CalculatePOStatusResult
        {
            StatusName = statusName, TotalLines = totalLines, ReceivedLines = fullyReceivedLines
        };

        return (status, poStatusResult);
    }

    /// <summary>
    ///     Helper method: Kiểm tra trạng thái GR cho một PO cụ thể (logic mới: quét từ GR)
    ///     Trả về thông tin chi tiết về GR status without updating PO
    /// </summary>
    /// <param name="poNumber">PO Number cần kiểm tra</param>
    /// <returns>Thông tin chi tiết về GR status</returns>
    public async Task<ApiResponse> CheckGRStatusForPOAsync(string poNumber)
    {
        try
        {
            var poHeader = await _context.PurchaseOrderHeaders
                .FirstOrDefaultAsync(po => po.Number == poNumber);

            if (poHeader == null)
            {
                return new ApiResponse(404, $"Không tìm thấy PO {poNumber}");
            }

            // Lấy PO Lines
            var poLines = await _context.PurchaseOrderLines
                .Where(pol => pol.DocumentNumber == poHeader.Number)
                .ToListAsync();

            // Lấy GR data với navigation properties (quét từ GR trước)
            var grHeaders = await _context.CjlGrHeaders
                .Include(grh => grh.CjlGrLines)
                .Include(grh => grh.PoNumberNavigation)
                .Where(grh => grh.PoNumber == poHeader.Number)
                .ToListAsync();

            var allGrLines = grHeaders.SelectMany(grh => grh.CjlGrLines).ToList();

            // Tính toán status mà không cập nhật (sử dụng logic mới)
            var poStatus = CalculatePOStatusFromGR(poLines, allGrLines);

            var result = new
            {
                PONumber = poNumber,
                CurrentPOStatus = poHeader.Status,
                CalculatedStatus = (int)poStatus.Status,
                StatusInfo = poStatus.poStatusResult,
                GRHeaders = grHeaders.Select(grh => new
                {
                    grh.ReceivingNumber, grh.ReceivingDate, grh.Buyer, LineCount = grh.CjlGrLines.Count
                }).ToList(),
                GRLines = allGrLines.Select(grl => new
                {
                    grl.ReceivingNumber,
                    grl.LineNumber,
                    grl.ItemCode,
                    grl.DocumentType,
                    grl.Quantity,
                    grl.UnitOfMeasure
                }).ToList(),
                POLines = poLines.Select(pol => new
                {
                    pol.ItemNumber, pol.Type, pol.Quantity, pol.UnitOfMeasure
                }).ToList()
            };

            return new ApiResponse(200, "GR Status retrieved successfully", result);
        }
        catch (Exception ex)
        {
            return new ApiResponse(500, $"Error checking GR status: {ex.Message}");
        }
    }

    private class CalculatePOStatusResult
    {
        public string StatusName { get; set; }
        public int TotalLines { get; set; }
        public int ReceivedLines { get; set; }
    }
}
