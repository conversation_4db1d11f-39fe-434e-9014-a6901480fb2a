using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Localizer;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

/// <summary>
///     Controller for Front Margin workflow management
/// </summary>
[ApiController]
[Route("api/front-margin-workflow")]
public class FrontMarginWorkflowController : ControllerBase
{
    private readonly IFrontMarginWorkflowService _workflowService;
    private readonly ApiResponse _invalidData;

    public FrontMarginWorkflowController(
        IFrontMarginWorkflowService workflowService,
        IStringLocalizer<Global> i18N)
    {
        _workflowService = workflowService;
        _invalidData = new ApiResponse(Status400BadRequest, i18N["InvalidData"]);
    }

    /// <summary>
    ///     Start registration workflow for Front Margin
    /// </summary>
    [HttpPost("start-registration")]
    public async Task<ApiResponse> StartRegistrationWorkflow([FromBody] CreatePromotionFrontMarginDto dto)
    {
        if (!ModelState.IsValid)
        {
            return _invalidData;
        }

        var initiatedBy = "SYSTEM";// TODO: Get from current user context
        var result = await _workflowService.StartRegistrationWorkflowAsync(dto, initiatedBy);

        if (!result.Success)
        {
            return ApiResponse.S400(result.Message, result.ValidationErrors);
        }

        return ApiResponse.S200(result.Message, new
        {
            result.Number, result.CurrentStatus, result.NextActions, result.Warnings
        });
    }

    /// <summary>
    ///     Submit Front Margin for approval
    /// </summary>
    [HttpPost("submit-for-approval/{number}")]
    public async Task<ApiResponse> SubmitForApproval(string number)
    {
        var submittedBy = "SYSTEM";// TODO: Get from current user context
        var result = await _workflowService.SubmitForApprovalAsync(number, submittedBy);

        if (!result.Success)
        {
            return ApiResponse.S400(result.Message, result.ValidationErrors);
        }

        return ApiResponse.S200(result.Message, new
        {
            result.Number, result.CurrentStatus, result.NextActions
        });
    }

    /// <summary>
    ///     Approve Front Margin in workflow
    /// </summary>
    [HttpPost("approve/{number}")]
    public async Task<ApiResponse> ApproveInWorkflow(string number, [FromBody] WorkflowApprovalRequest request)
    {
        var approvedBy = "SYSTEM";// TODO: Get from current user context
        var result = await _workflowService.ApproveInWorkflowAsync(number, approvedBy, request.Comments);

        if (!result.Success)
        {
            return ApiResponse.S400(result.Message);
        }

        return ApiResponse.S200(result.Message, new
        {
            result.Number, result.CurrentStatus, result.NextActions
        });
    }

    /// <summary>
    ///     Reject Front Margin in workflow
    /// </summary>
    [HttpPost("reject/{number}")]
    public async Task<ApiResponse> RejectInWorkflow(string number, [FromBody] WorkflowRejectionRequest request)
    {
        if (string.IsNullOrEmpty(request.Reason))
        {
            return ApiResponse.S400("Rejection reason is required");
        }

        var rejectedBy = "SYSTEM";// TODO: Get from current user context
        var result = await _workflowService.RejectInWorkflowAsync(number, rejectedBy, request.Reason);

        if (!result.Success)
        {
            return ApiResponse.S400(result.Message);
        }

        return ApiResponse.S200(result.Message, new
        {
            result.Number, result.CurrentStatus, result.NextActions
        });
    }

    /// <summary>
    ///     Activate approved Front Margin
    /// </summary>
    [HttpPost("activate/{number}")]
    public async Task<ApiResponse> Activate(string number)
    {
        var activatedBy = "SYSTEM";// TODO: Get from current user context
        var result = await _workflowService.ActivateAsync(number, activatedBy);

        if (!result.Success)
        {
            return ApiResponse.S400(result.Message);
        }

        return ApiResponse.S200(result.Message, new
        {
            result.Number, result.CurrentStatus, result.NextActions
        });
    }

    /// <summary>
    ///     Suspend active Front Margin
    /// </summary>
    [HttpPost("suspend/{number}")]
    public async Task<ApiResponse> Suspend(string number, [FromBody] WorkflowSuspensionRequest request)
    {
        if (string.IsNullOrEmpty(request.Reason))
        {
            return ApiResponse.S400("Suspension reason is required");
        }

        var suspendedBy = "SYSTEM";// TODO: Get from current user context
        var result = await _workflowService.SuspendAsync(number, suspendedBy, request.Reason);

        if (!result.Success)
        {
            return ApiResponse.S400(result.Message);
        }

        return ApiResponse.S200(result.Message, new
        {
            result.Number, result.CurrentStatus, result.NextActions
        });
    }

    /// <summary>
    ///     Get workflow status and next possible actions
    /// </summary>
    [HttpGet("status/{number}")]
    public async Task<ApiResponse> GetWorkflowStatus(string number)
    {
        var status = await _workflowService.GetWorkflowStatusAsync(number);
        return ApiResponse.S200("Workflow status retrieved", status);
    }

    /// <summary>
    ///     Get workflow history
    /// </summary>
    [HttpGet("history/{number}")]
    public async Task<ApiResponse> GetWorkflowHistory(string number)
    {
        var history = await _workflowService.GetWorkflowHistoryAsync(number);
        return ApiResponse.S200("Workflow history retrieved", history);
    }

    /// <summary>
    ///     Check if current user can perform action on Front Margin
    /// </summary>
    [HttpGet("can-perform-action/{number}/{action}")]
    public async Task<ApiResponse> CanPerformAction(string number, string action)
    {
        var userId = "SYSTEM";// TODO: Get from current user context
        var canPerform = await _workflowService.CanPerformActionAsync(number, userId, action);

        return ApiResponse.S200("Permission checked", new
        {
            Number = number, Action = action, CanPerform = canPerform, UserId = userId
        });
    }

    /// <summary>
    ///     Get workflow dashboard data
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ApiResponse> GetWorkflowDashboard([FromQuery] string? vendorCode = null)
    {
        // This would aggregate workflow data for dashboard
        // For now, return placeholder data
        var dashboard = new
        {
            PendingSubmissions = 0,
            PendingApprovals = 0,
            ActivePromotions = 0,
            SuspendedPromotions = 0,
            RecentActions = new List<object>(),
            WorkflowMetrics = new
            {
                AverageApprovalTime = "2.5 days", ApprovalRate = "85%", RejectionRate = "15%"
            }
        };

        return ApiResponse.S200("Workflow dashboard data retrieved", dashboard);
    }

    /// <summary>
    ///     Get available workflow actions for Front Margin
    /// </summary>
    [HttpGet("available-actions/{number}")]
    public async Task<ApiResponse> GetAvailableActions(string number)
    {
        var userId = "SYSTEM";// TODO: Get from current user context
        var status = await _workflowService.GetWorkflowStatusAsync(number);

        if (status.CurrentStatus == "NOT_FOUND")
        {
            return ApiResponse.S404("Front Margin not found");
        }

        var availableActions = new List<object>();

        foreach (var action in status.NextActions)
        {
            var canPerform = await _workflowService.CanPerformActionAsync(number, userId, action);
            availableActions.Add(new
            {
                Action = action,
                CanPerform = canPerform,
                DisplayName = GetActionDisplayName(action),
                Description = GetActionDescription(action)
            });
        }

        return ApiResponse.S200("Available actions retrieved", new
        {
            Number = number, status.CurrentStatus, status.StatusDescription, AvailableActions = availableActions
        });
    }

    #region Private Methods
    private string GetActionDisplayName(string action)
    {
        return action switch
        {
            "SUBMIT_FOR_APPROVAL" => "Gửi phê duyệt",
            "APPROVE" => "Phê duyệt",
            "REJECT" => "Từ chối",
            "ACTIVATE" => "Kích hoạt",
            "SUSPEND" => "Tạm ngưng",
            "REACTIVATE" => "Kích hoạt lại",
            "DEACTIVATE" => "Hủy kích hoạt",
            "EDIT" => "Chỉnh sửa",
            "DELETE" => "Xóa",
            "EDIT_AND_RESUBMIT" => "Chỉnh sửa và gửi lại",
            _ => action
        };
    }

    private string GetActionDescription(string action)
    {
        return action switch
        {
            "SUBMIT_FOR_APPROVAL" => "Gửi Front Margin để chờ phê duyệt",
            "APPROVE" => "Phê duyệt Front Margin",
            "REJECT" => "Từ chối Front Margin",
            "ACTIVATE" => "Kích hoạt Front Margin để sử dụng",
            "SUSPEND" => "Tạm ngưng Front Margin",
            "REACTIVATE" => "Kích hoạt lại Front Margin đã tạm ngưng",
            "DEACTIVATE" => "Hủy kích hoạt Front Margin",
            "EDIT" => "Chỉnh sửa thông tin Front Margin",
            "DELETE" => "Xóa Front Margin",
            "EDIT_AND_RESUBMIT" => "Chỉnh sửa và gửi lại để phê duyệt",
            _ => "Thực hiện hành động"
        };
    }
    #endregion
}
#region Request Models
public class WorkflowApprovalRequest
{
    public string? Comments { get; set; }
}
public class WorkflowRejectionRequest
{
    public string Reason { get; set; } = string.Empty;
}
public class WorkflowSuspensionRequest
{
    public string Reason { get; set; } = string.Empty;
}
#endregion
