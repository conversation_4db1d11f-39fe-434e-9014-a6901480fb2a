﻿using System.Globalization;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using ExcelDataReader;
using Karambolo.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http.Extensions;
using MudBlazor;
using ObjectCloner.Extensions;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services;
using PurchaseManager.Theme.Material.Demo.Shared.Components;
using PurchaseManager.UI.Base.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.Promotions;

public class BackMarginPage : BaseComponent
{
    protected string PONumber = "***************";
    [CascadingParameter]
    [Inject]
    protected AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject]
    protected IViewNotifier ViewNotifier { get; set; }
    [Inject]
    protected HttpClient HttpClient { get; set; }
    [Inject]
    protected IApiClient ApiClient { get; set; }
    [Inject]
    protected NavigationManager NavigationManager { get; set; }
    protected ListFileBasePage ListFilePageRef { get; set; }
    protected MudForm EditPOLineFormRef { get; set; }
    [Inject]
    protected IDialogService DialogService { get; set; }
    [Inject]
    protected IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    [Inject]
    protected IPurchasePriceApiClient PurchasePriceApiClient { get; set; }
    [Inject]
    protected IVendorApiClient VendorApiClient { get; set; }
    [Inject]
    protected IItemApiClient ItemApiClient { get; set; }
    [Inject]
    protected POServicesHandler POServicesHandler { get; set; }
    protected POHeaderGetDto POHeader { get; set; } = new POHeaderGetDto();
    // protected DateTime? OrderDate { get; set; }
    protected GetVendorDto VendorInfo { get; set; } = new GetVendorDto();
    protected MudTable<POLineGetDto> Table { get; set; }
    // protected POLineGetDto LineEditing { get; set; } = new POLineGetDto();
    protected POLineGetDto CurrentLine { get; set; } = new POLineGetDto();
    protected List<DetailItemUnitOfMeasureDto> LsDetailItemUnitOfMeasureDtoEditing { get; set; } = [];
    protected List<POLineGetDto> POLines { get; set; } = [];
    protected HashSet<POLineGetDto> SelectedPOLine { get; set; } = [];
    protected UserViewModel UserViewModel { get; set; } = new UserViewModel();
    protected string ErrorMessage { get; set; }
    protected List<GetPurchasePriceDto> ListPurchasePriceDto { get; set; } = [];
    protected bool IsPurchaseManager { get; set; }
    protected bool IsMKT { get; set; }
    protected string SelectedVendorNumber { get; set; }

    protected bool IsWarehouse { get; set; }
    protected bool IsAdmin { get; set; }
    protected bool IsShowAddFormLine { get; set; }
    protected bool IsShowReport { get; set; }
    protected bool IsShowAddNewVendorItem { get; set; }
    protected bool IsLoad { get; set; }
    protected bool IsEdit { get; set; }
    protected bool IsShowEditPOLineDialog { get; set; }
    protected string UrlFileReport { get; set; }
    public bool Basic_Switch2 { get; set; } = true;
    protected bool AlarmOn { get; set; }
    protected string value { get; set; } = "Nothing selected";
    protected MudAutocomplete<GetVendorDto> AutoComplete { get; set; }
    protected IEnumerable<string> options { get; set; } = new HashSet<string>()
    {
        "Lion"
    };
    protected CreateFrontMarginPromotionDto CreateDto = new CreateFrontMarginPromotionDto();
    protected string[] felines =
    {
        "Jaguar", "Leopard", "Lion", "Lynx", "Panther", "Puma", "Tiger"
    };

    protected string GetMultiSelectionText(List<string> selectedValues)
    {
        return $"{selectedValues.Count} feline{(selectedValues.Count > 1 ? "s have" : " has")} been selected";
    }
    protected readonly CultureInfo _cultureInfo = CultureInfo.CurrentCulture;
    /// <summary>
    ///     30 MB
    /// </summary>
    protected const int MaxAllowedSize = 1024 * 1024 * 30;
    protected DateTime? ExpirationDateForPicker
    {
        get => CurrentLine.ExpirationDate?.ToDateTime(TimeOnly.MinValue);
        set => CurrentLine.ExpirationDate = value.HasValue
            ? DateOnly.FromDateTime(value.Value)
            : null;
    }
    protected override async Task OnInitializedAsync()
    {
        IsLoad = true;
        await GetHeaderPO(PONumber);
        await GetDetailPOAsync(PONumber);
        await GetUserRolesAsync();
        UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        IsLoad = false;
        await base.OnInitializedAsync();
    }

    protected async Task GetUserRolesAsync()
    {
        var authState = await authenticationStateTask;
        var user = authState.User;

        user.IsPurchaseUser();
        IsPurchaseManager = user.IsPurchaseManager();
        IsMKT = user.IsMKT();
        user.IsVendor();
        user.IsVendorContact();
        IsWarehouse = user.IsWarehouseUser();
        IsAdmin = user.IsAdmin();
    }

    #region Upload file PoLine
    protected async Task UploadPODetailFromFiles(IBrowserFile file)
    {
        try
        {
            IsLoad = true;
            var stream = new MemoryStream();
            await file.OpenReadStream(MaxAllowedSize).CopyToAsync(stream);
            stream.Position = 0;
            using var reader = ExcelReaderFactory.CreateReader(stream);
            var result = reader.AsDataSet(new ExcelDataSetConfiguration
            {
                ConfigureDataTable = _ => new ExcelDataTableConfiguration
                {
                    UseHeaderRow = true
                }
            });

            var memoryStreamCopy = new MemoryStream(stream.ToArray());
            var streamContent = new StreamContent(memoryStreamCopy);
            streamContent.Headers.ContentType = new MediaTypeHeaderValue(file.ContentType);
            if (file.Size > MaxAllowedSize)
            {
                ViewNotifier.Show($"File size exceeds the limit of {MaxAllowedSize} bytes.", ViewNotifierType.Error);
                return;
            }

            var table = result.Tables[0];

            var listDtPo = new List<POLinesFromFileDto>();
            for (var i = 0; i < table.Rows.Count; i++)
            {
                var row = table.Rows[i];
                if (row.ItemArray.Length < 5 || row.ItemArray[0] == null)
                {
                    ViewNotifier.Show("File không đúng định dạng. Vui lòng kiểm tra lại.", ViewNotifierType.Error);
                    return;
                }
                var poLineFromFile = new POLinesFromFileDto();
                var purchasePrice =
                    await LoadLastUnitCostByItemByVendor(poLineFromFile.PurchaseUnitOfMeasure, poLineFromFile.ItemNumber);
                if (poLineFromFile.PriceB4VAT == 0)
                {
                    poLineFromFile.PriceB4VAT = purchasePrice.UnitCost;
                }
                if (poLineFromFile.VAT == 0)
                {
                    poLineFromFile.VAT = purchasePrice.VAT;
                }
                listDtPo.Add(poLineFromFile);
            }
            if (listDtPo.Count > 0)
            {
                await CreateMultipleLinesFromFileAsync(listDtPo);
                await GetDetailPOAsync(POHeader.Number);
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
        finally
        {
            IsLoad = false;
        }
    }

    protected async Task CreateMultipleLinesFromFileAsync(List<POLinesFromFileDto> listDtPo)
    {
        try
        {
            ErrorMessage = string.Empty;
            if (listDtPo == null || listDtPo.Count == 0)
            {
                return;
            }

            var poLines = new List<POLineAddOrUpdate>();

            foreach (var poLine in listDtPo)
            {
                var isPromotion = poLine.Type.Equals("promotional", StringComparison.CurrentCultureIgnoreCase);
                var isConsignment = poLine.Type.Equals("consignment", StringComparison.CurrentCultureIgnoreCase);
                var newPOLineToCreate = new POLineGetDto
                {
                    Vat = poLine.VAT,
                    LotNo = "notset",
                    Quantity = poLine.Quantity,
                    UnitCost = poLine.PriceB4VAT,
                    UnitPrice = poLine.PriceB4VAT,
                    ItemNumber = poLine.ItemNumber,
                    LastUnitCost = poLine.PriceB4VAT,
                    Description = poLine.Description,
                    DocumentNumber = POHeader.Number,
                    LineDiscountPercent = poLine.DiscountB4VAT,
                    UnitOfMeasure = poLine.PurchaseUnitOfMeasure,
                    ExpirationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
                    DocumentType = (int)DocNoOccurrenceEnum.Order
                };
                if (isPromotion || isConsignment)// Nếu là hàng khuyến mãi ||  ký gửi
                {
                    newPOLineToCreate.DocumentType =
                        isPromotion ? (int)DocNoOccurrenceEnum.Promotional : (int)DocNoOccurrenceEnum.Consigned;
                    newPOLineToCreate.UnitCost = 0;
                }
                newPOLineToCreate.UpdateAmount();
                var poLineInsert = Mapper.Map<POLineAddOrUpdate>(newPOLineToCreate);
                poLines.Add(poLineInsert);
            }

            var apiResponse = await PurchaseOrderApiClient.AddMultipleLines(poLines);

            if (!apiResponse.IsSuccessStatusCode)
            {
                ErrorMessage = apiResponse.Message;
                ViewNotifier.Show("Validation failed", ViewNotifierType.Error);
                return;
            }
            await GetDetailPOAsync(POHeader.Number);
            ViewNotifier.Show(L["Operation Successful"], ViewNotifierType.Success);
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
    }
    #endregion


    protected void AddOrderLine()
    {
        IsShowAddFormLine = true;
        CurrentLine = new POLineGetDto
        {
            DocumentNumber = POHeader.Number,
            LotNo = "notset",
            Quantity = 1,
            QuantityToReceive = 1,
            ExpirationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
            LineNumber = (POLines == null || POLines.Count == 0 ? 0 : POLines.Select(x => x.LineNumber).Max()) + 1,
            Type = 0,
            Description = ""
        };
    }

    protected void OnVendorItemAddedCallback(bool isSuccess)
    {
        if (isSuccess)
        {
            IsShowAddNewVendorItem = false;
        }
    }

    protected async Task<List<DetailItemDto>> SearchVendorItemByName(string value)
    {
        try
        {
            var resp = await ApiClient.GetAllVendorItems(new VendorItemFilter
            {
                VendorNumber = POHeader.BuyFromVendorNumber, ItemName = value, ItemNumber = value
            });
            var mappedItems = Mapper.Map<List<DetailItemDto>>(resp.Results);
            return mappedItems;
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
            return default;
        }
    }

    protected async Task<IEnumerable<GetVendorDto>> VendorAutoComplete(string value, CancellationToken token)
    {
        var apiResponse = await
            HttpClient.GetFromJsonAsync<ApiResponseDto<List<GetVendorDto>>>($"api/vendor/search?number={value}",
            token);
        return apiResponse.IsSuccessStatusCode ? apiResponse.Result : [];
    }

    protected async Task<IEnumerable<DetailItemDto>> ItemSearch(string value, CancellationToken token)
    {
        try
        {
            IEnumerable<DetailItemDto> result = new List<DetailItemDto>();
            //if user is vendor role, get vendor items
            if (!string.IsNullOrEmpty(POHeader.BuyFromVendorNumber))
            {
                result = await SearchVendorItemByName(value);
            }

            if (result.Any())
            {
                return result;
            }
            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrEmpty(value))
            {
                value = string.Empty;
            }
            var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<List<DetailItemDto>>>(
            $"api/data/SearchItem?number={value.Trim()}", token);

            if (!apiResponse.IsSuccessStatusCode)
            {
                return result;
            }

            result = apiResponse.Result;

            return result;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return new List<DetailItemDto>();
        }
    }

    protected async Task GetDetailPOAsync(string number)
    {
        if (number.IsNullOrEmpty())
        {
            ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
            return;
        }
        try
        {
            var apiResponse = await PurchaseOrderApiClient.GetLines(PONumber);
            if (!apiResponse.IsSuccessStatusCode || apiResponse.Result == null)
            {
                ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Warning);
            }
            else
            {
                POLines = [.. apiResponse.Result.OrderByDescending(x => x.DocumentType)];
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
        }
    }
    protected async Task GetHeaderPO(string number)
    {
        if (string.IsNullOrEmpty(number))
        {
            ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
        }
        else
        {
            try
            {
                var apiResponse = await PurchaseOrderApiClient.GetHeader(PONumber);

                if (!apiResponse.IsSuccessStatusCode)
                {
                    ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
                    return;
                }
                POHeader = apiResponse.Result;
                if (POHeader != null)
                {
                    var vendorApiResponse = await VendorApiClient.GetSingleVendor(POHeader.BuyFromVendorNumber);

                    if (!vendorApiResponse.IsSuccessStatusCode)
                    {
                        ViewNotifier.Show(L["Vendor Not Found"], ViewNotifierType.Error);
                    }
                    else
                    {
                        VendorInfo = vendorApiResponse.Result;
                    }
                }
            }
            catch (Exception e)
            {
                ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
            }
        }
    }
    protected async Task SaveChangeHeaderPOOrOpenPO()
    {
        try
        {
            if (IsEdit)
            {
                await HandleEditMode();
            }
            else
            {
                await HandleOpenMode();
            }

            StateHasChanged();
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.GetBaseException().Message, ViewNotifierType.Error);
        }
    }
    protected async Task CreatePoDetailAsync()
    {
        try
        {
            var item = CurrentLine.Item;

            var foundIuom = item?.ItemUnitOfMeasures?.Where(x => x.Code == CurrentLine.UnitOfMeasure).FirstOrDefault();
            if (foundIuom != null)
            {
                CurrentLine.QtyPerUnitOfMeasure = foundIuom.QuantityPerUnitOfMeasure;
            }

            if (CurrentLine.Quantity == 0)
            {
                ViewNotifier.Show(L["Quantity cannot be zero"], ViewNotifierType.Warning);
                return;
            }

            //  promotion order
            if (POHeader.DocNoOccurrence == 2)
            {
                CurrentLine.DocumentType = 1;// gif
                CurrentLine.UnitCost = 0;
            }
            if (CurrentLine.DocumentType == (int)POLineStatusEnum.Promotion)
            {
                CurrentLine.LotNo = "KM";// KM = Khuyến mãi
                CurrentLine.UnitCost = 0;
                CurrentLine.Vat = 0;
                CurrentLine.LineDiscountAmount = 0;
                CurrentLine.Amount = 0;
            }
            // newPOLine.UnitOfMeasure = newPOLine.uni;
            CurrentLine.UpdateAmount();
            var poLineInsert = Mapper.Map<POLineAddOrUpdate>(CurrentLine);
            poLineInsert.Desc = poLineInsert.Desc ??= "";

            var apiResponseAddLine = await PurchaseOrderApiClient.AddLine(poLineInsert);

            if (!apiResponseAddLine.IsSuccessStatusCode)
            {
                ViewNotifier.Show(apiResponseAddLine.Message, ViewNotifierType.Error, L["Operation Failed"]);
                return;
            }
            await GetDetailPOAsync(PONumber);
            ViewNotifier.Show(L["Operation Successful"], ViewNotifierType.Success);
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            IsShowAddFormLine = false;
            CurrentLine = new POLineGetDto();
        }
    }
    protected async Task CallReloadFileTable()
    {
        await ListFilePageRef.ReloadFromPage();
    }
    protected void VendorApprove()
    {
        POHeader.VendorApprovalBy = string.IsNullOrEmpty(POHeader.VendorApprovalBy) ? UserViewModel.UserName : "";
    }

    protected void PurchaserApprove()
    {
        POHeader.PurchaserApprovalBy = string.IsNullOrEmpty(POHeader.PurchaserApprovalBy) ? UserViewModel.UserName : "";
    }
    protected async Task ExportDetailPo()
    {
        try
        {
            var exportFile = await POServicesHandler.ExportDetailPo(POLines, POHeader, JsRuntime);
            ViewNotifier.Show(exportFile.Message, !exportFile.IsSuccess ? ViewNotifierType.Error : ViewNotifierType.Success);
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }
    protected async Task DownloadReportPO()
    {
        var apiResponse = await PurchaseOrderApiClient.DownloadPo(POHeader.Number);

        if (!apiResponse.IsSuccessStatusCode)
        {
            ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
            return;
        }
        UrlFileReport = apiResponse.Result.ToString();
        IsShowReport = true;
        StateHasChanged();
    }
    protected void OnClickPreviewEmail()
    {
        var param = new QueryBuilder
        {
            {
                "PONumber", POHeader.Number
            },
            {
                "OrderDate", POHeader.OrderDate.ToString(CultureInfo.InvariantCulture)
            },
            {
                "ConfirmDate", POHeader.DocumentDate.ToString(CultureInfo.InvariantCulture)
            },
            {
                "VendorName", POHeader.BuyFromVendorName
            },
            {
                "VendorNumber", POHeader.BuyFromVendorNumber
            },
            {
                "PostDescription", POHeader.PostingDescription
            }
        };
        NavigationManager.NavigateTo("po/send-mail/preview" + param.ToQueryString());
    }
    protected async Task DeleteMultiLine()
    {
        try
        {
            var result = await DialogService.ShowMessageBox(
            "Warning",
            "Deleting can not be undone!",
            "Delete!", cancelText: "Cancel");
            if (result != null)
            {
                // API
                if (Table.SelectedItems != null)
                {
                    foreach (var item in Table.SelectedItems)
                    {
                        var apiResponse = await PurchaseOrderApiClient.DeleteLine(POHeader.Number, item.ItemNumber, item.RowId);
                        if (apiResponse.IsSuccessStatusCode)
                        {
                            POLines.Remove(item);
                        }
                    }
                }
                await Table.ReloadServerData();
                ViewNotifier.Show("Success", ViewNotifierType.Success);
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
    }

    protected async ValueTask DisposeAsync()
    {
        if (IsEdit)
        {
            if (POHeader.Status == (int)PurchaseOrderEnum.CancelConfirm)
            {
                await HandleEditMode();
            }
            await PurchaseOrderApiClient.CloseDocument(POHeader.Number);
        }
    }

    protected async Task OnItemSelectedInAutoComplete(DetailItemDto dto)
    {
        try
        {
            var itemNumber = dto.Number;
            var vendorNumber = POHeader.BuyFromVendorNumber.Trim();
            // Get purchase price By itemNumber and vendorNumber
            // Setting giá gốc
            if (itemNumber != null)
            {
                var apiResponse = await PurchasePriceApiClient.GetPurchasePriceAsync(itemNumber, vendorNumber);
                if (apiResponse.Result.Count == 0)
                {
                    ViewNotifier.Show("Giá tiền chưa được cài đặt.", ViewNotifierType.Warning);
                }
                else
                {
                    ListPurchasePriceDto = apiResponse.Result;
                }
            }
            if (dto.VatProductPostingGroup != null)
            {
                CurrentLine.Vat = int.Parse(dto.VatProductPostingGroup);
            }
            // Default
            CurrentLine.Item = dto;
            CurrentLine.ItemName = dto.Name;
            CurrentLine.ItemNumber = dto.Number;
            LsDetailItemUnitOfMeasureDtoEditing = dto.ItemUnitOfMeasures;
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Get Last Unit Cost has error: " + e.GetBaseException().Message, ViewNotifierType.Error);
            throw;
        }
    }

    protected async Task<GetLatestPriceDto> LoadLastUnitCostByItemByVendor(string unit, string itemNumber)
    {
        try
        {
            var resp = await PurchaseOrderApiClient.GetLatestPrice(itemNumber, unit, POHeader.BuyFromVendorNumber.Trim());

            if (resp.IsSuccessStatusCode)
            {
                var latestPrice = resp.Result;

                if (latestPrice.LastUnitCost != 0 || latestPrice.UnitCost != 0)
                {
                    return latestPrice;
                }
                ViewNotifier.Show("Liên hệ IT để sync Data ERP.", ViewNotifierType.Warning);
                return latestPrice;
            }
            ViewNotifier.Show("Get Last Unit Cost has error: " + resp.Message, ViewNotifierType.Error);

            return new GetLatestPriceDto();
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Get Last Unit Cost has error: " + e.Message, ViewNotifierType.Error);
            return new GetLatestPriceDto();
        }
    }

    protected async Task OnUOMInLineChanged(POLineGetDto dto, string unit)
    {
        try
        {
            var latestPrice = await LoadLastUnitCostByItemByVendor(unit, dto.ItemNumber);
            dto.LastUnitCost = latestPrice.LastUnitCost;
            dto.UnitCost = latestPrice.UnitCost;
            dto.UnitOfMeasure = unit;
            dto.QtyPerUnitOfMeasure = latestPrice.QtyPerUnitOfMeasure;
            dto.Vat = latestPrice.VAT;
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Get Last Unit Cost has error: " + e.Message, ViewNotifierType.Error);
        }
    }

    protected void OnDueDateChanged(DateTime? dueDate)
    {
        if (dueDate is not null)
        {
            POHeader.DueDate = dueDate.Value;
        }
    }

    protected string GetCurrencyCode()
    {
        var currencyCode = string.Empty;
        if (POHeader != null && !string.IsNullOrEmpty(POHeader.CurrencyCode))
        {
            currencyCode = POHeader.CurrencyCode;
        }
        return currencyCode;
    }
    #region editing po line
    protected async Task OnEditPOLine(POLineGetDto dto)
    {
        try
        {
            CurrentLine = new POLineGetDto();
            if (!IsEdit)
            {
                return;
            }
            var itemNumber = dto.ItemNumber;

            // Get item unit of measure by itemNumber
            var apiResponse = await ItemApiClient.GetItemUnitOfMeasure(itemNumber);
            if (apiResponse.IsSuccessStatusCode)
            {
                LsDetailItemUnitOfMeasureDtoEditing = apiResponse.Result;
            }
            CurrentLine = dto.DeepClone();
            IsShowEditPOLineDialog = true;
            IsShowAddFormLine = false;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    protected void OnCloseEditPOLineDialog()
    {
        IsShowAddFormLine = false;
        IsShowEditPOLineDialog = false;
    }
    protected async Task OnSavePOLineAsync()
    {
        CurrentLine.ExpectedReceiptDate = ExpirationDateForPicker ?? DateTime.Now;
        if (IsShowAddFormLine)
        {
            await CreatePoDetailAsync();
            return;
        }

        await EditPOLineFormRef.Validate();
        if (EditPOLineFormRef.IsValid)
        {
            var rq = Mapper.Map<POLineAddOrUpdate>(CurrentLine);
            rq.Desc = CurrentLine.Description;
            var apiResponse = await PurchaseOrderApiClient.UpdatePurchaseOrderLine(rq);
            if (!apiResponse.IsSuccessStatusCode)
            {
                ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
            }
            else
            {
                ViewNotifier.Show(L["Update success"], ViewNotifierType.Success);
                OnCloseEditPOLineDialog();
                await GetDetailPOAsync(PONumber);
            }
        }
    }

    protected async Task HandleEditMode()
    {
        if (POLines.Count == 0)
        {
            await DialogService.ShowMessageBox("Warning", "Chi tiết đang rỗng");
            return;
        }

        var headerDto = new UpdatePOHeaderDto
        {
            Number = POHeader.Number,
            VendorNo = POHeader.BuyFromVendorNumber,
            OrderDate = POHeader.OrderDate,
            DueDate = POHeader.DueDate,
            PurchaseUser = UserViewModel.UserName,
            PurchaserApprovalBy = POHeader.PurchaserApprovalBy,
            VendorApprovalBy = POHeader.VendorApprovalBy,
            YourReference = POHeader.YourReference,
            PostingDescription = POHeader.PostingDescription,
            DocNoOccurrence = POHeader.DocNoOccurrence
        };

        var (isSuccess, message) = await POServicesHandler.SaveHeaderAndProcessAsync(headerDto, POHeader);

        if (!isSuccess)
        {
            ViewNotifier.Show(message, ViewNotifierType.Error);
            return;
        }

        IsEdit = false;
        ViewNotifier.Show(L["Success"], ViewNotifierType.Success);
    }

    protected async Task HandleOpenMode()
    {
        await GetHeaderPO(PONumber);

        var openResult = await PurchaseOrderApiClient.OpenDocument(POHeader.Number);
        if (!openResult.IsSuccessStatusCode)
        {
            ViewNotifier.Show(openResult.Message, ViewNotifierType.Error);
            return;
        }
        POHeader.Status = (int)PurchaseOrderEnum.CancelConfirm;
        IsEdit = true;
    }
    #endregion
}
