-- =============================================
-- Test Front Margin Migration
-- Verify migration and test data insertion
-- Date: 2025-01-11
-- =============================================

USE [PurchaseManager] -- Change to your database name
GO

PRINT 'Testing Front Margin Migration...'
PRINT 'Database: ' + DB_NAME()
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
PRINT '============================================='

-- =============================================
-- STEP 1: Verify table structure
-- =============================================

PRINT 'Verifying table structure...'

-- Check if all required columns exist
DECLARE @MissingColumns TABLE (ColumnName VARCHAR(50))

INSERT INTO @MissingColumns (ColumnName)
SELECT 'DiscountType' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'DiscountType')
UNION ALL
SELECT 'FixedDiscountAmount' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'FixedDiscountAmount')
UNION ALL
SELECT 'BuyQuantity' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'BuyQuantity')
UNION ALL
SELECT 'GiftQuantity' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftQuantity')
UNION ALL
SELECT 'GiftItemNumber' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftItemNumber')
UNION ALL
SELECT 'GiftItemName' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftItemName')
UNION ALL
SELECT 'GiftItemUOM' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftItemUOM')
UNION ALL
SELECT 'GiftItemQuantity' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftItemQuantity')
UNION ALL
SELECT 'MinimumQuantity' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'MinimumQuantity')
UNION ALL
SELECT 'MinimumAmount' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'MinimumAmount')
UNION ALL
SELECT 'MaximumDiscountAmount' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'MaximumDiscountAmount')

IF EXISTS (SELECT * FROM @MissingColumns)
BEGIN
    PRINT '❌ Missing columns:'
    SELECT ColumnName FROM @MissingColumns
    RETURN
END
ELSE
    PRINT '✅ All required columns exist'

-- =============================================
-- STEP 2: Verify constraints
-- =============================================

PRINT 'Verifying constraints...'

DECLARE @ConstraintCount INT
SELECT @ConstraintCount = COUNT(*) 
FROM sys.check_constraints 
WHERE parent_object_id = OBJECT_ID('PromotionFrontMargins')
AND name LIKE 'CK_PromotionFrontMargins_%'

IF @ConstraintCount >= 4
    PRINT '✅ Check constraints exist (' + CAST(@ConstraintCount AS VARCHAR(10)) + ')'
ELSE
    PRINT '⚠️ Missing check constraints. Expected at least 4, found ' + CAST(@ConstraintCount AS VARCHAR(10))

-- =============================================
-- STEP 3: Test data insertion for each discount type
-- =============================================

PRINT 'Testing data insertion...'

-- First, ensure we have a test promotion header
IF NOT EXISTS (SELECT * FROM PromotionHeaders WHERE ProgramNumber = 'TEST_FM_2025')
BEGIN
    INSERT INTO PromotionHeaders (
        ProgramNumber, ProgramName, Description, VendorCode, ProgramType, 
        StartDate, EndDate, Status, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES (
        'TEST_FM_2025', 'Test Front Margin 2025', 'Test promotion for Front Margin', 
        'TEST_VENDOR', 1, '2025-01-01', '2025-12-31', 1, 
        'SYSTEM', GETDATE(), 1
    )
    PRINT '✅ Created test promotion header'
END

-- Clean up existing test data
DELETE FROM PromotionFrontMargins WHERE ProgramNumber = 'TEST_FM_2025'

BEGIN TRY
    -- Test Case I.1: Percentage Discount
    INSERT INTO PromotionFrontMargins (
        ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, MinimumQuantity, MinimumAmount,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES (
        'TEST_FM_2025', 1, 'ITEM001', 'Test Item 1', 'PCS',
        1, 10.50, 100, 1000000,
        1, 'Test percentage discount 10.5%', 'SYSTEM', GETDATE(), 1
    )
    PRINT '✅ Case I.1 (Percentage): Inserted successfully'

    -- Test Case I.2: Fixed Amount Discount
    INSERT INTO PromotionFrontMargins (
        ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, FixedDiscountAmount, MinimumAmount,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES (
        'TEST_FM_2025', 2, 'ITEM002', 'Test Item 2', 'PCS',
        2, 0, 2000000, 10000000,
        1, 'Test fixed amount discount 2M', 'SYSTEM', GETDATE(), 1
    )
    PRINT '✅ Case I.2 (Fixed Amount): Inserted successfully'

    -- Test Case II: Same Item Gift
    INSERT INTO PromotionFrontMargins (
        ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, BuyQuantity, GiftQuantity, MinimumQuantity,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES (
        'TEST_FM_2025', 3, 'ITEM003', 'Test Item 3', 'PCS',
        3, 0, 10, 1, 10,
        1, 'Test same item gift: Buy 10 get 1 free', 'SYSTEM', GETDATE(), 1
    )
    PRINT '✅ Case II (Same Item Gift): Inserted successfully'

    -- Test Case III: Different Item Gift
    INSERT INTO PromotionFrontMargins (
        ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, GiftItemNumber, GiftItemName, 
        GiftItemUOM, GiftItemQuantity, MinimumQuantity,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES (
        'TEST_FM_2025', 4, 'ITEM004', 'Test Item 4', 'PCS',
        4, 0, 'GIFT001', 'Gift Item 1', 'PCS', 5, 50,
        1, 'Test different item gift: Buy 50 get 5 gift items', 'SYSTEM', GETDATE(), 1
    )
    PRINT '✅ Case III (Different Item Gift): Inserted successfully'

END TRY
BEGIN CATCH
    PRINT '❌ Data insertion failed:'
    PRINT 'Error: ' + ERROR_MESSAGE()
    PRINT 'Line: ' + CAST(ERROR_LINE() AS VARCHAR(10))
END CATCH

-- =============================================
-- STEP 4: Test constraint validation
-- =============================================

PRINT 'Testing constraint validation...'

-- Test invalid discount type (should fail)
BEGIN TRY
    INSERT INTO PromotionFrontMargins (
        ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, Status, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES (
        'TEST_FM_2025', 99, 'INVALID', 'Invalid Item', 'PCS',
        5, 10, 1, 'SYSTEM', GETDATE(), 1  -- Invalid DiscountType = 5
    )
    PRINT '❌ Constraint validation failed: Invalid discount type was allowed'
END TRY
BEGIN CATCH
    PRINT '✅ Constraint validation passed: Invalid discount type rejected'
END CATCH

-- Test invalid percentage (should fail)
BEGIN TRY
    INSERT INTO PromotionFrontMargins (
        ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, Status, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES (
        'TEST_FM_2025', 98, 'INVALID2', 'Invalid Item 2', 'PCS',
        1, 150, 1, 'SYSTEM', GETDATE(), 1  -- Invalid percentage > 100
    )
    PRINT '❌ Constraint validation failed: Invalid percentage was allowed'
END TRY
BEGIN CATCH
    PRINT '✅ Constraint validation passed: Invalid percentage rejected'
END CATCH

-- =============================================
-- STEP 5: Display test results
-- =============================================

PRINT 'Test results:'
PRINT '============================================='

SELECT 
    LineNumber,
    ItemNumber,
    ItemName,
    DiscountType,
    CASE DiscountType
        WHEN 1 THEN 'Percentage (' + CAST(DiscountPercentage AS VARCHAR(10)) + '%)'
        WHEN 2 THEN 'Fixed Amount (' + FORMAT(FixedDiscountAmount, 'N0') + ' VND)'
        WHEN 3 THEN 'Same Item Gift (Buy ' + CAST(BuyQuantity AS VARCHAR(10)) + ' get ' + CAST(GiftQuantity AS VARCHAR(10)) + ')'
        WHEN 4 THEN 'Different Item Gift (' + CAST(GiftItemQuantity AS VARCHAR(10)) + ' ' + GiftItemName + ')'
        ELSE 'Unknown'
    END as DiscountDescription,
    Notes
FROM PromotionFrontMargins 
WHERE ProgramNumber = 'TEST_FM_2025'
ORDER BY LineNumber

-- =============================================
-- STEP 6: Performance test
-- =============================================

PRINT 'Performance test:'

DECLARE @StartTime DATETIME2 = SYSDATETIME()

-- Test query performance with new indexes
SELECT COUNT(*) as TotalRecords
FROM PromotionFrontMargins 
WHERE DiscountType = 1

SELECT COUNT(*) as GiftItemRecords
FROM PromotionFrontMargins 
WHERE GiftItemNumber IS NOT NULL

DECLARE @EndTime DATETIME2 = SYSDATETIME()
DECLARE @Duration INT = DATEDIFF(MILLISECOND, @StartTime, @EndTime)

PRINT '✅ Query performance: ' + CAST(@Duration AS VARCHAR(10)) + ' ms'

-- =============================================
-- STEP 7: Cleanup (optional)
-- =============================================

PRINT 'Cleanup test data...'

-- Uncomment the following lines to clean up test data
-- DELETE FROM PromotionFrontMargins WHERE ProgramNumber = 'TEST_FM_2025'
-- DELETE FROM PromotionHeaders WHERE ProgramNumber = 'TEST_FM_2025'
-- PRINT '✅ Test data cleaned up'

PRINT '============================================='
PRINT 'Front Margin Migration Test Completed!'
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
