@page "/stock/{DocumentNo}/{DocumentStatus}/detail"
@page "/stock/{DocumentNo}/detail"
@using PurchaseManager.Shared.Dto.PO
@using PurchaseManager.Shared.Dto.StockOrder
@using PurchaseManager.Theme.Material.Demo.Shared.Components.PO.Services
@using PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder
@attribute [Authorize]
<PageTitle>Purchase Order</PageTitle>
@if (!IsProcessing)
{
    <MudTabs Class="mt-3" PanelClass="pt-3" Rounded=true ApplyEffectsToContainer="true" MinimumTabWidth="20px" Elevation="1">
        <MudTabPanel Text="@L["List Stock Order"]">
            <MudToolBar Gutters="false" Dense Class="mb-3 px-4">
                <MudIconButton Color="Color.Default" Icon="@Icons.Material.Filled.ArrowBack"
                               OnClick="@(() => { NavigationManager.NavigateTo("/stock-order"); })" Variant="Variant.Text">
                </MudIconButton>
                <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                    <MudText Typo="Typo.h6">@L["PurchaseOrderDetail"] - @DocumentNo</MudText>
                    <MudChip T="String" Color="Color.Primary">
                        @POComponentExtension.GetStatusText(int.TryParse(DocumentStatus, out var status) ? status : 10)
                    </MudChip>
                </MudStack>
                <MudSpacer/>
                <div>
                    <MudText Class="mt-6" Typo="Typo.h6">
                        <small> @L["BuyFrom"]:</small>
                        @VendorInfo.Name
                    </MudText>
                </div>
            </MudToolBar>
            <MudCardContent>
                <MudToolBar Gutters="false" Class="" Dense="true">
                    @if (!string.IsNullOrEmpty(UserCreatedPO.UserName))
                    {
                        <MudStack Justify="Justify.SpaceBetween">
                            <MudText Typo="Typo.body2">
                                @L["CreatedBy"]:
                                <b>@UserCreatedPO.FullName</b> - @UserCreatedPO.UserName
                            </MudText>
                        </MudStack>
                    }
                    <MudSpacer/>
                    <MudText>@L["OrderDate"]: <b>@PoHeader.OrderDate.ToString("dd-MM-yyyy")</b></MudText>
                    <MudSpacer/>
                    <MudText>@L["DueDate"]: <b>@PoHeader.DueDate.ToString("dd-MM-yyyy")</b></MudText>
                    <MudSpacer/>
                    @if (PoLines.Any(x => x.TempReceive != 0))
                    {
                        <MudButton Disabled="@Validate" Size="Size.Small" OnClick="@(() => SaveChangeHeaderPoOrOpenPo(true))"
                                   Variant="Variant.Filled" Color="Color.Primary">
                            @L["Save"]
                        </MudButton>
                        <MudButton Class="mx-4" Size="Size.Small" OnClick="@(() => SaveChangeHeaderPoOrOpenPo(false))"
                                   Variant="Variant.Outlined" Color="Color.Error">
                            @L["Discard"]
                        </MudButton>
                    }
                </MudToolBar>
                <MudTable Striped="false" Bordered="true" Outlined="true" Dense="true" Hover="true" Class="mt-3"
                          Items="@PoLines" T="POLineGetDto" CanCancelEdit="true"
                          EditTrigger="TableEditTrigger.EditButton" Height="500px" FixedHeader="true" FixedFooter="true"
                          FooterClass="bold-text" CustomFooter="true">
                    <HeaderContent>
                        <MudTh Style="text-align: center">@L["Item name"]</MudTh>
                        <MudTh Style="text-align: center">@L["Quantity"]</MudTh>
                        <MudTh Style="text-align: center">@L["Received"]</MudTh>
                        <MudTh Style="text-align: center">@L["Remaining"]</MudTh>
                        <MudTh></MudTh>
                        <MudTh Style="text-align: center">@L["To received"]</MudTh>
                        <MudTh Style="text-align: center">@L["LotNo"]</MudTh>
                        <MudTh Style="text-align: center">@L["ExpirationDate"]</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd>
                            <MudText Typo="Typo.body1"></MudText> @context.ItemName
                            <MudText Typo="Typo.subtitle2"> Number: @context.ItemNumber </MudText>
                        </MudTd>
                        <MudTd Style="text-align:right">@context.Quantity.ToString("N0")</MudTd>
                        <MudTd Style="text-align:right">@context.QuantityReceived.ToString("N0")</MudTd>
                        <MudTd Style="text-align:right">
                            @context.QuantityToReceive.ToString("N0")
                        </MudTd>
                        <MudTd Style="width: 20px">
                            <MudIconButton Icon="@Icons.Material.Filled.ArrowForward" Disabled="@(IsArrowDisabled(context))"
                                           OnClick="() => OnArrowClick(context)"/>
                        </MudTd>
                        <MudTd>
                            <MudNumericField ReadOnly="@(!IsEdit)" T="Int32" Style="text-align:right" Immediate="true"
                                             Value="context.TempReceive" ValueChanged="receive => CheckToReceived(receive, context)"/>
                        </MudTd>
                        <MudTd Style="text-align:right">
                            <MudTextField ReadOnly="@(!IsEdit)" T="String" Style="text-align:right" Immediate="true"
                                          @bind-Value="context.LotNo"/>
                        </MudTd>
                        <MudTd Style="text-align:right">
                            <MudDatePicker
                                T="DateTime?"
                                @bind-Value="@context.ExpirationDate"
                                DateChanged="date => DateChanged(date, context)"
                                DateFormat="dd/MM/yyyy"
                                Editable="true"
                                Mask="@(new DateMask("dd/MM/yyyy"))"
                                ReadOnly="@(!IsEdit)"/>
                        </MudTd>
                    </RowTemplate>
                    <PagerContent>
                        <MudTablePager/>
                    </PagerContent>
                </MudTable>
            </MudCardContent>
        </MudTabPanel>
        <MudTabPanel Text="PO">
            <div class="px-4">
                <PODetails PONumber="@DocumentNo"/>
            </div>
        </MudTabPanel>
        @* <MudTabPanel Text="@L["ReceivedOrders"]"> *@
        @*     <MudCardHeader> *@
        @*         <CardHeaderContent> *@
        @*             <MudStack Row Spacing="6"> *@
        @*                 <div> *@
        @*                     <MudStack Row Class="my-4" Justify="Justify.SpaceBetween"> *@
        @*                         <MudStack Row> *@
        @*                             <MudFab Size="Size.Small" DropShadow="false" StartIcon="@Icons.Material.Filled.ArrowBack" *@
        @*                                     OnClick="@(() => { NavigationManager.NavigateTo("/stock-order"); })"/> *@
        @*                             <MudStack Justify="Justify.Center"> *@
        @*                                 <MudText Typo="Typo.h6">@L["Stock detail"] - @DocumentNo</MudText> *@
        @*                             </MudStack> *@
        @*                         </MudStack> *@
        @*                     </MudStack> *@
        @*                 </div> *@
        @*             </MudStack> *@
        @*         </CardHeaderContent> *@
        @*         <CardHeaderActions> *@
        @*             <MudText Class="mt-6" Typo="Typo.h6"> *@
        @*                 <small> @L["BuyFrom"]:</small> *@
        @*                 @VendorInfo.Name *@
        @*             </MudText> *@
        @*         </CardHeaderActions> *@
        @*     </MudCardHeader> *@
        @*     <MudCardContent> *@
        @*         <MudToolBar Gutters="false" Class="" Dense="true"> *@
        @*             @if (!string.IsNullOrEmpty(UserCreatedPO.UserName)) *@
        @*             { *@
        @*                 <MudStack Justify="Justify.SpaceBetween"> *@
        @*                     <MudText Typo="Typo.body2"> *@
        @*                         @L["CreatedBy"]: *@
        @*                         <b>@UserCreatedPO.FullName</b> - @UserCreatedPO.UserName *@
        @*                     </MudText> *@
        @*                 </MudStack> *@
        @*             } *@
        @*             <MudSpacer/> *@
        @*             <MudText>@L["OrderDate"]: <b>@PoHeader.OrderDate.ToString("dd-MM-yyyy")</b></MudText> *@
        @*             <MudSpacer/> *@
        @*             <MudText>@L["DueDate"]: <b>@PoHeader.DueDate.ToString("dd-MM-yyyy")</b></MudText> *@
        @*         </MudToolBar> *@
        @*     </MudCardContent> *@
        @*     <MudTable Loading="@IsProcessing" T="GetStockOrderDto" TotalItems="@TotalItems" *@
        @*               Items="@ListStockOrderDtos" *@
        @*               ServerData="@(new Func<TableState, CancellationToken, Task<TableData<GetStockOrderDto>>>(ServerReload))" Dense *@
        @*               Hover> *@
        @*         <HeaderContent> *@
        @*             <MudTh>@L["PO Number"]</MudTh> *@
        @*             <MudTh>@L["Item"]</MudTh> *@
        @*             <MudTh>@L["LotNo"]</MudTh> *@
        @*             <MudTh>@L["ExpirationDate"]</MudTh> *@
        @*             <MudTh>@L["TotalQuantity"]</MudTh> *@
        @*             <MudTh>@L["QuantityReceived"]</MudTh> *@
        @*             <MudTh>@L["WarehouseReceiver"]</MudTh> *@
        @*         </HeaderContent> *@
        @*         <RowTemplate> *@
        @*             <MudTd DataLabel="PONumber" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;"> *@
        @*                 @context.HeaderNumber.ToUpper() *@
        @*             </MudTd> *@
        @*             <MudTd DataLabel="@L["Item"]" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;"> *@
        @*                 <MudText Typo="Typo.body1"></MudText> @context.ItemName *@
        @*                 <MudText Typo="Typo.subtitle2"> Number: @context.ItemNumber </MudText> *@
        @*             </MudTd> *@
        @*             <MudTd DataLabel="LotNo" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;"> *@
        @*                 @context.LotNo.ToUpper() *@
        @*             </MudTd> *@
        @*             <MudTd DataLabel="ExpirationDate" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;"> *@
        @*                 @context.ExpirationDate.ToString("dd/MM/yyyy") *@
        @*             </MudTd> *@
        @*             <MudTd DataLabel="TotalQuantity" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;"> *@
        @*                 @context.TotalQuantity *@
        @*             </MudTd> *@
        @*             <MudTd DataLabel="QuantityReceived" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;"> *@
        @*                 @context.QuantityReceived *@
        @*             </MudTd> *@
        @*             <MudTd DataLabel="Note" Style="overflow-wrap: break-word;white-space: nowrap;overflow: hidden;"> *@
        @*                 @context.CreateBy *@
        @*             </MudTd> *@
        @*         </RowTemplate> *@
        @*         <PagerContent> *@
        @*             <MudTablePager/> *@
        @*         </PagerContent> *@
        @*         <NoRecordsContent> *@
        @*             No Data *@
        @*         </NoRecordsContent> *@
        @*     </MudTable> *@
        @* </MudTabPanel> *@
    </MudTabs>
}
else
{
    <MudProgressLinear Indeterminate="@IsProcessing"/>
}