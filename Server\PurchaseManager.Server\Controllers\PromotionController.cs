using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Promotions;
using static Microsoft.AspNetCore.Http.StatusCodes;


namespace PurchaseManager.Server.Controllers;

[ApiController]
[Route("api/[controller]")]
public partial class PromotionController : ControllerBase
{
    private readonly IPromotionManager _promotionManager;
    private readonly IPromotionService _promotionService;
    private readonly ApiResponse _invalidData;

    public PromotionController(IPromotionManager promotionManager, IPromotionService promotionService, IStringLocalizer<Global> i18N)
    {
        _promotionManager = promotionManager;
        _promotionService = promotionService;
        _invalidData = new ApiResponse(Status400BadRequest, i18N["InvalidData"]);
    }

    [HttpGet]
    public async Task<ApiResponse<List<GetPromotionHeaderDto>>> GetPromotions()
    {
        return await _promotionManager.GetPromotionsAsync();
    }

    [HttpGet("filtered")]
    public async Task<ApiResponse> GetPromotionsFiltered([FromQuery] PromotionFilter filter)
    {
        return await _promotionManager.GetAllPromotionsAsync(filter);
    }

    [HttpGet("{number}")]
    public async Task<ApiResponse<GetPromotionHeaderDto>> GetPromotionByNumber(string number)
    {
        return await _promotionManager.GetPromotionByNumberAsync(number);
    }

    [HttpPost]
    public async Task<ApiResponse> CreatePromotion([FromBody] CreatePromotionHeaderDto createDto)
    {
        return await _promotionManager.CreatePromotionAsync(createDto);
    }

    [HttpPut("{number}")]
    public async Task<ApiResponse> UpdatePromotion(string number, [FromBody] UpdatePromotionHeaderDto updateDto)
    {
        return await _promotionManager.UpdatePromotionAsync(number, updateDto);
    }

    [HttpDelete("bulk")]
    public async Task<ApiResponse> DeletePromotions([FromBody] List<string> numbers)
    {
        return await _promotionManager.DeletePromotionAsync(numbers);
    }

    [AllowAnonymous]
    [HttpPut("open-document/{number}")]
    public async Task<ApiResponse> OpenDocument(string number)
    {
        return ModelState.IsValid ? await _promotionService.OpenDocument(number) : _invalidData;
    }

    [AllowAnonymous]
    [HttpPut("close-document/{number}")]
    public async Task<ApiResponse> CloseDocument(string number)
    {
        return ModelState.IsValid ? await _promotionManager.CloseDocumentAsync(number) : _invalidData;
    }
}
