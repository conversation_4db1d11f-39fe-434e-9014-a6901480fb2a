namespace PurchaseManager.Shared.Dto.Reports;

/// <summary>
/// DTO for Back Margin Report by Item (Report 1)
/// Báo cáo Back Margin theo mã hàng
/// </summary>
public class BackMarginByItemReportDto
{
    public int STT { get; set; }
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public string UnitOfMeasure { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public string VendorName { get; set; } = string.Empty;
    public string PromotionNumber { get; set; } = string.Empty;
    public string PromotionName { get; set; } = string.Empty;

    /// <summary>
    /// Số lượng mua của Item trong chương trình (1)
    /// </summary>
    public decimal PurchaseQuantity { get; set; }

    /// <summary>
    /// Back Margin nhận được (2)
    /// Công thức: Số tiền thưởng Back Margin tối thiểu theo CT / Tổng doanh số mua theo CT * Doanh số mua của Item
    /// </summary>
    public decimal BackMargin { get; set; }

    /// <summary>
    /// Back Margin/Đơn vị (3) = (2)/(1)
    /// </summary>
    public decimal BackMarginPerUnit { get; set; }

    public string CalculationPeriod { get; set; } = string.Empty;
    public DateTime PeriodStartDate { get; set; }
    public DateTime PeriodEndDate { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// DTO for Profit Report by Item (Report 2)
/// Báo cáo lợi nhuận theo mã hàng
/// </summary>
public class ProfitByItemReportDto
{
    public int STT { get; set; }
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public string UnitOfMeasure { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public string VendorName { get; set; } = string.Empty;

    /// <summary>
    /// Số lượng bán (1) - từ hệ thống bán hàng
    /// </summary>
    public decimal SalesQuantity { get; set; }

    /// <summary>
    /// Nguyên giá bình quân (giá gốc từ vendor)
    /// </summary>
    public decimal AverageOriginalPrice { get; set; }

    /// <summary>
    /// Giá mua bình quân (đã trừ chiết khấu trực tiếp) (2)
    /// </summary>
    public decimal AveragePurchasePrice { get; set; }

    /// <summary>
    /// Giá bán bình quân trong kỳ (3)
    /// </summary>
    public decimal AverageSalesPrice { get; set; }

    /// <summary>
    /// Front Margin (4) = [(3) - (2)] * (1)
    /// Lợi nhuận từ việc bán với giá cao hơn giá mua
    /// </summary>
    public decimal FrontMargin { get; set; }

    /// <summary>
    /// Back Margin (5) = Back margin đơn vị * (1)
    /// Thưởng nhận được từ việc mua hàng
    /// </summary>
    public decimal BackMargin { get; set; }

    /// <summary>
    /// Back Margin trên đơn vị
    /// </summary>
    public decimal BackMarginPerUnit { get; set; }

    /// <summary>
    /// Tổng Margin (6) = (4) + (5)
    /// </summary>
    public decimal TotalMargin { get; set; }

    /// <summary>
    /// % Tổng Margin (7) = (6) / [(3) * (1)]
    /// </summary>
    public decimal TotalMarginPercentage { get; set; }

    /// <summary>
    /// Tổng doanh thu = (3) * (1)
    /// </summary>
    public decimal TotalRevenue { get; set; }

    /// <summary>
    /// Tổng chi phí mua = (2) * số lượng mua
    /// </summary>
    public decimal TotalPurchaseCost { get; set; }

    public string ReportPeriod { get; set; } = string.Empty;
    public DateTime PeriodStartDate { get; set; }
    public DateTime PeriodEndDate { get; set; }
}

/// <summary>
/// DTO for Purchase Amount by Program Report
/// </summary>
public class PurchaseAmountByProgramReportDto
{
    public int STT { get; set; }
    public string PromotionNumber { get; set; } = string.Empty;
    public string PromotionName { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public string VendorName { get; set; } = string.Empty;
    public string SupportTypeName { get; set; } = string.Empty;
    public string DiscountTypeName { get; set; } = string.Empty;

    /// <summary>
    /// Mốc doanh số thưởng (1)
    /// </summary>
    public decimal MilestoneAmount { get; set; }

    /// <summary>
    /// Doanh số đã mua (2)
    /// </summary>
    public decimal PurchasedAmount { get; set; }

    /// <summary>
    /// Doanh số cần mua thêm (3) = (1) - (2)
    /// </summary>
    public decimal RemainingAmount { get; set; }

    /// <summary>
    /// % Hoàn thành = (2) / (1) * 100
    /// </summary>
    public decimal CompletionPercentage { get; set; }

    public DateTime ProgramStartDate { get; set; }
    public DateTime ProgramEndDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public string CalculationPeriod { get; set; } = string.Empty;
}

/// <summary>
/// Filter for Back Margin reports
/// </summary>
public class BackMarginReportFilterDto
{
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public string? ItemNumber { get; set; }
    public string? VendorCode { get; set; }
    public string? PromotionNumber { get; set; }
    public int? Status { get; set; }
    public int PageNumber { get; set; } = 1;
    public int PageSize { get; set; } = 50;
}

/// <summary>
/// Summary for Back Margin reports
/// </summary>
public class BackMarginReportSummaryDto
{
    public int TotalItems { get; set; }
    public decimal TotalPurchaseQuantity { get; set; }
    public decimal TotalPurchaseAmount { get; set; }
    public decimal TotalBackMargin { get; set; }
    public decimal TotalSalesQuantity { get; set; }
    public decimal TotalRevenue { get; set; }
    public decimal TotalFrontMargin { get; set; }
    public decimal TotalMargin { get; set; }
    public decimal AverageMarginPercentage { get; set; }
}
