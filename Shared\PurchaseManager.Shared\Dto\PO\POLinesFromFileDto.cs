namespace PurchaseManager.Shared.Dto.PO;

public class POLinesFromFileDto
{
    public string ItemNumber { get; set; } = null!;
    public int Quantity { get; set; }
    public string PurchaseUnitOfMeasure { get; set; } = null!;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // promotion: hàng KM; consignment:  hàng ký g<PERSON>i; để trống là sản phẩm được mua thông thường
    public decimal PriceB4VAT { get; set; } = 0; // giá trước vat Price (-vat)
    public decimal VAT { get; set; } = 0; // giá trước vat Price (-vat)
    public decimal DiscountB4VAT { get; set; } = 0; // % discount trước vat discount(-vat)
}
