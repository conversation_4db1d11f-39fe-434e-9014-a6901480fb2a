root = true
###############################
# Core EditorConfig Options   #
###############################
# All files
[*]
indent_style = space
dotnet_diagnostic.CS8632.severity = none
dotnet_diagnostic.IDE0028.severity = none
# Microsoft .NET properties
csharp_indent_braces = false
csharp_new_line_before_open_brace = all
# var preferences
csharp_style_var_elsewhere = true:suggestion
csharp_style_var_for_built_in_types = true:suggestion
csharp_style_var_when_type_is_apparent = true:suggestion

dotnet_naming_rule.unity_serialized_field_rule.severity = suggestion
dotnet_naming_rule.unity_serialized_field_rule.style = lower_camel_case_style
dotnet_naming_rule.unity_serialized_field_rule.symbols = unity_serialized_field_symbols
dotnet_naming_style.lower_camel_case_style.capitalization = camel_case
dotnet_naming_symbols.unity_serialized_field_symbols.applicable_accessibilities = *
dotnet_naming_symbols.unity_serialized_field_symbols.applicable_kinds =
dotnet_style_parentheses_in_arithmetic_binary_operators = never_if_unnecessary:none
dotnet_style_parentheses_in_other_binary_operators = always_for_clarity:none
dotnet_style_parentheses_in_relational_binary_operators = never_if_unnecessary:none
dotnet_style_require_accessibility_modifiers = for_non_interface_members:suggestion

# ReSharper properties
resharper_accessor_owner_body = accessors_with_block_body
resharper_align_linq_query = true
resharper_arguments_anonymous_function = named
resharper_arguments_skip_single = true
resharper_blank_lines_after_block_statements = 0
resharper_blank_lines_after_start_comment = 0
resharper_blank_lines_after_using_list = 0
resharper_blank_lines_around_auto_property = 0
resharper_blank_lines_around_property = 0
resharper_braces_for_ifelse = not_required
resharper_braces_for_lock = not_required
resharper_braces_for_using = required_for_multiline
resharper_braces_redundant = false
resharper_csharp_allow_comment_after_lbrace = true
resharper_csharp_blank_lines_around_field = 0
resharper_csharp_blank_lines_around_invocable = 0
resharper_csharp_blank_lines_around_namespace = 0
resharper_csharp_blank_lines_around_region = 0
resharper_csharp_blank_lines_around_type = 0
resharper_csharp_blank_lines_inside_region = 0
resharper_csharp_indent_invocation_pars = none
resharper_csharp_indent_method_decl_pars = outside
resharper_csharp_insert_final_newline = true
resharper_csharp_keep_blank_lines_in_declarations = 100
resharper_csharp_max_line_length = 135
resharper_csharp_remove_blank_lines_near_braces_in_code = false
resharper_csharp_remove_blank_lines_near_braces_in_declarations = false
resharper_csharp_space_around_alias_eq = false
resharper_csharp_space_before_trailing_comment = false
resharper_csharp_wrap_ternary_expr_style = wrap_if_long
resharper_indent_nested_fixed_stmt = true
resharper_indent_nested_foreach_stmt = true
resharper_indent_nested_for_stmt = true
resharper_indent_nested_lock_stmt = true
resharper_indent_nested_usings_stmt = true
resharper_indent_nested_while_stmt = true
resharper_indent_pars = outside
resharper_indent_preprocessor_other = do_not_change
resharper_instance_members_qualify_declared_in =
resharper_keep_existing_attribute_arrangement = true
resharper_keep_existing_declaration_block_arrangement = true
resharper_keep_existing_embedded_block_arrangement = true
resharper_keep_existing_initializer_arrangement = false
resharper_max_attribute_length_for_same_line = 10000
resharper_method_or_operator_body = expression_body
resharper_object_creation_when_type_evident = explicitly_typed
resharper_parentheses_redundancy_style = remove
resharper_place_accessorholder_attribute_on_same_line = false
resharper_place_expr_accessor_on_single_line = true
resharper_place_expr_property_on_single_line = true
resharper_place_field_attribute_on_same_line = false
resharper_place_method_attribute_on_same_line = if_owner_is_single_line
resharper_place_simple_declaration_blocks_on_single_line = false
resharper_place_simple_embedded_statement_on_same_line = false
resharper_place_simple_initializer_on_single_line = false
resharper_space_between_attribute_sections = false
resharper_space_within_empty_braces = false
resharper_wrap_array_initializer_style = chop_if_long
resharper_wrap_before_arrow_with_expressions = true
resharper_wrap_before_primary_constructor_declaration_lpar = false
resharper_wrap_object_and_collection_initializer_style = chop_always

# ReSharper inspection severities

# XML project files
[*.{csproj,vbproj,vcxproj,vcxproj.filters,proj,projitems,shproj}]
indent_size = 2

# XML config files
[*.{props,targets,ruleset,config,nuspec,resx,vsixmanifest,vsct}]
indent_size = 2

# Code files
[*.{cs,csx,vb,vbx}]
indent_size = 4
insert_final_newline = true
charset = utf-8
###############################
# .NET Coding Conventions     #
###############################
[*.{cs,vb}]
# Organize usings
dotnet_sort_system_directives_first = true
# this. preferences
dotnet_style_qualification_for_field = false:silent
dotnet_style_qualification_for_property = false:silent
dotnet_style_qualification_for_method = false:silent
dotnet_style_qualification_for_event = false:silent
# Language keywords vs BCL types preferences
dotnet_style_predefined_type_for_locals_parameters_members = true:silent
dotnet_style_predefined_type_for_member_access = true:silent
# Parentheses preferences
dotnet_style_parentheses_in_other_operators = never_if_unnecessary:silent
# Modifier preferences
dotnet_style_readonly_field = true:suggestion
# Expression-level preferences
dotnet_style_object_initializer = true:suggestion
dotnet_style_collection_initializer = true:suggestion
dotnet_style_explicit_tuple_names = true:suggestion
dotnet_style_null_propagation = true:suggestion
dotnet_style_coalesce_expression = true:suggestion
dotnet_style_prefer_is_null_check_over_reference_equality_method = true:silent
dotnet_style_prefer_inferred_tuple_names = true:suggestion
dotnet_style_prefer_inferred_anonymous_type_member_names = true:suggestion
dotnet_style_prefer_auto_properties = true:silent
dotnet_style_prefer_conditional_expression_over_assignment = true:silent
dotnet_style_prefer_conditional_expression_over_return = true:silent
###############################
# Naming Conventions          #
###############################
# Style Definitions
dotnet_naming_style.pascal_case_style.capitalization = pascal_case
# Use PascalCase for constant fields
dotnet_naming_rule.constant_fields_should_be_pascal_case.severity = suggestion
dotnet_naming_rule.constant_fields_should_be_pascal_case.symbols = constant_fields
dotnet_naming_rule.constant_fields_should_be_pascal_case.style = pascal_case_style
dotnet_naming_symbols.constant_fields.applicable_kinds = field
dotnet_naming_symbols.constant_fields.applicable_accessibilities = *
dotnet_naming_symbols.constant_fields.required_modifiers = const
tab_width = 4
dotnet_naming_rule.private_members_with_underscore.symbols = private_fields
dotnet_naming_rule.private_members_with_underscore.style = prefix_underscore
dotnet_naming_rule.private_members_with_underscore.severity = suggestion
dotnet_naming_symbols.private_fields.applicable_kinds = field
dotnet_naming_symbols.private_fields.applicable_accessibilities = private
dotnet_naming_style.prefix_underscore.capitalization = camel_case
dotnet_naming_style.prefix_underscore.required_prefix = _
dotnet_style_operator_placement_when_wrapping = beginning_of_line
end_of_line = crlf
dotnet_style_prefer_simplified_boolean_expressions = true:suggestion
###############################
# C# Coding Conventions       #
###############################
[*.cs]
# Expression-bodied members
csharp_style_expression_bodied_methods = false:silent
csharp_style_expression_bodied_constructors = false:silent
csharp_style_expression_bodied_operators = false:silent
csharp_style_expression_bodied_properties = true:silent
csharp_style_expression_bodied_indexers = true:silent
csharp_style_expression_bodied_accessors = true:silent
# Pattern matching preferences
csharp_style_pattern_matching_over_is_with_cast_check = true:suggestion
csharp_style_pattern_matching_over_as_with_null_check = true:suggestion
# Null-checking preferences
csharp_style_throw_expression = true:suggestion
csharp_style_conditional_delegate_call = true:suggestion
# Modifier preferences
csharp_preferred_modifier_order = public, private, protected, internal, static, extern, new, virtual, abstract, sealed, override, readonly, unsafe, volatile, async:suggestion
# Expression-level preferences
csharp_prefer_braces = true:silent
csharp_preserve_single_line_statements = true
csharp_preserve_single_line_blocks = true
csharp_style_deconstructed_variable_declaration = true:suggestion
csharp_prefer_simple_default_expression = true:suggestion
csharp_style_pattern_local_over_anonymous_function = true:suggestion
csharp_style_inlined_variable_declaration = true:suggestion
# Namespaces
###############################
# C# Formatting Rules         #
###############################
# New line preferences
csharp_new_line_before_else = true
csharp_new_line_before_catch = true
csharp_new_line_before_finally = true
csharp_new_line_before_members_in_object_initializers = true
csharp_new_line_before_members_in_anonymous_types = true
csharp_new_line_between_query_expression_clauses = true
# Indentation preferences
csharp_indent_case_contents = true
csharp_indent_switch_labels = true
csharp_indent_labels = flush_left
# Space preferences
csharp_space_after_cast = false
csharp_space_after_keywords_in_control_flow_statements = true
csharp_space_between_method_call_parameter_list_parentheses = false
csharp_space_between_method_declaration_parameter_list_parentheses = false
csharp_space_between_parentheses = false
csharp_space_before_colon_in_inheritance_clause = true
csharp_space_after_colon_in_inheritance_clause = true
csharp_space_around_binary_operators = before_and_after
csharp_space_between_method_declaration_empty_parameter_list_parentheses = false
csharp_space_between_method_call_name_and_opening_parenthesis = false
csharp_space_between_method_call_empty_parameter_list_parentheses = false
# Wrapping preferences
csharp_using_directive_placement = outside_namespace:suggestion
csharp_prefer_simple_using_statement = true:suggestion
csharp_style_expression_bodied_lambdas = true:silent
csharp_style_expression_bodied_local_functions = false:silent
###############################
# VB Coding Conventions       #
###############################

[*.{appxmanifest,asax,ascx,aspx,axaml,build,c,c++,c++m,cc,ccm,cginc,compute,cp,cpp,cppm,cs,cshtml,cu,cuh,cxx,cxxm,dtd,fs,fsi,fsscript,fsx,fx,fxh,h,hh,hlsl,hlsli,hlslinc,hpp,hxx,inc,inl,ino,ipp,ixx,master,ml,mli,mpp,mq4,mq5,mqh,mxx,nuspec,paml,razor,resw,resx,shader,skin,tpp,usf,ush,uxml,vb,xaml,xamlx,xoml,xsd}]
indent_size = 4
dotnet_style_prefer_compound_assignment = true:suggestion
dotnet_style_prefer_simplified_interpolation = true:suggestion
# .editorconfig - disable nullable reference types (avoids CS8632)
