using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Server.Services.Promotions.Interface;

/// <summary>
///     Service for Front Margin registration workflow and lifecycle management
/// </summary>
public interface IFrontMarginWorkflowService
{
    /// <summary>
    ///     Start registration workflow for Front Margin
    /// </summary>
    Task<FrontMarginWorkflowResult> StartRegistrationWorkflowAsync(CreatePromotionFrontMarginDto dto, string initiatedBy);

    /// <summary>
    ///     Submit Front Margin for approval
    /// </summary>
    Task<FrontMarginWorkflowResult> SubmitForApprovalAsync(string number, string submittedBy);

    /// <summary>
    ///     Approve Front Margin in workflow
    /// </summary>
    Task<FrontMarginWorkflowResult> ApproveInWorkflowAsync(string number, string approvedBy, string? comments = null);

    /// <summary>
    ///     Reject Front Margin in workflow
    /// </summary>
    Task<FrontMarginWorkflowResult> RejectInWorkflowAsync(string number, string rejectedBy, string reason);

    /// <summary>
    ///     Activate approved Front Margin
    /// </summary>
    Task<FrontMarginWorkflowResult> ActivateAsync(string number, string activatedBy);

    /// <summary>
    ///     Suspend active Front Margin
    /// </summary>
    Task<FrontMarginWorkflowResult> SuspendAsync(string number, string suspendedBy, string reason);

    /// <summary>
    ///     Get workflow status and next possible actions
    /// </summary>
    Task<FrontMarginWorkflowStatus> GetWorkflowStatusAsync(string number);

    /// <summary>
    ///     Get workflow history
    /// </summary>
    Task<List<FrontMarginWorkflowHistory>> GetWorkflowHistoryAsync(string number);

    /// <summary>
    ///     Check if user can perform action on Front Margin
    /// </summary>
    Task<bool> CanPerformActionAsync(string number, string userId, string action);
}
