﻿@page "/stock-order"
@page "/stock-order/vendor/{vendorNumberParam}/{vendorNameParam}"
@using PurchaseManager.Shared.Dto.Item
@using PurchaseManager.Theme.Material.Demo.Shared.Components.PO
@attribute [Authorize]

<MudToolBar Gutters="false" Class="mb-2">
    <MudText Typo="Typo.h6">Stock Order</MudText>
    <MudSpacer/>
    <MudIconButton Icon="@Icons.Material.Outlined.Refresh" OnClick="@(() => Reload())" Size="Size.Small"/>
</MudToolBar>
<MudGrid Spacing="12" Class="mb-2">
    <MudItem sm="2">
        <MudDateRangePicker PickerVariant="PickerVariant.Inline" Variant="Variant.Text"
                            PlaceholderStart="Start Date" Margin="Margin.Dense" PlaceholderEnd="End Date"
                            @bind-DateRange="DateRange" Label="Tìm theo ngày tạo" AutoClose="@false">
        </MudDateRangePicker>
    </MudItem>
    <MudItem sm="2">
        <MudStack Row>
            <MudAutocomplete T="GetVendorDto" ShrinkLabel ListItemClass="m-0" ResetValueOnEmptyText
                             Label="Tìm theo nhà cung cấp" ShowProgressIndicator Value="@VendorFilter"
                             ValueChanged="@OnSearchByVendor"
                             ToStringFunc="@(dto => dto.Number is null ? L["ALL"] : string.Concat(dto.Number, " - ", dto.Name))"
                             SearchFunc="@VendorSearch" Margin="Margin.Dense">
                <ProgressIndicatorInPopoverTemplate>
                    <MudList T="String" ReadOnly>
                        <MudListItem>
                            Loading...
                        </MudListItem>
                    </MudList>
                </ProgressIndicatorInPopoverTemplate>
                <ItemTemplate Context="e">
                    <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                        <MudText>@e.Name</MudText>
                        <MudChip Size="Size.Small" Color="@(e.Blocked == 1 ? Color.Default : Color.Success)"
                                 T="String">@(e.Blocked == 1 ? "Block" : "Active")</MudChip>
                    </MudStack>
                    <MudText Typo="Typo.body2"><b>Number: </b>@e.Number</MudText>
                </ItemTemplate>
            </MudAutocomplete>
            <div class="mt-auto">
                <MudIconButton Size="Size.Small" class="cursor:pointer" OnClick="@OnClearFilterByVendor"
                               Icon="@Icons.Material.Filled.Clear"/>
            </div>
        </MudStack>
    </MudItem>
    <MudItem sm="2">
        <AuthorizeView Policy="@Policies.IsWarehouseUser">
            <MudStack Row>
                <MudSelect T="StockOrderFilerEnum" Value="_selectedStatus"
                           ValueChanged="@(status => OnSearchByStatus(status))" Label="Status"
                           Margin="Margin.Dense">
                    @foreach (var status in
                                  Enum.GetValues(typeof(StockOrderFilerEnum)).Cast<StockOrderFilerEnum>())
                    {
                        <MudSelectItem Value="@status">@GetMarginLabel(status)</MudSelectItem>
                    }
                </MudSelect>
                <div class="mt-auto">
                    <MudIconButton Size="Size.Small" class="cursor:pointer"
                                   OnClick="@OnClearFilterByApproveStatus" Icon="@Icons.Material.Filled.Clear"/>
                </div>
            </MudStack>
        </AuthorizeView>
    </MudItem>
    <MudItem sm="2">
        <MudStack Row>
            <MudAutocomplete T="DetailItemDto" ShrinkLabel Label="Item Number" Value="@ItemFilter"
                             ShowProgressIndicator ValueChanged="@(dto => OnItemSelectedInAutoComplete(dto))"
                             ToStringFunc="@(dto => string.Concat(dto.Number, ' ', '-', ' ', dto.Name))"
                             Required
                             SearchFunc="@ItemSearch" Margin="Margin.Dense" Dense>
                <ProgressIndicatorInPopoverTemplate>
                    <MudList T="String" ReadOnly>
                        <MudListItem>
                            Loading...
                        </MudListItem>
                    </MudList>
                </ProgressIndicatorInPopoverTemplate>
                <ItemTemplate Context="e">
                    <MudStack Row="false" StretchItems="StretchItems.All">
                        <MudStack Spacing="0">
                            <MudText>@e.Name</MudText>
                            <MudStack Row Spacing="0" Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center">
                                <MudText Typo="Typo.caption">@e.Number</MudText>
                                <MudChip T="String" Size="Size.Small" Variant="Variant.Text"
                                         Color="@(e.Blocked == 1 ? Color.Warning : Color.Success)">@(e.Blocked == 1 ? "Blocked" : "Active")</MudChip>
                                @* <MudChip T="String" Size="Size.Small" Variant="Variant.Text" *@
                                @*          Color="@(e.Status == 2 ? Color.Success : Color.Warning)"> *@
                                @*     Status *@
                                @* </MudChip> *@
                            </MudStack>
                        </MudStack>
                    </MudStack>
                </ItemTemplate>
            </MudAutocomplete>
            <div class="mt-auto">
                <MudIconButton Size="Size.Small" class="cursor:pointer"
                               OnClick="@OnClearFilterByItemNumber" Icon="@Icons.Material.Filled.Clear"/>
            </div>
        </MudStack>
    </MudItem>
</MudGrid>
@if (UserViewModel is not null)
{
    <ListPOByUser @ref="ListPOByUserRef" FromDate="@DateRange.Start" VendorNumber="@VendorNumberQuery"
                  UserName="@UserViewModel.UserName" StatusFilter="@StatusFilter" ToDate="@DateRange.End" ItemNumberParam="@ItemNumberParam"/>
}
