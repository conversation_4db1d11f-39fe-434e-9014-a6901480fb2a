using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.PO;
namespace PurchaseManager.Theme.Material.Demo.Shared.Components.PO;

public partial class ListPOByUser : ComponentBase
{
    [Required]
    [Parameter]
    public string VendorNumber { get; set; } = "";
    [Parameter]
    public string ContactNumber { get; set; } = "";
    [Parameter]
    public string DescriptionFilter { get; set; }
    [Parameter]
    public string PONumberFilter { get; set; }
    [Parameter]
    public string ItemNumberParam { get; set; } = "";
    [Parameter]
    public int? StatusFilter { get; set; }
    /// <summary>
    ///     Is a phone number when current is vendor contact
    /// </summary>
    [Required]
    [Parameter]
    public string UserName { get; set; }
    [Required]
    [Parameter]
    public DateTime? FromDate { get; set; }
    [Required]
    [Parameter]
    public DateTime? ToDate { get; set; }
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    private MudTable<POHeaderGetDto> Table { get; set; }
    [Inject]
    private IStringLocalizer<Global> L { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    private List<POHeaderGetDto> PurchaseHeaders { get; set; } = [];
    [Inject]
    private IApiClient ApiClient { get; set; }
    [Inject]
    private IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    private bool IsUpdating { get; set; }
    //pagination
    private int PageSize { get; set; } = 10;
    private int PageIndex { get; set; }
    private int TotalItemsCount { get; set; }
    private List<POHeaderGetDto> ListDueDateOfPurchaseOrdersB4Edit { get; set; } = [];
    private bool IsEdited { get; set; }
    [Inject]
    protected IDialogService DialogService { get; set; }
    private bool IsPurchaseUser { get; set; }
    private bool IsWarehouseUser { get; set; }
    private bool IsAdmin { get; set; }
    private bool IsVendor { get; set; }
    private bool IsVendorContact { get; set; }
    private bool IsMKT { get; set; }
    protected IDialogReference deleteConfirmDialogRef;
    //filtering
    private void OnPage(int index, int size)
    {
        PageSize = size;
        PageIndex = index;
    }

    private async Task DeleteMultiLineAsync()
    {
        if (Table.SelectedItems != null && Table.SelectedItems.Count != 0)
        {
            var approvedOrders = Table.SelectedItems
                .Where(item => item.Status >= (int)PurchaseOrderEnum.Approve)
                .Select(item => item.Number)
                .ToList();

            if (approvedOrders.Count != 0)
            {
                var message = string.Join(", ", approvedOrders);
                await DialogService.ShowMessageBox(
                L["Cannot delete approved purchase orders"],
                $"{L["The following purchase orders are approved and cannot be deleted"]}: \n{message}",
                L["OK"]);

                return;
            }

            var purchaseCodes = Table.SelectedItems.Select(item => item.Number).ToList();
            var response = await PurchaseOrderApiClient.DeleteMultipleAsync(purchaseCodes);
            if (response.IsSuccessStatusCode)
            {
                ViewNotifier.Show(L["Delete success"], ViewNotifierType.Success, L["Operation Successful"]);
                Table.SelectedItems.Clear();
                await OnCloseConfirmDeleteDialogAsync();
                await Reload();
            }
            else
            {
                ViewNotifier.Show(L[response.Message], ViewNotifierType.Error, L["Operation Failed"]);
            }
            // }
        }
    }

    private string NavigateToDetails(POHeaderGetDto detail)
    {
        if (IsWarehouseUser && !IsAdmin
                            && detail.Status is (int)PurchaseOrderEnum.Approve
                                or (int)PurchaseOrderEnum.PartiallyReceived
                                or (int)PurchaseOrderEnum.Completed)
        {
            return "/stock/" + detail.Number + "/" + detail.Status + "/detail";
        }
        //purchase order view
        return "/PO/" + detail.Number + "/Detail";
    }
    private static string NavigateToVendorDetails(string vendorNumber) => "/vendors/" + vendorNumber + "/detail";

    protected override async Task OnInitializedAsync()
    {
        await GetUserRoles();
        await base.OnInitializedAsync();
    }

    private async Task GetUserRoles()
    {
        var authState = await AuthenticationStateTask;
        var user = authState.User;

        IsAdmin = user.IsAdmin();
        IsVendor = user.IsVendor();
        IsPurchaseUser = user.IsPurchaseUser();
        IsWarehouseUser = user.IsWarehouseUser();
        IsVendorContact = user.IsVendorContact();
        IsMKT = user.IsMKT();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (Table is not null)
        {
            await Reload();
        }
        await base.OnParametersSetAsync();
    }
    private async Task LoadDataAsync()
    {
        try
        {
            var filters = new PurchaseOrderFilter
            {
                FromDate = FromDate,
                ToDate = ToDate,
                PageIndex = PageIndex,
                PageSize = PageSize,
                Number = PONumberFilter,
                Vendor = VendorNumber,
                ContactNumber = ContactNumber,
                Description = DescriptionFilter,
                ItemNumber = ItemNumberParam,
                IsMKT = false
            };

            ApiClient.ClearEntitiesCache();

            //ignore filter for vendor when vendor contact is logged in
            // if (IsVendorContact)
            // {
            //     filters.Vendor = null;
            // }
            if (IsMKT)
            {
                filters.isMKT = true;
            }
            if (IsAdmin)
            {
                filters.IsMKT = null;
            }
            if (IsPurchaseUser || IsAdmin || IsVendor)
            {
                filters.StatusFilter = StatusFilter;
            }
            else if (IsWarehouseUser && !IsAdmin)
            {
                filters.Status = (int)PurchaseOrderEnum.Approve;
                filters.StatusFilter = StatusFilter;
            }
            if (IsVendor && !IsVendorContact && string.IsNullOrEmpty(ContactNumber))
            {
                filters.ContactNumber = null;
            }

            var response = await PurchaseOrderApiClient.GetPoHeadersAsync(filters);

            if (!response.IsSuccessStatusCode)
            {
                ViewNotifier.Show(L[response.Message], ViewNotifierType.Error, L["Operation Failed"]);
            }
            else if (response.Result is not null)
            {
                TotalItemsCount = response.Result.RowCount;
                PurchaseHeaders = response.Result.Data.ToList();
            }
            else
            {
                PurchaseHeaders = [];
                ViewNotifier.Show(L["No item found"], ViewNotifierType.Warning, L["Operation Successful"]);
            }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
    }

    private async Task<TableData<POHeaderGetDto>> ServerReload(TableState state, CancellationToken token)
    {
        OnPage(state.Page, state.PageSize);
        await LoadDataAsync();
        return new TableData<POHeaderGetDto>
        {
            TotalItems = TotalItemsCount,
            Items = PurchaseHeaders
        };
    }
    public async Task Reload() => await Table.ReloadServerData();
    private void OnDueDateChange(DateTime? value, string number) => PurchaseHeaders.ForEach(x =>
    {
        if (x.Number != number)
        {
            return;
        }
        if (ListDueDateOfPurchaseOrdersB4Edit.All(y => y.Number != number))
        {
            ListDueDateOfPurchaseOrdersB4Edit.Add(x);// backup old  value
        }
        if (value != null)
        {
            x.DueDate = (DateTime)value;// update date in an original list
        }
        IsEdited = true;
    });
    private void OnDiscardChanges()
    {
        PurchaseHeaders.ForEach(x =>
        {
            if (ListDueDateOfPurchaseOrdersB4Edit.Any(y => y.Number == x.Number))
            {
            }
        });
        IsEdited = false;
    }
    private void OnSaveChanged()
    {
        IsEdited = false;
        var isAllUpdated = true;
        var listMsg = new List<string>();
        IsUpdating = true;
        ListDueDateOfPurchaseOrdersB4Edit.ForEach(async void (x) =>
        {
            try
            {
                var requestUpdateDto = new UpdatePOHeaderDto
                {
                    Number = x.Number,
                    VendorNo = x.BuyFromVendorNumber.Trim(),
                    OrderDate = x.OrderDate,
                    DueDate = x.DueDate,
                    PurchaseUser = UserName == null ? "" : x.PurchaserCode.Trim(),
                    PurchaserApprovalBy = x.PurchaserApprovalBy == null ? "" : x.PurchaserApprovalBy.Trim(),
                    VendorApprovalBy = x.VendorApprovalBy == null ? "" : x.VendorApprovalBy.Trim(),
                    PostingDescription = x.PostingDescription.Trim()
                };
                try
                {
                    var apiResponse = await PurchaseOrderApiClient.UpdateHeader(requestUpdateDto);
                    if (!apiResponse.IsSuccessStatusCode)
                    {
                        listMsg.Add(apiResponse.Message);
                        isAllUpdated = false;
                    }
                }
                catch (Exception ex)
                {
                    ViewNotifier.Show(ex.Message, ViewNotifierType.Error, L["Operation Failed"]);
                    IsUpdating = false;
                }
                if (listMsg.Count != 0)
                {
                    ViewNotifier.Show(string.Join("<br/>", listMsg), ViewNotifierType.Error, L["Operation Failed"]);
                }
                else if (listMsg.Count == 0 && isAllUpdated)
                {
                    ListDueDateOfPurchaseOrdersB4Edit.Clear();
                    ViewNotifier.Show(L["Update success"], ViewNotifierType.Success, L["Operation Successful"]);
                }
            }
            catch (Exception e)
            {
                ViewNotifier.Show(e.Message, ViewNotifierType.Error, L["Operation Failed"]);
            }
        });
        IsUpdating = false;
    }


    private void PageChanged(int i) => Table.NavigateTo(i - 1);



    protected async Task ShowConfirmDeleteDialogAsync()
    {
        var parameters = new DialogParameters<Dialog>
        {
            { x => x.ContentText, "Do you really want to delete these records? This process cannot be undone." },
            { x => x.OkText, "Delete" },
            { x => x.CancelText, "Cancel" },
            { x => x.OkColor, Color.Error },
            { x => x.OnOk, DeleteMultiLineAsync },
            { x => x.OnClose,  OnCloseConfirmDeleteDialogAsync },
        };

        var options = new DialogOptions() { CloseButton = true, MaxWidth = MaxWidth.ExtraSmall };
        deleteConfirmDialogRef = await DialogService.ShowAsync<Dialog>("Delete Confirmation", parameters, options);
    }

    protected async Task OnCloseConfirmDeleteDialogAsync()
    {
        DialogService.Close(deleteConfirmDialogRef);
        await Task.CompletedTask;
    }

}
