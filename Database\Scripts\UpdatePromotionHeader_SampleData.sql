-- =============================================
-- Script: Update existing PromotionHeader records (Simplified for FM PO Integration)
-- Description: Set Priority and AutoApply values for existing promotions
-- Date: 2025-01-15
-- =============================================

USE [PurchaseManager] -- Replace with your database name
GO

PRINT 'Updating existing PromotionHeader records (Simplified)...'

BEGIN TRANSACTION

BEGIN TRY
    -- Update Front Margin promotions (ProgramType = 1)
    UPDATE [dbo].[PromotionHeaders]
    SET
        [Priority] = CASE
            WHEN [Priority] IS NULL OR [Priority] = 0 THEN 1
            ELSE [Priority]
        END,
        [AutoApply] = CASE
            WHEN [AutoApply] IS NULL THEN 1
            ELSE [AutoApply]
        END
    WHERE [ProgramType] = 1 -- Front Margin

    PRINT '✓ Updated Front Margin promotions: Priority = 1, AutoApply = true'

    -- Update Back Margin promotions (ProgramType = 2)
    UPDATE [dbo].[PromotionHeaders]
    SET
        [Priority] = CASE
            WHEN [Priority] IS NULL OR [Priority] = 0 THEN 2
            ELSE [Priority]
        END,
        [AutoApply] = CASE
            WHEN [AutoApply] IS NULL THEN 0 -- Back Margin không auto-apply, cần tính toán phức tạp
            ELSE [AutoApply]
        END
    WHERE [ProgramType] = 2 -- Back Margin

    PRINT '✓ Updated Back Margin promotions: Priority = 2, AutoApply = false'

    COMMIT TRANSACTION
    PRINT ''
    PRINT '✅ Successfully updated existing PromotionHeader records!'

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT ''
    PRINT '❌ Error updating PromotionHeader records:'
    PRINT ERROR_MESSAGE()
    PRINT ''
END CATCH

-- Show summary of updates
PRINT ''
PRINT '📊 Summary of updated records:'

SELECT
    [ProgramType],
    CASE [ProgramType]
        WHEN 1 THEN 'Front Margin'
        WHEN 2 THEN 'Back Margin'
        ELSE 'Other'
    END as [Type],
    COUNT(*) as [Count],
    AVG([Priority]) as [Avg_Priority],
    SUM(CASE WHEN [AutoApply] = 1 THEN 1 ELSE 0 END) as [AutoApply_Count]
FROM [dbo].[PromotionHeaders]
WHERE [ModificationStatus] = 1 -- Active records only
GROUP BY [ProgramType]

PRINT ''
PRINT '📋 Business Rules Applied (Simplified):'
PRINT ''
PRINT 'Front Margin (ProgramType = 1):'
PRINT '- Priority: 1 (cao nhất cho immediate discount)'
PRINT '- AutoApply: true (tự động áp dụng khi tạo PO)'
PRINT '- Đơn giản với 3 cases: Percentage, Fixed Amount, Gift'
PRINT ''
PRINT 'Back Margin (ProgramType = 2):'
PRINT '- Priority: 2 (thấp hơn, tính toán định kỳ)'
PRINT '- AutoApply: false (cần tính toán phức tạp)'
PRINT '- Sẽ dùng PromotionCondition/PromotionReward cho 5 loại chiết khấu'
PRINT ''
PRINT '🎯 Next Steps:'
PRINT '1. Test Front Margin PO integration với Priority và AutoApply'
PRINT '2. Implement Back Margin với PromotionCondition/PromotionReward sau'
PRINT '3. Verify auto-apply logic hoạt động đúng'

PRINT ''
PRINT '✅ Database update completed successfully (Simplified)!'
