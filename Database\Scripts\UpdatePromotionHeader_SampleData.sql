-- =============================================
-- Script: Update existing PromotionHeader records with sample business rules
-- Description: Set appropriate values for new columns based on program type
-- Date: 2025-01-15
-- =============================================

USE [PurchaseManager] -- Replace with your database name
GO

PRINT 'Updating existing PromotionHeader records with business rules...'

BEGIN TRANSACTION

BEGIN TRY
    -- Update Front Margin promotions (ProgramType = 1)
    UPDATE [dbo].[PromotionHeaders]
    SET 
        [Priority] = CASE 
            WHEN [Priority] IS NULL OR [Priority] = 0 THEN 1 
            ELSE [Priority] 
        END,
        [AutoApply] = CASE 
            WHEN [AutoApply] IS NULL THEN 1 
            ELSE [AutoApply] 
        END,
        [UsedAmount] = CASE 
            WHEN [UsedAmount] IS NULL THEN 0 
            ELSE [UsedAmount] 
        END,
        [ApplyToAllVendorItems] = CASE 
            WHEN [ApplyToAllVendorItems] IS NULL THEN 1 
            ELSE [ApplyToAllVendorItems] 
        END,
        [RequireApprovalBeforeReward] = CASE 
            WHEN [RequireApprovalBeforeReward] IS NULL THEN 0 
            ELSE [RequireApprovalBeforeReward] 
        END,
        -- Set some reasonable defaults for Front Margin
        [MinOrderValue] = CASE 
            WHEN [MinOrderValue] IS NULL AND [ProgramType] = 1 THEN 1000000 -- 1M VND minimum
            ELSE [MinOrderValue] 
        END,
        [MaxDiscountAmount] = CASE 
            WHEN [MaxDiscountAmount] IS NULL AND [ProgramType] = 1 THEN 50000000 -- 50M VND max discount
            ELSE [MaxDiscountAmount] 
        END,
        [ApplicableDocTypes] = CASE 
            WHEN [ApplicableDocTypes] IS NULL AND [ProgramType] = 1 THEN '0,2' -- Purchase and Promotional items
            ELSE [ApplicableDocTypes] 
        END
    WHERE [ProgramType] = 1 -- Front Margin

    PRINT '✓ Updated Front Margin promotions'

    -- Update Back Margin promotions (ProgramType = 2) 
    UPDATE [dbo].[PromotionHeaders]
    SET 
        [Priority] = CASE 
            WHEN [Priority] IS NULL OR [Priority] = 0 THEN 2 
            ELSE [Priority] 
        END,
        [AutoApply] = CASE 
            WHEN [AutoApply] IS NULL THEN 0 -- Back Margin usually requires manual calculation
            ELSE [AutoApply] 
        END,
        [UsedAmount] = CASE 
            WHEN [UsedAmount] IS NULL THEN 0 
            ELSE [UsedAmount] 
        END,
        [ApplyToAllVendorItems] = CASE 
            WHEN [ApplyToAllVendorItems] IS NULL THEN 1 
            ELSE [ApplyToAllVendorItems] 
        END,
        [RequireApprovalBeforeReward] = CASE 
            WHEN [RequireApprovalBeforeReward] IS NULL THEN 1 -- Back Margin usually requires approval
            ELSE [RequireApprovalBeforeReward] 
        END,
        -- Back Margin specific defaults
        [DefaultPaymentMethod] = CASE 
            WHEN [DefaultPaymentMethod] IS NULL AND [ProgramType] = 2 THEN 1 -- Credit note
            ELSE [DefaultPaymentMethod] 
        END,
        [DefaultPaymentTiming] = CASE 
            WHEN [DefaultPaymentTiming] IS NULL AND [ProgramType] = 2 THEN 2 -- End of period
            ELSE [DefaultPaymentTiming] 
        END,
        [EvaluationPeriod] = CASE 
            WHEN [EvaluationPeriod] IS NULL AND [ProgramType] = 2 THEN 3 -- Quarterly
            ELSE [EvaluationPeriod] 
        END,
        [ApplicableDocTypes] = CASE 
            WHEN [ApplicableDocTypes] IS NULL AND [ProgramType] = 2 THEN '0,1,2' -- All document types
            ELSE [ApplicableDocTypes] 
        END
    WHERE [ProgramType] = 2 -- Back Margin

    PRINT '✓ Updated Back Margin promotions'

    -- Set budget amounts based on existing data patterns
    UPDATE [dbo].[PromotionHeaders]
    SET [MaxBudgetAmount] = 
        CASE 
            WHEN [MaxBudgetAmount] IS NULL THEN
                CASE [ProgramType]
                    WHEN 1 THEN 100000000 -- 100M VND for Front Margin
                    WHEN 2 THEN 500000000 -- 500M VND for Back Margin
                    ELSE 50000000
                END
            ELSE [MaxBudgetAmount]
        END
    WHERE [MaxBudgetAmount] IS NULL

    PRINT '✓ Set default budget amounts'

    COMMIT TRANSACTION
    PRINT ''
    PRINT '✅ Successfully updated existing PromotionHeader records!'

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT ''
    PRINT '❌ Error updating PromotionHeader records:'
    PRINT ERROR_MESSAGE()
    PRINT ''
END CATCH

-- Show summary of updates
PRINT ''
PRINT '📊 Summary of updated records:'

SELECT 
    [ProgramType],
    COUNT(*) as [Count],
    AVG([Priority]) as [Avg_Priority],
    SUM(CASE WHEN [AutoApply] = 1 THEN 1 ELSE 0 END) as [AutoApply_Count],
    AVG([MinOrderValue]) as [Avg_MinOrderValue],
    AVG([MaxOrderValue]) as [Avg_MaxOrderValue],
    AVG([MaxDiscountAmount]) as [Avg_MaxDiscountAmount],
    AVG([MaxBudgetAmount]) as [Avg_MaxBudgetAmount]
FROM [dbo].[PromotionHeaders]
WHERE [ModificationStatus] = 1 -- Active records only
GROUP BY [ProgramType]

PRINT ''
PRINT '📋 Business Rules Applied:'
PRINT ''
PRINT 'Front Margin (ProgramType = 1):'
PRINT '- Priority: 1 (high priority for immediate discount)'
PRINT '- AutoApply: true (automatically apply when creating PO)'
PRINT '- MinOrderValue: 1,000,000 VND'
PRINT '- MaxDiscountAmount: 50,000,000 VND'
PRINT '- ApplicableDocTypes: 0,2 (Purchase and Promotional items)'
PRINT '- RequireApprovalBeforeReward: false'
PRINT '- MaxBudgetAmount: 100,000,000 VND'
PRINT ''
PRINT 'Back Margin (ProgramType = 2):'
PRINT '- Priority: 2 (lower priority, calculated periodically)'
PRINT '- AutoApply: false (requires manual calculation)'
PRINT '- DefaultPaymentMethod: 1 (Credit note)'
PRINT '- DefaultPaymentTiming: 2 (End of period)'
PRINT '- EvaluationPeriod: 3 (Quarterly)'
PRINT '- ApplicableDocTypes: 0,1,2 (All document types)'
PRINT '- RequireApprovalBeforeReward: true'
PRINT '- MaxBudgetAmount: 500,000,000 VND'

PRINT ''
PRINT '🎯 Next Steps:'
PRINT '1. Review and adjust values based on your business requirements'
PRINT '2. Test Front Margin auto-apply functionality'
PRINT '3. Verify business rules work correctly in application'
PRINT '4. Consider creating indexes on Priority and AutoApply columns for performance'

-- Optional: Create indexes for better performance
PRINT ''
PRINT 'Creating performance indexes...'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionHeaders_Priority_AutoApply')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_PromotionHeaders_Priority_AutoApply]
    ON [dbo].[PromotionHeaders] ([Priority] ASC, [AutoApply] ASC)
    INCLUDE ([VendorCode], [ProgramType], [Status], [StartDate], [EndDate])
    PRINT '✓ Created index on Priority and AutoApply'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionHeaders_VendorCode_ProgramType_Status')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_PromotionHeaders_VendorCode_ProgramType_Status]
    ON [dbo].[PromotionHeaders] ([VendorCode] ASC, [ProgramType] ASC, [Status] ASC)
    INCLUDE ([StartDate], [EndDate], [Priority], [AutoApply])
    PRINT '✓ Created index on VendorCode, ProgramType, and Status'
END

PRINT ''
PRINT '✅ Database update completed successfully!'
