using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PurchaseManager.Infrastructure.Storage.DataModels;

namespace PurchaseManager.Storage.Configurations;

/// <summary>
/// Entity Framework configuration for PromotionCondition
/// </summary>
public class PromotionConditionConfiguration : IEntityTypeConfiguration<PromotionCondition>
{
    public void Configure(EntityTypeBuilder<PromotionCondition> builder)
    {
        builder.ToTable("PromotionConditions");

        // Primary Key
        builder.HasKey(e => e.Number);
        builder.HasAlternateKey(e => e.RowId);

        // Properties
        builder.Property(e => e.Number)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.PromotionNumber)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.ItemNumber)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.ItemName)
            .HasMaxLength(500)
            .HasDefaultValue(string.Empty);

        builder.Property(e => e.Quantity)
            .HasColumnType("decimal(18,4)")
            .HasDefaultValue(0);

        builder.Property(e => e.UnitOfMeasure)
            .HasMaxLength(20)
            .HasDefaultValue(string.Empty);

        builder.Property(e => e.DiscountPercent)
            .HasColumnType("decimal(18,4)")
            .HasDefaultValue(0);

        builder.Property(e => e.MinAmount)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.MaxAmount)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.Notes)
            .HasMaxLength(1000);

        // Indexes
        builder.HasIndex(e => e.PromotionNumber);
        builder.HasIndex(e => e.ItemNumber);

        // Foreign key
        builder.HasOne(e => e.PromotionHeader)
            .WithMany(e => e.PromotionConditions)
            .HasForeignKey(e => e.PromotionNumber)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
