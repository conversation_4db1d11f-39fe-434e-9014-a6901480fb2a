﻿using System.Net.Http.Json;
using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Shared.Services;

public class StockOrderApiClient : BaseApiClient, IStockOrderApiClient
{

    public StockOrderApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, string rootApiPath = "api/data/") : base(
    httpClient, logger, rootApiPath)
    {
    }
    public async Task<ApiResponseDto> CreateMultipleStockOrder(CreateStockOrderDto stockOrders)
        => await httpClient.PostJsonAsync<ApiResponseDto>("api/StockOrder/create", stockOrders);

    public async Task<ApiResponseDto<List<GetStockOrderDto>>> GetStockOrderByPoHeader(string poHeader)
        => await httpClient.GetJsonAsync<ApiResponseDto<List<GetStockOrderDto>>>($"api/StockOrder/by-po-header/{poHeader}");

    public async Task<ApiResponseDto<PagedResultDto<GetStockOrderDto>>> GetStockOrderByPoHeaderAsync(StockOrderFilter filter)
        => await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResultDto<GetStockOrderDto>>>("/api/StockOrder/Gets?" +
        filter.ToQuery());
}
