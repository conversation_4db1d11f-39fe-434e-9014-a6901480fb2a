﻿using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
namespace PurchaseManager.UI.Base.Shared.Components;

public abstract class BaseComponent : ComponentBase, IDisposable
{
    [CascadingParameter]
    protected Task<AuthenticationState> authenticationStateTask { get; set; }
    [Inject]
    protected IAuthorizationService authorizationService { get; set; }
    [Inject]
    protected NavigationManager navigationManager { get; set; }
    [Inject]
    protected IViewNotifier viewNotifier { get; set; }
    [Inject]
    protected IApiClient apiClient { get; set; }
    [Inject]
    protected IMasterDataApiClient masterDataApiClient { get; set; }
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    protected IMapper Mapper { get; set; }
    [Inject]
    protected IJSRuntime JsRuntime { get; set; }

    public virtual void Dispose()
    {
        apiClient.CancelChanges();
        apiClient.ClearEntitiesCache();

        masterDataApiClient.CancelChanges();
        masterDataApiClient.ClearEntitiesCache();

    }
}
