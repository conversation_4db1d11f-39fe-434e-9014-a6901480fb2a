using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Services.Promotions;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Models;
using PurchaseManager.Storage;

namespace PurchaseManager.Server.Controllers;

/// <summary>
/// Controller for applying Front Margin to Purchase Orders
/// </summary>
[ApiController]
[Route("api/po-front-margin")]
public class POFrontMarginController : ControllerBase
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private readonly IFrontMarginCalculationService _calculationService;
    private readonly IFrontMarginIntegrationService _integrationService;
    private readonly ILogger<POFrontMarginController> _logger;

    public POFrontMarginController(
        ApplicationDbContext context,
        IMapper mapper,
        IFrontMarginCalculationService calculationService,
        IFrontMarginIntegrationService integrationService,
        ILogger<POFrontMarginController> logger)
    {
        _context = context;
        _mapper = mapper;
        _calculationService = calculationService;
        _integrationService = integrationService;
        _logger = logger;
    }

    /// <summary>
    /// Get applicable Front Margin promotions for a PO
    /// </summary>
    [HttpPost("applicable-promotions")]
    public async Task<ApiResponse<List<GetPromotionFrontMarginDto>>> GetApplicablePromotionsAsync(
        [FromBody] POHeaderGetDto poHeader)
    {
        try
        {
            var currentDate = DateTime.Now;

            // Get active Front Margin promotions for the vendor
            var promotions = await _context.PromotionFrontMargins
                .Include(p => p.PromotionHeader)
                .Where(p => p.PromotionHeader.VendorCode == poHeader.BuyFromVendorNumber &&
                           p.PromotionHeader.ProgramType == 1 && // Front Margin
                           p.PromotionHeader.Status == 2 && // Active
                           p.PromotionHeader.StartDate <= currentDate &&
                           p.PromotionHeader.EndDate >= currentDate &&
                           p.Status == 1 && // Active
                           p.ModificationStatus == 1) // Not deleted
                .ToListAsync();

            var mappedPromotions = _mapper.Map<List<GetPromotionFrontMarginDto>>(promotions);

            return new ApiResponse<List<GetPromotionFrontMarginDto>>(200, "Promotions retrieved successfully", mappedPromotions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting applicable promotions for vendor {VendorCode}", poHeader.BuyFromVendorNumber);
            return new ApiResponse<List<GetPromotionFrontMarginDto>>(500, "Promotions retrieved successfully");
        }
    }

    // /// <summary>
    // /// Calculate Front Margin discount for a PO line
    // /// </summary>
    // [HttpPost("calculate-line-discount")]
    // public async Task<ActionResult<ApiResponse<FrontMarginCalculationResult>>> CalculateLineDiscountAsync(
    //     [FromBody] CalculateLineDiscountRequest request)
    // {
    //     try
    //     {
    //         // Get applicable promotions
    //         var promotions = await _context.PromotionFrontMargins
    //             .Include(p => p.PromotionHeader)
    //             .Where(p => request.PromotionNumbers.Contains(p.ProgramNumber) &&
    //                        p.ItemNumber == request.POLine.ItemNumber &&
    //                        p.Status == 1 &&
    //                        p.ModificationStatus == 1)
    //             .ToListAsync();

    //         if (!promotions.Any())
    //         {
    //             return Ok(ApiResponse<FrontMarginCalculationResult>.Success(
    //                 "No applicable promotions found",
    //                 new FrontMarginCalculationResult
    //                 {
    //                     OriginalUnitCost = request.POLine.UnitCost,
    //                     OriginalAmount = request.POLine.UnitCost * request.POLine.Quantity,
    //                     FinalUnitCost = request.POLine.UnitCost,
    //                     FinalAmount = request.POLine.UnitCost * request.POLine.Quantity
    //                 }));
    //         }

    //         // Calculate discount
    //         var result = await _calculationService.CalculateLineDiscountAsync(request.POLine, promotions);

    //         return Ok(ApiResponse<FrontMarginCalculationResult>.Success("Discount calculated successfully", result));
    //     }
    //     catch (Exception ex)
    //     {
    //         _logger.LogError(ex, "Error calculating line discount for item {ItemNumber}", request.POLine.ItemNumber);
    //         return StatusCode(500, ApiResponse<FrontMarginCalculationResult>.Error("Internal server error"));
    //     }
    // }

    // /// <summary>
    // /// Apply Front Margin to entire PO
    // /// </summary>
    // [HttpPost("apply-to-po")]
    // public async Task<ActionResult<ApiResponse<POFrontMarginResult>>> ApplyFrontMarginToPOAsync(
    //     [FromBody] ApplyFrontMarginToPORequest request)
    // {
    //     try
    //     {
    //         // Apply Front Margin calculations
    //         var result = await _calculationService.ApplyFrontMarginToPOAsync(request.POHeader, request.POLines);

    //         _logger.LogInformation("Applied Front Margin to PO {PONumber}. Original: {Original:C}, Final: {Final:C}, Savings: {Savings:C}",
    //             request.POHeader.PONumber, result.OriginalPOAmount, result.FinalPOAmount, result.TotalSavings);

    //         return Ok(ApiResponse<POFrontMarginResult>.Success("Front Margin applied successfully", result));
    //     }
    //     catch (Exception ex)
    //     {
    //         _logger.LogError(ex, "Error applying Front Margin to PO {PONumber}", request.POHeader.PONumber);
    //         return StatusCode(500, ApiResponse<POFrontMarginResult>.Error("Internal server error"));
    //     }
    // }

    // /// <summary>
    // /// Preview Front Margin calculation without applying
    // /// </summary>
    // [HttpPost("preview")]
    // public async Task<ActionResult<ApiResponse<POFrontMarginResult>>> PreviewFrontMarginAsync(
    //     [FromBody] ApplyFrontMarginToPORequest request)
    // {
    //     try
    //     {
    //         // Create copies of PO lines to avoid modifying original data
    //         var poLinesCopy = request.POLines.Select(line => new POLineGetDto
    //         {
    //             ItemNumber = line.ItemNumber,
    //             Description = line.Description,
    //             Quantity = line.Quantity,
    //             UnitOfMeasure = line.UnitOfMeasure,
    //             UnitCost = line.UnitCost,
    //             UnitPrice = line.UnitPrice,
    //             Amount = line.Amount,
    //             DocumentType = line.DocumentType,
    //             LotNo = line.LotNo
    //         }).ToList();

    //         // Apply Front Margin calculations to copies
    //         var result = await _calculationService.ApplyFrontMarginToPOAsync(request.POHeader, poLinesCopy);

    //         return Ok(ApiResponse<POFrontMarginResult>.Success("Front Margin preview calculated successfully", result));
    //     }
    //     catch (Exception ex)
    //     {
    //         _logger.LogError(ex, "Error previewing Front Margin for PO {PONumber}", request.POHeader.PONumber);
    //         return StatusCode(500, ApiResponse<POFrontMarginResult>.Error("Internal server error"));
    //     }
    // }

    // /// <summary>
    // /// Get Front Margin summary for a vendor
    // /// </summary>
    // [HttpGet("vendor-summary/{vendorCode}")]
    // public async Task<ActionResult<ApiResponse<VendorFrontMarginSummary>>> GetVendorSummaryAsync(
    //     string vendorCode,
    //     [FromQuery] DateTime? fromDate = null,
    //     [FromQuery] DateTime? toDate = null)
    // {
    //     try
    //     {
    //         var startDate = fromDate ?? DateTime.Now.AddMonths(-3);
    //         var endDate = toDate ?? DateTime.Now;

    //         var activePromotions = await _context.PromotionFrontMargins
    //             .Include(p => p.PromotionHeader)
    //             .Where(p => p.PromotionHeader.VendorCode == vendorCode &&
    //                        p.PromotionHeader.ProgramType == 1 &&
    //                        p.PromotionHeader.StartDate <= endDate &&
    //                        p.PromotionHeader.EndDate >= startDate &&
    //                        p.Status == 1 &&
    //                        p.ModificationStatus == 1)
    //             .GroupBy(p => p.DiscountType)
    //             .Select(g => new
    //             {
    //                 DiscountType = g.Key,
    //                 Count = g.Count(),
    //                 Programs = g.Select(p => p.ProgramNumber).Distinct().Count()
    //             })
    //             .ToListAsync();

    //         var summary = new VendorFrontMarginSummary
    //         {
    //             VendorCode = vendorCode,
    //             FromDate = startDate,
    //             ToDate = endDate,
    //             TotalActivePromotions = activePromotions.Sum(p => p.Count),
    //             TotalActivePrograms = activePromotions.Sum(p => p.Programs),
    //             DiscountTypeSummary = activePromotions.ToDictionary(
    //                 p => GetDiscountTypeName(p.DiscountType),
    //                 p => p.Count
    //             )
    //         };

    //         return Ok(ApiResponse<VendorFrontMarginSummary>.Success("Vendor summary retrieved successfully", summary));
    //     }
    //     catch (Exception ex)
    //     {
    //         _logger.LogError(ex, "Error getting vendor summary for {VendorCode}", vendorCode);
    //         return StatusCode(500, ApiResponse<VendorFrontMarginSummary>.Error("Internal server error"));
    //     }
    // }

    private static string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Chiết khấu theo phần trăm",
            2 => "Chiết khấu số tiền cố định",
            3 => "Mua hàng tặng hàng cùng loại",
            4 => "Tặng hàng khác loại",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Apply Front Margin to entire PO using integration service
    /// </summary>
    [HttpPost("apply-to-po-integrated")]
    public async Task<ApiResponse> ApplyFrontMarginToPOIntegrated([FromBody] POIntegrationRequest request)
    {
        if (!ModelState.IsValid)
            return ApiResponse.S400("Invalid request data");

        try
        {
            var result = await _integrationService.ApplyFrontMarginToPOAsync(request.POHeader, request.POLines);

            if (!result.Success)
            {
                return ApiResponse.S400(result.Message);
            }

            return ApiResponse.S200(result.Message, new
            {
                OriginalAmount = result.TotalOriginalAmount,
                FinalAmount = result.TotalFinalAmount,
                TotalSavings = result.TotalSavings,
                DiscountPercentage = result.TotalDiscountPercentage,
                UpdatedLines = result.UpdatedPOLines,
                GiftLines = result.GiftLines,
                AppliedPromotions = result.AppliedPromotions
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying Front Margin to PO using integration service");
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    /// <summary>
    /// Preview Front Margin impact using integration service
    /// </summary>
    [HttpPost("preview-impact-integrated")]
    public async Task<ApiResponse> PreviewFrontMarginImpactIntegrated([FromBody] POIntegrationRequest request)
    {
        if (!ModelState.IsValid)
            return ApiResponse.S400("Invalid request data");

        try
        {
            var preview = await _integrationService.PreviewFrontMarginImpactAsync(request.POHeader, request.POLines);

            if (!string.IsNullOrEmpty(preview.ErrorMessage))
            {
                return ApiResponse.S400(preview.ErrorMessage);
            }

            return ApiResponse.S200("Front Margin impact preview generated", preview);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing Front Margin impact using integration service");
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    /// <summary>
    /// Validate PO for Front Margin eligibility
    /// </summary>
    [HttpPost("validate-po")]
    public async Task<ApiResponse> ValidatePOForFrontMargin([FromBody] POIntegrationRequest request)
    {
        if (!ModelState.IsValid)
            return ApiResponse.S400("Invalid request data");

        try
        {
            var validation = await _integrationService.ValidatePOForFrontMarginAsync(request.POHeader, request.POLines);

            return ApiResponse.S200("PO validation completed", new
            {
                IsValid = validation.IsValid,
                ValidationMessages = validation.ValidationMessages,
                Warnings = validation.Warnings
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating PO for Front Margin");
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }

    /// <summary>
    /// Get Front Margin usage history for vendor
    /// </summary>
    [HttpGet("usage-history/{vendorCode}")]
    public async Task<ApiResponse> GetUsageHistory(string vendorCode, [FromQuery] DateTime? fromDate = null, [FromQuery] DateTime? toDate = null)
    {
        try
        {
            var history = await _integrationService.GetUsageHistoryAsync(vendorCode, fromDate, toDate);

            return ApiResponse.S200($"Usage history retrieved for vendor {vendorCode}", new
            {
                VendorCode = vendorCode,
                FromDate = fromDate,
                ToDate = toDate,
                TotalRecords = history.Count,
                History = history
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage history for vendor {VendorCode}", vendorCode);
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
}

// Request/Response models
public class POIntegrationRequest
{
    public POHeaderGetDto POHeader { get; set; } = null!;
    public List<POLineGetDto> POLines { get; set; } = new();
}
public class CalculateLineDiscountRequest
{
    public POLineGetDto POLine { get; set; } = new();
    public List<string> PromotionNumbers { get; set; } = new();
}

public class ApplyFrontMarginToPORequest
{
    public POHeaderGetDto POHeader { get; set; } = new();
    public List<POLineGetDto> POLines { get; set; } = new();
}

public class VendorFrontMarginSummary
{
    public string VendorCode { get; set; } = string.Empty;
    public DateTime FromDate { get; set; }
    public DateTime ToDate { get; set; }
    public int TotalActivePromotions { get; set; }
    public int TotalActivePrograms { get; set; }
    public Dictionary<string, int> DiscountTypeSummary { get; set; } = new();
}
