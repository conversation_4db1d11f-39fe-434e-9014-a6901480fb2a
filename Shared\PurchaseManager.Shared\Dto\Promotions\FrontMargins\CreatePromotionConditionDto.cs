﻿namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
///     DTO for promotion condition
/// </summary>
public class CreatePromotionConditionDto
{
    public string ItemNumber { get; set; } = string.Empty;
    public decimal Quantity { get; set; }
    public string UnitOfMeasure { get; set; } = string.Empty;
    public decimal? MinAmount { get; set; }
    public decimal? MaxAmount { get; set; }
}
