using MudBlazor;
using PurchaseManager.Shared.Dto.PurchaseSuggestedPayment;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.PurchaseSuggestedPayment;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseSuggestedPayment;

public partial class PurchaseSuggestPaymentReportPage : ItemsTableBase<PurchaseSuggestedReport>
{
    protected bool isLoading { get; set; }
    protected string selectedView { get; set; } = "All";
    protected string selectedTags { get; set; } = "";
    protected PurchaseSuggestedPaymentReportFilter filter { get; set; } = new PurchaseSuggestedPaymentReportFilter();

    protected List<string> listView { get; set; } = new List<string>();
    protected List<string> listTagToSelect { get; set; } = new List<string>();
    protected TableGroupDefinition<PurchaseSuggestedReport> _groupDefinition = new()
    {
        GroupName = "Department",
        Indentation = false,
        Expandable = false,
        Selector = (e) => e.LocationCode
    };
    protected override void OnInitialized()
    {
        listView.AddRange(["Item", "Department"]);
        queryParameters = filter;
        from = "demand-v2-report";
        base.OnInitialized();
    }
    protected override async Task OnInitializedAsync()
    {
        await GetPurchaseSuggestPaymentTags();
        await base.OnInitializedAsync();
    }
    protected async Task GetPurchaseSuggestPaymentTags()
    {
        try
        {
            var resp = await apiClient.GetPurchaseSuggestPaymentTags();
            listTagToSelect = resp.Result;
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }
    protected async Task OnViewChanged(string value)
    {
        try
        {
            selectedView = value;
            if (value == "" || value is null) selectedView = "All";
            if (value is not null)
            {
                var isViewByItem = value.Contains("Item");
                var isViewByDept = value.Contains("Department");
                filter.ByItemNumber = isViewByItem;
                filter.ByLocationCode = isViewByDept;
            }
            else
            {
                filter.ByItemNumber = false;
                filter.ByLocationCode = false;
            }
            apiClient.ClearEntitiesCache();
            await Reload();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }
    protected async Task OnFilterByTagsChanged(string value)
    {
        try
        {
            selectedTags = value;
            filter.Tag = value;
            await Reload();
        }
        catch (Exception ex)
        {
            viewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }

}
