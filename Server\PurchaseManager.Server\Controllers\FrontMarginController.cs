using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.Promotions;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Controllers;

/// <summary>
/// Controller for Front Margin operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class FrontMarginController : ControllerBase
{
    private readonly IFrontMarginManager _frontMarginManager;
    private readonly ApiResponse _invalidData;

    public FrontMarginController(IFrontMarginManager frontMarginManager, IStringLocalizer<Global> i18N)
    {
        _frontMarginManager = frontMarginManager;
        _invalidData = new ApiResponse(Status400BadRequest, i18N["InvalidData"]);
    }

    [HttpGet("filtered")]
    public async Task<ApiResponse> GetFrontMarginsFiltered([FromQuery] FrontMarginFilter filter)
    {
        return await _frontMarginManager.GetAllFrontMarginsAsync(filter);
    }

    [HttpGet("{number}")]
    public async Task<ApiResponse> GetFrontMarginByNumber(string number)
    {
        return await _frontMarginManager.GetFrontMarginByNumberAsync(number);
    }

    [HttpPost]
    public async Task<ApiResponse> CreateFrontMargin([FromBody] CreatePromotionFrontMarginDto createDto)
    {
        return ModelState.IsValid ? await _frontMarginManager.CreateFrontMarginAsync(createDto) : _invalidData;
    }

    [HttpPut("{number}")]
    public async Task<ApiResponse> UpdateFrontMargin(string number, [FromBody] UpdatePromotionFrontMarginDto updateDto)
    {
        return ModelState.IsValid ? await _frontMarginManager.UpdateFrontMarginAsync(number, updateDto) : _invalidData;
    }

    [HttpDelete("bulk")]
    public async Task<ApiResponse> DeleteFrontMargins([FromBody] List<string> numbers)
    {
        return await _frontMarginManager.DeleteFrontMarginAsync(numbers);
    }

    [HttpGet("discount-types")]
    public async Task<ApiResponse> GetDiscountTypes()
    {
        return await _frontMarginManager.GetDiscountTypesAsync();
    }

    [HttpPost("validate")]
    public async Task<ApiResponse> ValidateFrontMargin([FromBody] CreatePromotionFrontMarginDto createDto)
    {
        return ModelState.IsValid ? await _frontMarginManager.ValidateFrontMarginAsync(createDto) : _invalidData;
    }

    [HttpGet("by-program/{programNumber}")]
    public async Task<ApiResponse> GetFrontMarginsByProgram(string programNumber)
    {
        return await _frontMarginManager.GetFrontMarginsByProgramAsync(programNumber);
    }

    [HttpPost("duplicate/{number}")]
    public async Task<ApiResponse> DuplicateFrontMargin(string number)
    {
        return await _frontMarginManager.DuplicateFrontMarginAsync(number);
    }

    [HttpGet("summary")]
    public async Task<ApiResponse> GetFrontMarginSummary([FromQuery] string? vendorCode = null)
    {
        return await _frontMarginManager.GetFrontMarginSummaryAsync(vendorCode);
    }

    [HttpPost("export")]
    public async Task<ApiResponse> ExportFrontMargins([FromBody] FrontMarginFilter filter)
    {
        return await _frontMarginManager.ExportFrontMarginsAsync(filter);
    }

    [HttpPost("import")]
    public async Task<ApiResponse> ImportFrontMargins([FromBody] List<CreatePromotionFrontMarginDto> dtos)
    {
        return await _frontMarginManager.ImportFrontMarginsAsync(dtos);
    }

    [HttpPost("activate/{number}")]
    public async Task<ApiResponse> ActivateFrontMargin(string number)
    {
        return await _frontMarginManager.ActivateFrontMarginAsync(number);
    }

    [HttpPost("deactivate/{number}")]
    public async Task<ApiResponse> DeactivateFrontMargin(string number)
    {
        return await _frontMarginManager.DeactivateFrontMarginAsync(number);
    }

    [HttpGet("conflicts")]
    public async Task<ApiResponse> GetConflictingPromotions([FromQuery] string itemNumber, [FromQuery] string vendorCode)
    {
        return await _frontMarginManager.GetConflictingPromotionsAsync(itemNumber, vendorCode);
    }
}
