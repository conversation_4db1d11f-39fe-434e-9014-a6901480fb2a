using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("PurchaseOrderHeader")]
public class PurchaseOrderHeader
{
    [Key]
    [Required]
    [MaxLength(100)]
    public string Number { get; set; }

    [Required]
    public int DocumentType { get; set; }

    [Required]
    [MaxLength(100)]
    public string BuyFromVendorNumber { get; set; }

    [Required]
    [MaxLength(100)]
    public string PayToCode { get; set; }

    [Required]
    [MaxLength(160)]
    public string PayToName { get; set; }

    [Required]
    [MaxLength(160)]
    public string PayToAddress { get; set; }

    [Required]
    [MaxLength(160)]
    public string PayToCity { get; set; }

    [Required]
    [MaxLength(160)]
    public string YourReference { get; set; }

    [Required]
    [MaxLength(100)]
    public string ShipToCode { get; set; }

    [Required]
    [MaxLength(160)]
    public string ShipToName { get; set; }

    [Required]
    [MaxLength(160)]
    public string ShipToAddress { get; set; }

    [Required]
    [MaxLength(160)]
    public string ShipToCity { get; set; }

    [Required]
    public DateTime OrderDate { get; set; }

    [Required]
    public DateTime PostingDate { get; set; }

    [Required]
    [MaxLength(100)]
    public string PostingDescription { get; set; }

    [Required]
    public DateTime DueDate { get; set; }

    [Required]
    public decimal PaymentDiscount { get; set; }

    [Required]
    public DateTime PaymentDiscountDate { get; set; }

    [Required]
    [MaxLength(100)]
    public string ShipmentMethodCode { get; set; }

    [Required]
    [MaxLength(100)]
    public string LocationCode { get; set; }

    [Required]
    [MaxLength(100)]
    public string VendorPostingGroup { get; set; }

    [Required]
    [MaxLength(100)]
    public string CurrencyCode { get; set; }

    [Required]
    [MaxLength(100)]
    public string PurchaserCode { get; set; }

    [MaxLength(30)]
    public string OnHold { get; set; }

    [Required]
    public int AppliesToDocumentType { get; set; }

    [Required]
    [MaxLength(100)]
    public string AppliesToDocumentNumber { get; set; }

    [Required]
    public int Receive { get; set; }

    [Required]
    public int Invoice { get; set; }

    [Required]
    [MaxLength(100)]
    public string ReceivingNumber { get; set; }

    [Required]
    [MaxLength(100)]
    public string ExternalDocumentNumber { get; set; }

    [Required]
    [MaxLength(100)]
    public string VATRegistrationNumber { get; set; }

    [Required]
    [MaxLength(160)]
    public string BuyFromVendorName { get; set; }

    [Required]
    [MaxLength(160)]
    public string BuyFromAddress { get; set; }

    [Required]
    [MaxLength(160)]
    public string BuyFromCity { get; set; }

    [Required]
    [MaxLength(160)]
    public string BuyFromContact { get; set; }

    [Required]
    public DateTime DocumentDate { get; set; }

    [Required]
    [MaxLength(100)]
    public string PaymentMethodCode { get; set; }

    [Required]
    [MaxLength(100)]
    public string SourceCode { get; set; }

    [Required]
    [MaxLength(100)]
    public string VATBusinessPostingGroup { get; set; }

    [Required]
    public int Status { get; set; }

    [Required]
    public DateTime RequestedReceiptDate { get; set; }

    [Required]
    public DateTime PromisedReceiptDate { get; set; }

    [Required]
    [MaxLength(64)]
    public string LeadTimeCalculation { get; set; }

    [Required]
    public int Ship { get; set; }

    [Required]
    public DateTime DateReceived { get; set; }

    [Required]
    public DateTime TimeReceived { get; set; }

    [Required]
    public DateTime DateSent { get; set; }

    [Required]
    public DateTime TimeSent { get; set; }

    [MaxLength(100)]
    public string LoginId { get; set; }

    [MaxLength(100)]
    public string OrderingUser { get; set; }

    [MaxLength(100)]
    public string DeliveryUser { get; set; }

    [MaxLength(100)]
    public string InvoicingUser { get; set; }

    [MaxLength(100)]
    public string WarehouseUser { get; set; }

    [MaxLength(100)]
    public string PickingUser { get; set; }

    [MaxLength(100)]
    public string PackingUser { get; set; }

    public decimal? OverheadRate { get; set; }

    public int? Checked { get; set; }

    [MaxLength(160)]
    public string Description { get; set; }

    [MaxLength(100)]
    public string ReceivedNumber { get; set; }

    public int? OrderType { get; set; }

    [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
    public int RowId { get; set; }

    public DateTime? DocumentTime { get; set; }

    public DateTime? PrintingTime { get; set; }

    [MaxLength(20)]
    public string ModifiedId { get; set; }

    public DateTime? LastModifiedTime { get; set; }

    [MaxLength(20)]
    public string UsingID { get; set; }

    public DateTime? BeginUsingTime { get; set; }

    public DateTime? CreatedAtTime { get; set; }

    [MaxLength(20)]
    public string VendorApprovalBy { get; set; }

    [MaxLength(20)]
    public string PurchaserApprovalBy { get; set; }

    [Required]
    public int DocNoOccurrence { get; set; }

    public DateOnly? ApprovalDate { get; set; }

    [Required]
    public bool Blocked { get; set; }
    public bool? IsMKT { get; set; }

    /// <summary>
    /// Số lượng CTKM đã áp dụng cho PO này
    /// 0 = chưa áp dụng CTKM nào, >0 = số lượng CTKM đã áp dụng
    /// </summary>
    public int PromotionCalculated { get; set; } = 0;

    [InverseProperty("DocumentNumberNavigation")]
    public ICollection<PurchaseOrderLine> PurchaseOrderLines { get; set; } = new List<PurchaseOrderLine>();
    public ICollection<StockOrder> StockOrders { get; set; } = new List<StockOrder>();
    public ICollection<CJLGrHeader> CjlGrHeaders { get; set; } = new List<CJLGrHeader>();

}
