-- =============================================
-- Run Front Margin Migration Script
-- Execute this to update database for Front Margin
-- Date: 2025-01-11
-- =============================================

USE [PurchaseManager] -- Change to your database name
GO

PRINT 'Starting Front Margin Migration...'
PRINT 'Database: ' + DB_NAME()
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
PRINT '============================================='

-- Check if table exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionFrontMargins')
BEGIN
    PRINT 'ERROR: PromotionFrontMargins table does not exist!'
    PRINT 'Please ensure the table is created first.'
    RETURN
END

-- Check if migration is needed
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'DiscountType')
BEGIN
    PRINT 'Migration already applied.'
    PRINT 'Current Front Margin records:'
    SELECT
        DiscountType,
        COUNT(*) as RecordCount
    FROM PromotionFrontMargins
    GROUP BY DiscountType
    ORDER BY DiscountType
    RETURN
END

PRINT 'Migration needed. Starting update...'

BEGIN TRY
    BEGIN TRANSACTION

    -- Add new columns to PromotionFrontMargins table
    ALTER TABLE PromotionFrontMargins ADD
        DiscountType INT NOT NULL DEFAULT 1,
        FixedDiscountAmount DECIMAL(18,2) NULL,
        BuyQuantity DECIMAL(18,4) NULL,
        GiftQuantity DECIMAL(18,4) NULL,
        GiftItemNumber NVARCHAR(50) NULL,
        GiftItemName NVARCHAR(250) NULL,
        GiftItemUOM NVARCHAR(50) NULL,
        GiftItemQuantity DECIMAL(18,4) NULL,
        MinimumQuantity DECIMAL(18,4) NULL,
        MinimumAmount DECIMAL(18,2) NULL,
        MaximumDiscountAmount DECIMAL(18,2) NULL

    PRINT '✓ Added new columns'

    -- Update DiscountPercentage precision
    ALTER TABLE PromotionFrontMargins ALTER COLUMN DiscountPercentage DECIMAL(5,2)
    PRINT '✓ Updated DiscountPercentage precision'

    -- Add indexes
    CREATE INDEX IX_PromotionFrontMargins_DiscountType ON PromotionFrontMargins(DiscountType)
    PRINT '✓ Added DiscountType index'

    CREATE INDEX IX_PromotionFrontMargins_GiftItemNumber ON PromotionFrontMargins(GiftItemNumber) WHERE GiftItemNumber IS NOT NULL
    PRINT '✓ Added GiftItemNumber index'

    -- Add constraints
    ALTER TABLE PromotionFrontMargins ADD CONSTRAINT CK_PromotionFrontMargins_DiscountType
    CHECK (DiscountType IN (1, 2, 3, 4))
    PRINT '✓ Added DiscountType constraint'

    -- Update existing records
    UPDATE PromotionFrontMargins
    SET DiscountType = 1
    WHERE DiscountPercentage > 0

    DECLARE @UpdatedRecords INT = @@ROWCOUNT
    PRINT '✓ Updated ' + CAST(@UpdatedRecords AS VARCHAR(10)) + ' existing records'

    COMMIT TRANSACTION
    PRINT '✓ Migration completed successfully!'

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT 'ERROR: Migration failed!'
    PRINT 'Error Message: ' + ERROR_MESSAGE()
    PRINT 'Error Line: ' + CAST(ERROR_LINE() AS VARCHAR(10))
    THROW;
END CATCH

-- Final verification
PRINT '============================================='
PRINT 'Final verification:'

SELECT
    'Column Count' as CheckType,
    COUNT(*) as Value
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionFrontMargins'

UNION ALL

SELECT
    'Constraint Count' as CheckType,
    COUNT(*) as Value
FROM sys.check_constraints
WHERE parent_object_id = OBJECT_ID('PromotionFrontMargins')

UNION ALL

SELECT
    'Index Count' as CheckType,
    COUNT(*) as Value
FROM sys.indexes
WHERE object_id = OBJECT_ID('PromotionFrontMargins')
AND name LIKE 'IX_PromotionFrontMargins_%'

-- Show sample data structure
PRINT 'Sample data structure:'
SELECT TOP 1 * FROM PromotionFrontMargins

PRINT 'Front Margin Migration completed successfully!'
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
