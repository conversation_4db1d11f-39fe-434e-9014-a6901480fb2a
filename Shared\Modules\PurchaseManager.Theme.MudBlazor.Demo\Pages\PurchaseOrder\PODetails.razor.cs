using System.Globalization;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using AutoMapper;
using ExcelDataReader;
using Karambolo.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using MudBlazor;
using ObjectCloner.Extensions;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.PurchasePrices;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Models.Item;
using PurchaseManager.Shared.Providers;
using PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services;
using PurchaseManager.Theme.Material.Demo.Shared.Components;
using static PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder.Services.POExtensions;
namespace PurchaseManager.Theme.Material.Demo.Pages.PurchaseOrder;

public partial class PODetails : ComponentBase, IAsyncDisposable
{
    [Parameter]
    public string PONumber { get; set; }
    [CascadingParameter]
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    [Inject]
    private HttpClient HttpClient { get; set; }
    [Inject]
    private IStringLocalizer<Global> L { get; set; }
    [Inject]
    private IApiClient ApiClient { get; set; }
    [Inject]
    private IMapper Mapper { get; set; }
    [Inject]
    private NavigationManager NavigationManager { get; set; }
    private ListFileBasePage ListFilePageRef { get; set; }
    private MudForm EditPOLineFormRef { get; set; }
    [Inject]
    private IDialogService DialogService { get; set; }
    [Inject]
    private IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; }
    [Inject]
    private IPurchasePriceApiClient PurchasePriceApiClient { get; set; }
    [Inject]
    private IVendorApiClient VendorApiClient { get; set; }
    [Inject]
    private IItemApiClient ItemApiClient { get; set; }
    [Inject]
    public IJSRuntime JsRuntime { get; set; }
    [Inject]
    private POServicesHandler POServicesHandler { get; set; }
    private POHeaderGetDto POHeader { get; set; } = new POHeaderGetDto();
    // private DateTime? OrderDate { get; set; }
    private GetVendorDto VendorInfo { get; set; } = new GetVendorDto();
    private MudTable<POLineGetDto> Table { get; set; }
    // public POLineGetDto LineEditing { get; set; } = new POLineGetDto();
    private POLineGetDto CurrentLine { get; set; } = new POLineGetDto();
    private List<DetailItemUnitOfMeasureDto> LsDetailItemUnitOfMeasureDtoEditing { get; set; } = [];
    private List<POLineGetDto> POLines { get; set; } = [];
    private HashSet<POLineGetDto> SelectedPOLine { get; set; } = [];
    private UserViewModel UserViewModel { get; set; } = new UserViewModel();
    private string ErrorMessage { get; set; }
    private List<GetPurchasePriceDto> ListPurchasePriceDto { get; set; } = [];
    private bool IsPurchaseManager { get; set; }
    private bool IsMKT { get; set; }
    private bool IsWarehouse { get; set; }
    private bool IsAdmin { get; set; }
    private bool IsShowAddFormLine { get; set; }
    private bool IsShowReport { get; set; }
    private bool IsShowAddNewVendorItem { get; set; }
    private bool IsLoad { get; set; }
    private bool IsEdit { get; set; }
    private bool IsShowEditPOLineDialog { get; set; }
    private string UrlFileReport { get; set; }
    private readonly CultureInfo _cultureInfo = CultureInfo.CurrentCulture;
    /// <summary>
    ///     30 MB
    /// </summary>
    private const int MaxAllowedSize = 1024 * 1024 * 30;
    private DateTime? ExpirationDateForPicker
    {
        get => CurrentLine.ExpirationDate?.ToDateTime(TimeOnly.MinValue);
        set => CurrentLine.ExpirationDate = value.HasValue
            ? DateOnly.FromDateTime(value.Value)
            : null;
    }
    protected override async Task OnInitializedAsync()
    {
        IsLoad = true;
        await GetHeaderPO(PONumber);
        await GetDetailPOAsync(PONumber);
        await GetUserRolesAsync();
        UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        IsLoad = false;
        await base.OnInitializedAsync();
    }

    private async Task GetUserRolesAsync()
    {
        var authState = await AuthenticationStateTask;
        var user = authState.User;

        user.IsPurchaseUser();
        IsPurchaseManager = user.IsPurchaseManager();
        IsMKT = user.IsMKT();
        user.IsVendor();
        user.IsVendorContact();
        IsWarehouse = user.IsWarehouseUser();
        IsAdmin = user.IsAdmin();
    }

    #region Upload file PoLine
    // Grouped error properties for UI
    private Dictionary<string, List<string>> GroupedDuplicateErrors { get; set; } = new Dictionary<string, List<string>>();
    private Dictionary<string, List<string>> GroupedVatErrors { get; set; } = new Dictionary<string, List<string>>();

    private void SetGroupedErrors(List<POLinesFromFileDto> duplicateLines, List<POLinesFromFileDto> invalidVatLines)
    {
        GroupedDuplicateErrors = new Dictionary<string, List<string>>
        {
            {
                "Duplicate Item", duplicateLines.Select(x => $"SKU: {x.ItemNumber} - Unit: {x.PurchaseUnitOfMeasure}").ToList()
            }
        };
        GroupedVatErrors = new Dictionary<string, List<string>>
        {
            {
                "VAT Error", invalidVatLines.Select(x => $"SKU: {x.ItemNumber} - VAT: {x.VAT}").ToList()
            }
        };
    }

    private async Task UploadPODetailFromFiles(IBrowserFile file)
    {
        try
        {
            IsLoad = true;
            var stream = new MemoryStream();
            await file.OpenReadStream(MaxAllowedSize).CopyToAsync(stream);
            stream.Position = 0;
            using var reader = ExcelReaderFactory.CreateReader(stream);
            var result = reader.AsDataSet(new ExcelDataSetConfiguration
            {
                ConfigureDataTable = _ => new ExcelDataTableConfiguration
                {
                    UseHeaderRow = true
                }
            });

            var memoryStreamCopy = new MemoryStream(stream.ToArray());
            var streamContent = new StreamContent(memoryStreamCopy);
            streamContent.Headers.ContentType = new MediaTypeHeaderValue(file.ContentType);
            if (file.Size > MaxAllowedSize)
            {
                ViewNotifier.Show($"File size exceeds the limit of {MaxAllowedSize} bytes.", ViewNotifierType.Error);
                return;
            }

            var table = result.Tables[0];

            var listDtPo = new List<POLinesFromFileDto>();
            var invalidVatLines = new List<POLinesFromFileDto>();
            var duplicateLines = new List<POLinesFromFileDto>();
            var seen = new HashSet<string>();
            for (var i = 0; i < table.Rows.Count; i++)
            {
                var row = table.Rows[i];
                if (row.ItemArray.Length < 5 || row.ItemArray[0] == null)
                {
                    ViewNotifier.Show("File không đúng định dạng. Vui lòng kiểm tra lại.", ViewNotifierType.Error);
                    return;
                }
                var poLineFromFile = new POLinesFromFileDto
                {
                    ItemNumber = GetStringValue(row, 0),
                    Quantity = GetIntValue(row, 1),
                    PurchaseUnitOfMeasure = GetStringValue(row, 2),
                    Type = GetStringValue(row, 3),
                    PriceB4VAT = GetDecimalValue(row, 4),
                    VAT = GetDecimalValue(row, 5),
                    Description = GetStringValue(row, 6),
                    DiscountB4VAT = GetDecimalValue(row, 7),
                };
                var purchasePrice =
                    await LoadLastUnitCostByItemByVendor(poLineFromFile.PurchaseUnitOfMeasure, poLineFromFile.ItemNumber);
                if (poLineFromFile.PriceB4VAT == 0)
                {
                    poLineFromFile.PriceB4VAT = purchasePrice.UnitCost;
                }
                if (poLineFromFile.VAT == 0)
                {
                    poLineFromFile.VAT = purchasePrice.VAT;
                }
                if (poLineFromFile.VAT is not (0 or 5 or 8 or 10))
                {
                    invalidVatLines.Add(poLineFromFile);
                }
                var key = $"{poLineFromFile.PurchaseUnitOfMeasure}|{poLineFromFile.ItemNumber}";
                if (!seen.Add(key))
                {
                    duplicateLines.Add(poLineFromFile);
                }
                listDtPo.Add(poLineFromFile);
            }
            // Check duplicate with POLines (server data)
            duplicateLines.AddRange(listDtPo.Where(item
                => POLines.Any(
                x => x.ItemNumber == item.ItemNumber && x.UnitOfMeasure == item.PurchaseUnitOfMeasure &&
                     x.DocumentType == (int)DocNoOccurrenceEnum.Order// hoặc so sánh đúng kiểu của bạn
                )));
            SetGroupedErrors(duplicateLines, invalidVatLines);
            if (duplicateLines.Count > 0 || invalidVatLines.Count > 0)
            {
                ErrorMessage = "Có lỗi khi upload file. Vui lòng kiểm tra lại.";
                StateHasChanged();
                return;
            }
            if (listDtPo.Count > 0)
            {
                await CreateMultipleLinesFromFileAsync(listDtPo);
                await GetDetailPOAsync(POHeader.Number);
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
        finally
        {
            IsLoad = false;
        }
    }

    private async Task CreateMultipleLinesFromFileAsync(List<POLinesFromFileDto> listDtPo)
    {
        try
        {
            ErrorMessage = string.Empty;
            if (listDtPo == null || listDtPo.Count == 0)
            {
                return;
            }

            var poLines = new List<POLineAddOrUpdate>();

            foreach (var poLine in listDtPo)
            {
                var isPromotion = poLine.Type.Equals("promotional", StringComparison.CurrentCultureIgnoreCase);
                var isConsignment = poLine.Type.Equals("consignment", StringComparison.CurrentCultureIgnoreCase);
                var newPOLineToCreate = new POLineGetDto
                {
                    Vat = poLine.VAT,
                    LotNo = "notset",
                    Quantity = poLine.Quantity,
                    UnitCost = poLine.PriceB4VAT,
                    UnitPrice = poLine.PriceB4VAT,
                    ItemNumber = poLine.ItemNumber,
                    LastUnitCost = poLine.PriceB4VAT,
                    Description = poLine.Description,
                    DocumentNumber = POHeader.Number,
                    LineDiscountPercent = poLine.DiscountB4VAT,
                    UnitOfMeasure = poLine.PurchaseUnitOfMeasure,
                    ExpirationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
                    DocumentType = (int)DocNoOccurrenceEnum.Order
                };
                if (isPromotion || isConsignment)// Nếu là hàng khuyến mãi ||  ký gửi
                {
                    newPOLineToCreate.DocumentType =
                        isPromotion ? (int)DocNoOccurrenceEnum.Promotional : (int)DocNoOccurrenceEnum.Consigned;
                    newPOLineToCreate.UnitCost = 0;
                }
                newPOLineToCreate.UpdateAmount();
                var poLineInsert = Mapper.Map<POLineAddOrUpdate>(newPOLineToCreate);
                poLines.Add(poLineInsert);
            }

            var apiResponse = await PurchaseOrderApiClient.AddMultipleLines(poLines);

            if (!apiResponse.IsSuccessStatusCode)
            {
                ErrorMessage = apiResponse.Message;
                ViewNotifier.Show("Validation failed", ViewNotifierType.Error);
                return;
            }
            await GetDetailPOAsync(POHeader.Number);
            ViewNotifier.Show(L["Operation Successful"], ViewNotifierType.Success);
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
    }
    #endregion


    private void AddOrderLine()
    {
        IsShowAddFormLine = true;
        CurrentLine = new POLineGetDto
        {
            DocumentNumber = POHeader.Number,
            LotNo = "notset",
            Quantity = 1,
            QuantityToReceive = 1,
            ExpirationDate = DateOnly.FromDateTime(DateTime.Today.AddDays(30)),
            LineNumber = (POLines == null || POLines.Count == 0 ? 0 : POLines.Select(x => x.LineNumber).Max()) + 1,
            Type = 0,
            Description = "",
        };
    }

    private void OnVendorItemAddedCallback(bool isSuccess)
    {
        if (isSuccess)
            IsShowAddNewVendorItem = false;
    }

    private async Task<List<DetailItemDto>> SearchVendorItemByName(string value)
    {
        try
        {
            var resp = await ApiClient.GetAllVendorItems(new VendorItemFilter
            {
                VendorNumber = POHeader.BuyFromVendorNumber,
                ItemName = value,
                ItemNumber = value
            });
            var mappedItems = Mapper.Map<List<DetailItemDto>>(resp.Results);
            return mappedItems;
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
            return default;
        }
    }
    private async Task<IEnumerable<DetailItemDto>> ItemSearch(string value, CancellationToken token)
    {
        try
        {
            IEnumerable<DetailItemDto> result = new List<DetailItemDto>();
            //if user is vendor role, get vendor items
            if (!string.IsNullOrEmpty(POHeader.BuyFromVendorNumber))
            {
                result = await SearchVendorItemByName(value);
            }

            if (result.Any())
            {
                return result;
            }
            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrEmpty(value))
            {
                value = string.Empty;
            }
            var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<List<DetailItemDto>>>(
            $"api/data/SearchItem?number={value.Trim()}", token);

            if (!apiResponse.IsSuccessStatusCode)
            {
                return result;
            }

            result = apiResponse.Result;

            return result;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return new List<DetailItemDto>();
        }
    }

    private async Task GetDetailPOAsync(string number)
    {
        if (number.IsNullOrEmpty())
        {
            ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
            return;
        }
        try
        {
            var apiResponse = await PurchaseOrderApiClient.GetLines(PONumber);
            if (!apiResponse.IsSuccessStatusCode || apiResponse.Result == null)
            {
                ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Warning);
            }
            else
            {
                POLines = [.. apiResponse.Result.OrderByDescending(x => x.DocumentType)];
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
        }
    }
    private async Task GetHeaderPO(string number)
    {
        if (string.IsNullOrEmpty(number)) ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
        else
            try
            {
                var apiResponse = await PurchaseOrderApiClient.GetHeader(PONumber);

                if (!apiResponse.IsSuccessStatusCode)
                {
                    ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
                    return;
                }
                POHeader = apiResponse.Result;
                if (POHeader != null)
                {
                    var vendorApiResponse = await VendorApiClient.GetSingleVendor(POHeader.BuyFromVendorNumber);

                    if (!vendorApiResponse.IsSuccessStatusCode)
                    {
                        ViewNotifier.Show(L["Vendor Not Found"], ViewNotifierType.Error);
                    }
                    else
                    {
                        VendorInfo = vendorApiResponse.Result;
                    }
                }
            }
            catch (Exception e)
            {
                ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
            }
    }
    private async Task SaveChangeHeaderPOOrOpenPO()
    {
        try
        {
            if (IsEdit)
            {
                await HandleEditMode();
            }
            else
            {
                await HandleOpenMode();
            }

            StateHasChanged();
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.GetBaseException().Message, ViewNotifierType.Error);
        }
    }
    private async Task CreatePoDetailAsync()
    {
        try
        {
            var item = CurrentLine.Item;

            var foundIuom = item?.ItemUnitOfMeasures?.Where(x => x.Code == CurrentLine.UnitOfMeasure).FirstOrDefault();
            if (foundIuom != null)
            {
                CurrentLine.QtyPerUnitOfMeasure = foundIuom.QuantityPerUnitOfMeasure;
            }

            if (CurrentLine.Quantity == 0)
            {
                ViewNotifier.Show(L["Quantity cannot be zero"], ViewNotifierType.Warning);
                return;
            }

            //  promotion order
            if (POHeader.DocNoOccurrence == 2)
            {
                CurrentLine.DocumentType = 1;// gif
                CurrentLine.UnitCost = 0;
            }
            if (CurrentLine.DocumentType == (int)POLineStatusEnum.Promotion)
            {
                CurrentLine.LotNo = "KM";// KM = Khuyến mãi
                CurrentLine.UnitCost = 0;
                CurrentLine.Vat = 0;
                CurrentLine.LineDiscountAmount = 0;
                CurrentLine.Amount = 0;
            }
            // newPOLine.UnitOfMeasure = newPOLine.uni;
            CurrentLine.UpdateAmount();
            var poLineInsert = Mapper.Map<POLineAddOrUpdate>(CurrentLine);
            poLineInsert.Desc = poLineInsert.Desc ??= "";

            var apiResponseAddLine = await PurchaseOrderApiClient.AddLine(poLineInsert);

            if (!apiResponseAddLine.IsSuccessStatusCode)
            {
                ViewNotifier.Show(apiResponseAddLine.Message, ViewNotifierType.Error, L["Operation Failed"]);
                return;
            }
            await GetDetailPOAsync(PONumber);
            ViewNotifier.Show(L["Operation Successful"], ViewNotifierType.Success);
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            IsShowAddFormLine = false;
            CurrentLine = new POLineGetDto();
        }
    }
    private async Task CallReloadFileTable() => await ListFilePageRef.ReloadFromPage();
    private void VendorApprove()
        => POHeader.VendorApprovalBy = string.IsNullOrEmpty(POHeader.VendorApprovalBy) ? UserViewModel.UserName : "";

    private void PurchaserApprove()
        => POHeader.PurchaserApprovalBy = string.IsNullOrEmpty(POHeader.PurchaserApprovalBy) ? UserViewModel.UserName : "";
    private async Task ExportDetailPo()
    {
        try
        {
            var exportFile = await POServicesHandler.ExportDetailPo(POLines, POHeader, JsRuntime);
            ViewNotifier.Show(exportFile.Message, !exportFile.IsSuccess ? ViewNotifierType.Error : ViewNotifierType.Success);
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
    }
    private async Task DownloadReportPO()
    {
        var apiResponse = await PurchaseOrderApiClient.DownloadPo(POHeader.Number);

        if (!apiResponse.IsSuccessStatusCode)
        {
            ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
            return;
        }
        UrlFileReport = apiResponse.Result.ToString();
        IsShowReport = true;
        StateHasChanged();
    }
    private void OnClickPreviewEmail()
    {
        var param = new QueryBuilder{
            {
                "PONumber", POHeader.Number
            },
            {
                "OrderDate", POHeader.OrderDate.ToString(CultureInfo.InvariantCulture)
            },
            {
                "ConfirmDate", POHeader.DocumentDate.ToString(CultureInfo.InvariantCulture)
            },
            {
                "VendorName", POHeader.BuyFromVendorName
            },
            {
                "VendorNumber", POHeader.BuyFromVendorNumber
            },
            {
                "PostDescription", POHeader.PostingDescription
            }
        };
        NavigationManager.NavigateTo("po/send-mail/preview" + param.ToQueryString());
    }
    private async Task DeleteMultiLine()
    {
        try
        {
            bool? result = await DialogService.ShowMessageBox(
                "Warning",
                "Deleting can not be undone!",
                yesText: "Delete!", cancelText: "Cancel");
            if (result != null)
            {
                // API
                if (Table.SelectedItems != null)
                {
                    foreach (var item in Table.SelectedItems)
                    {
                        var apiResponse = await PurchaseOrderApiClient.DeleteLine(POHeader.Number, item.ItemNumber, item.RowId);
                        if (apiResponse.IsSuccessStatusCode)
                        {
                            POLines.Remove(item);
                        }
                    }
                }
                await Table.ReloadServerData();
                ViewNotifier.Show("Success", ViewNotifierType.Success);
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (IsEdit)
        {
            if (POHeader.Status == (int)PurchaseOrderEnum.CancelConfirm)
            {
                await HandleEditMode();
            }
            await PurchaseOrderApiClient.CloseDocument(POHeader.Number);
        }
    }

    private async Task OnItemSelectedInAutoComplete(DetailItemDto dto)
    {
        try
        {
            var itemNumber = dto.Number;
            var vendorNumber = POHeader.BuyFromVendorNumber.Trim();
            // Get purchase price By itemNumber and vendorNumber
            // Setting giá gốc
            if (itemNumber != null)
            {
                var apiResponse = await PurchasePriceApiClient.GetPurchasePriceAsync(itemNumber, vendorNumber);
                if (apiResponse.Result.Count == 0)
                {
                    ViewNotifier.Show("Giá tiền chưa được cài đặt.", ViewNotifierType.Warning);
                }
                else
                {
                    ListPurchasePriceDto = apiResponse.Result;
                }
            }
            if (dto.VatProductPostingGroup != null)
            {
                CurrentLine.Vat = int.Parse(dto.VatProductPostingGroup);
            }
            // Default
            CurrentLine.Item = dto;
            CurrentLine.ItemName = dto.Name;
            CurrentLine.ItemNumber = dto.Number;
            LsDetailItemUnitOfMeasureDtoEditing = dto.ItemUnitOfMeasures;
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Get Last Unit Cost has error: " + e.GetBaseException().Message, ViewNotifierType.Error);
            throw;
        }
    }

    private async Task<GetLatestPriceDto> LoadLastUnitCostByItemByVendor(string unit, string itemNumber)
    {
        try
        {
            var resp = await PurchaseOrderApiClient.GetLatestPrice(itemNumber, unit, POHeader.BuyFromVendorNumber.Trim());

            if (resp.IsSuccessStatusCode)
            {
                var latestPrice = resp.Result;

                if (latestPrice.LastUnitCost != 0 || latestPrice.UnitCost != 0)
                {
                    return latestPrice;
                }
                ViewNotifier.Show("Liên hệ IT để sync Data ERP.", ViewNotifierType.Warning);
                return latestPrice;
            }
            ViewNotifier.Show("Get Last Unit Cost has error: " + resp.Message, ViewNotifierType.Error);

            return new GetLatestPriceDto();
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Get Last Unit Cost has error: " + e.Message, ViewNotifierType.Error);
            return new GetLatestPriceDto();
        }
    }

    private async Task OnUOMInLineChanged(POLineGetDto dto, string unit)
    {
        try
        {
            var latestPrice = await LoadLastUnitCostByItemByVendor(unit, dto.ItemNumber);
            dto.LastUnitCost = latestPrice.LastUnitCost;
            dto.UnitCost = latestPrice.UnitCost;
            dto.UnitOfMeasure = unit;
            dto.QtyPerUnitOfMeasure = latestPrice.QtyPerUnitOfMeasure;
            dto.Vat = latestPrice.VAT;
        }
        catch (Exception e)
        {
            ViewNotifier.Show("Get Last Unit Cost has error: " + e.Message, ViewNotifierType.Error);
        }
    }

    protected void OnDueDateChanged(DateTime? dueDate)
    {
        if (dueDate is not null) POHeader.DueDate = dueDate.Value;
    }

    protected string GetCurrencyCode()
    {
        var currencyCode = string.Empty;
        if (POHeader != null && !string.IsNullOrEmpty(POHeader.CurrencyCode))
        {
            currencyCode = POHeader.CurrencyCode;
        }
        return currencyCode;
    }
    #region editing po line
    protected async Task OnEditPOLine(POLineGetDto dto)
    {
        try
        {
            CurrentLine = new POLineGetDto();
            if (!IsEdit)
            {
                return;
            }
            var itemNumber = dto.ItemNumber;

            // Get item unit of measure by itemNumber
            var apiResponse = await ItemApiClient.GetItemUnitOfMeasure(itemNumber);
            if (apiResponse.IsSuccessStatusCode)
            {
                LsDetailItemUnitOfMeasureDtoEditing = apiResponse.Result;
            }
            CurrentLine = dto.DeepClone();
            IsShowEditPOLineDialog = true;
            IsShowAddFormLine = false;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    protected void OnCloseEditPOLineDialog()
    {
        IsShowAddFormLine = false;
        IsShowEditPOLineDialog = false;
    }
    private async Task OnSavePOLineAsync()
    {
        CurrentLine.ExpectedReceiptDate = ExpirationDateForPicker ?? DateTime.Now;
        if (IsShowAddFormLine)
        {
            await CreatePoDetailAsync();
            return;
        }

        await EditPOLineFormRef.Validate();
        if (EditPOLineFormRef.IsValid)
        {
            var rq = Mapper.Map<POLineAddOrUpdate>(CurrentLine);
            rq.Desc = CurrentLine.Description;
            var apiResponse = await PurchaseOrderApiClient.UpdatePurchaseOrderLine(rq);
            if (!apiResponse.IsSuccessStatusCode)
            {
                ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
            }
            else
            {
                ViewNotifier.Show(L["Update success"], ViewNotifierType.Success);
                OnCloseEditPOLineDialog();
                await GetDetailPOAsync(PONumber);
            }
        }
    }

    private async Task HandleEditMode()
    {
        if (POLines.Count == 0)
        {
            await DialogService.ShowMessageBox("Warning", "Chi tiết đang rỗng");
            return;
        }

        var headerDto = new UpdatePOHeaderDto
        {
            Number = POHeader.Number,
            VendorNo = POHeader.BuyFromVendorNumber,
            OrderDate = POHeader.OrderDate,
            DueDate = POHeader.DueDate,
            PurchaseUser = UserViewModel.UserName,
            PurchaserApprovalBy = POHeader.PurchaserApprovalBy,
            VendorApprovalBy = POHeader.VendorApprovalBy,
            YourReference = POHeader.YourReference,
            PostingDescription = POHeader.PostingDescription,
            DocNoOccurrence = POHeader.DocNoOccurrence
        };

        var (isSuccess, message) = await POServicesHandler.SaveHeaderAndProcessAsync(headerDto, POHeader);

        if (!isSuccess)
        {
            ViewNotifier.Show(message, ViewNotifierType.Error);
            return;
        }

        IsEdit = false;
        ViewNotifier.Show(L["Success"], ViewNotifierType.Success);
    }

    private async Task HandleOpenMode()
    {
        await GetHeaderPO(PONumber);

        var openResult = await PurchaseOrderApiClient.OpenDocument(POHeader.Number);
        if (!openResult.IsSuccessStatusCode)
        {
            ViewNotifier.Show(openResult.Message, ViewNotifierType.Error);
            return;
        }
        POHeader.Status = (int)PurchaseOrderEnum.CancelConfirm;
        IsEdit = true;
    }
    #endregion
}
