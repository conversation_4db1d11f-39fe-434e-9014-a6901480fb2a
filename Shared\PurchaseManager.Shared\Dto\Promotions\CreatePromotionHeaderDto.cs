namespace PurchaseManager.Shared.Dto.Promotions;

public class CreatePromotionHeaderDto
{
    public string Number { get; set; } = string.Empty;
    public string ProgramName { get; set; } = string.Empty;

    public string Description { get; set; } = string.Empty;

    public string VendorCode { get; set; }

    public DateTime StartDate { get; set; } = DateTime.Now.Date;

    public DateTime EndDate { get; set; } = DateTime.Now.Date.AddDays(30);

    /// <summary>
    /// Tích lu<PERSON> doanh số (true/false)
    /// </summary>
    public bool AccumulateRevenue { get; set; } = false;

    /// <summary>
    /// Loại chương trình: 1=FrontMargin, 2=BackMargin
    /// </summary>
    public int ProgramType { get; set; } = 1; // Default to FrontMargin

    /// <summary>
    /// Status: 1=Draft, 2=Active, 3=Inactive, 4=Expired
    /// </summary>
    public int Status { get; set; } = 1; // Default to Draft

    /// <summary>
    /// <PERSON>i<PERSON> trị đơn hàng tối thiểu
    /// </summary>
    public decimal? MinOrderValue { get; set; }

    /// <summary>
    /// Giá trị đơn hàng tối đa
    /// </summary>
    public decimal? MaxOrderValue { get; set; }

    /// <summary>
    /// Giá trị chiết khấu tối đa
    /// </summary>
    public decimal? MaxDiscountAmount { get; set; }

    /// <summary>
    /// Loại đơn hàng áp dụng (1,2,3...)
    /// </summary>
    public string? ApplicableDocTypes { get; set; }

    /// <summary>
    /// Độ ưu tiên khi có nhiều CTKM
    /// </summary>
    public int Priority { get; set; } = 1;

    /// <summary>
    /// Ngân sách CTKM
    /// </summary>
    public decimal? BudgetAmount { get; set; }
}
