﻿namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
///     DTO for Front Margin promotion response
/// </summary>
public class FrontMarginPromotionDto
{
    public long RowId { get; set; }
    public string Number { get; set; } = string.Empty;
    public string ProgramCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Priority { get; set; }
    public decimal? BudgetAmount { get; set; }
    public decimal UsedAmount { get; set; }
    public decimal? RemainingAmount { get; set; }
    public decimal? MinOrderValue { get; set; }
    public decimal? MaxOrderValue { get; set; }
    public decimal? MaxDiscountAmount { get; set; }
    public string? ApplicableDocTypes { get; set; }
    public bool AccumulateRevenue { get; set; }
    public int Status { get; set; }
    public string StatusName { get; set; } = string.Empty;
    public int ApprovalStatus { get; set; }
    public string ApprovalStatusName { get; set; } = string.Empty;
    public string? VendorApprovalBy { get; set; }
    public DateTime? VendorApprovalDate { get; set; }
    public string? PurchaserApprovalBy { get; set; }
    public DateTime? PurchaserApprovalDate { get; set; }
    public string? UsingID { get; set; }
    public DateTime? BeginUsingTime { get; set; }

    public List<PromotionConditionDto> Conditions { get; set; } = [];
    public List<PromotionRewardDto> Rewards { get; set; } = [];
}
