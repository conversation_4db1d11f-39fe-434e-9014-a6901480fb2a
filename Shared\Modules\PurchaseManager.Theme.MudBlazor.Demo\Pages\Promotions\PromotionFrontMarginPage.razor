@page "/promotional/front-margin/{PromotionNumber}"
@using PurchaseManager.Constants.Enum
@using PurchaseManager.Shared.Dto.Item
@using PurchaseManager.Shared.Dto.Promotions
@using PurchaseManager.Shared.Dto.Promotions.FrontMargins
@using PurchaseManager.Theme.Material.Demo.Shared.Components.Promotions
@inherits FrontMarginPage;
@if (IsLoad)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <CascadingAuthenticationState>
        <PromotionToolBar @ref="ToolBarRef"
                          Title="@L["Promotion FrontMargin"]"
                          IsEdit="@IsEdit"
                          EmailButtonDisabled="@(GetCurrentStatus() != 2)"
                          PrintButtonDisabled="@(PromotionHeader.FrontMargins?.Count == 0)"
                          ExportButtonDisabled="@(PromotionHeader.FrontMargins?.Count == 0)"
                          ExportTooltipText="Export Front Margin Detail To Excel"
                          ShowEmailButton="true"
                          ShowPrintButton="true"
                          ShowExportButton="true"
                          OnSaveOrEdit="@SaveOrEditPromotion"
                          OnStatusChanged="@UpdatePromotionStatus"
                          OnEmail="@SendEmail"
                          OnPrint="@PrintPromotion"
                          OnExport="@ExportPromotion"/>
        <MudForm Spacing="4">
            <MudGrid Class="mb-2">
                <MudItem xs="4">
                    <MudStack Spacing="2">
                        <MudTextField Variant="Variant.Outlined"
                                      ReadOnly
                                      Adornment="Adornment.None"
                                      Label="@L["ProgramCode"]"
                                      Placeholder="Mã chi chương trình khuyến mãi"
                                      Value="@PromotionHeader.ProgramCode">
                        </MudTextField>
                        <MudTextField Variant="Variant.Outlined" T="String" Label="@L["Vendor"]"
                                      Value="@($"{PromotionHeader.VendorCode} - {PromotionHeader.VendorName}")"
                                      ReadOnly/>
                    </MudStack>
                </MudItem>
                <MudItem xs="4">
                    <MudStack Spacing="2">
                        <MudTextField Variant="Variant.Outlined" Adornment="Adornment.None"
                                      ReadOnly="@(!IsEdit)"
                                      Label="@L["ProgramName"]"
                                      Placeholder="Tên chương trình khuyến mãi"
                                      @bind-Value="@PromotionHeader.ProgramName">
                        </MudTextField>
                        <MudStack Spacing="2" Row>
                            <MudDatePicker ReadOnly Label="@L[nameof(GetPromotionHeaderDto.StartDate)]"
                                           MinDate="DateTime.Today"
                                           Variant="Variant.Outlined" Editable="true"
                                           Date="@PromotionHeader.StartDate" DateChanged="@OnStartDateChange"/>
                            <MudDatePicker ReadOnly Label="@L[nameof(GetPromotionHeaderDto.EndDate)]" Editable="true"
                                           MinDate="PromotionHeader.StartDate.AddDays(15)" Variant="Variant.Outlined"
                                           Date="@PromotionHeader.EndDate" DateChanged="@OnStartDateChange"/>
                        </MudStack>
                    </MudStack>
                </MudItem>
                <MudItem xs="4">
                    <MudStack Spacing="2">
                        <MudTextField Lines="5" Variant="Variant.Outlined" Adornment="Adornment.None"
                                      Label="@L["Description"]"
                                      ReadOnly="@(!IsEdit)"
                                      Placeholder="Mô tả chương trình khuyến mãi"
                                      @bind-Value="@PromotionHeader.Description">
                        </MudTextField>
                    </MudStack>
                </MudItem>
            </MudGrid>
        </MudForm>
        <br>
        @if (IsEdit)
        {
            <MudStack Class="ps-3">
                <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.FlexStart">
                    @if (PromotionFrontMarginTable.SelectedItems is { Count: > 0 })
                    {
                        <MudBadge Content="@PromotionFrontMarginTable.SelectedItems.Count" Color="Color.Error"
                                  Overlap="true">
                            <MudIconButton Icon="@Icons.Material.Filled.Delete" Size="Size.Small"
                                           OnClick="DeletePromotionFrontMarginsAsync"
                                           Variant="Variant.Outlined"
                                           Disabled="@(!IsEdit)"
                                           Color="Color.Error">
                            </MudIconButton>
                        </MudBadge>
                    }
                    @if (IsEdit)
                    {
                        <MudButton Size="Size.Small" StartIcon="@Icons.Material.Filled.Add"
                                   OnClick="AddCondition" Disabled="@(!IsEdit)"
                                   Variant="Variant.Outlined"
                                   Color="Color.Success">
                            Thêm
                        </MudButton>
                        <MudTooltip Placement="Placement.Bottom" Text="Import PO Detail From Excel">
                            <MudFileUpload T="IBrowserFile" MaximumFileCount="1"
                                           Accept=".xlsx">
                                <ActivatorContent>
                                    <MudIconButton Icon="@Icons.Custom.FileFormats.FileExcel"
                                                   Color="Color.Success" Size="Size.Medium">
                                    </MudIconButton>
                                </ActivatorContent>
                            </MudFileUpload>
                        </MudTooltip>
                        <MudSpacer/>
                        <MudTooltip Placement="Placement.Bottom"
                                    Text="Download sample file for import PO Detail">
                            <MudLink Underline="Underline.Always"
                                     Href="/files/Import_POLine_SampleData_10062025.xlsx">
                                <MudIconButton Icon="@Icons.Material.Outlined.Download" Color="Color.Success"
                                               Size="Size.Medium">
                                </MudIconButton>
                            </MudLink>
                        </MudTooltip>
                    }
                </MudStack>
            </MudStack>
        }
        <MudTable Dense Items="@PromotionHeader.FrontMargins" T="GetPromotionFrontMarginDto"
                  @ref="PromotionFrontMarginTable" Height="550px"
                  FixedHeader FixedFooter
                  CustomFooter
                  HorizontalScrollbar
                  SelectOnRowClick="false"
                  OnRowClick="@(async row => await OnEditPOLine(row.Item))"
                  ReadOnly="@(!IsEdit)" MultiSelection="@IsEdit">
            <HeaderContent>
                <MudTh Style="width: 500px">@L["ItemName"]</MudTh>
                <MudTh Style="cursor: pointer; width: 130px">@L["Status"]</MudTh>
                <MudTh Style="width: 130px">@L["Unit"]</MudTh>
                <MudTh Style="width: 200px; text-align: center">@L["DiscountPercentage"]</MudTh>
                <MudTh Style="width: 500px">@L["Notes"]</MudTh>
            </HeaderContent>
            <RowTemplate Context="poLine">
                <MudTd>
                    <MudText Typo="Typo.body1">
                        @poLine.ItemName
                    </MudText>
                    <MudText Typo="Typo.caption">
                        <b>Number:</b>
                        @poLine.ItemNumber
                    </MudText>
                </MudTd>
                <MudTd>
                    <MudText Style="max-width:200px; overflow: hidden;">
                        @switch (poLine.Status)
                        {
                            case (int)PromotionFontMarginStatus.Active:
                                <MudChip Style="width: 160px" T="string" Icon="@Icons.Material.Filled.CheckCircle"
                                         Color="Color.Success">@L["Active"]
                                </MudChip>
                                break;
                            case (int)PromotionFontMarginStatus.Inactive:
                                <MudChip Style="width: 103px" T="string" Icon="@Icons.Material.Filled.Block"
                                         Color="Color.Warning">@L["Inactive"]
                                </MudChip>
                                break;
                        }
                    </MudText>
                </MudTd>
                <MudTd Style="text-align: center">@poLine.UnitOfMeasure.ToUpper()</MudTd>
                <MudTd Style="text-align: center">
                    @poLine.DiscountPercentage.ToString("N2") %
                </MudTd>
                <MudTd>@poLine.Notes</MudTd>
            </RowTemplate>
            <NoRecordsContent>
                No Data
            </NoRecordsContent>
        </MudTable>
    </CascadingAuthenticationState>
}

<MudDialog Visible="@(IsShowEditFrontMarginDialog || IsShowAddFrontMarginDialog)"
           Options="new DialogOptions { FullWidth = true, MaxWidth = MaxWidth.Small, CloseOnEscapeKey= false, BackdropClick= false }"
           TitleClass="mud-secondary" ContentStyle="min-height:200px">
    <TitleContent>
        @if (IsShowEditFrontMarginDialog)
        {
            <div>
                @L["Edit line"] -
                <MudText Inline Typo="Typo.subtitle2"
                         Color="Color.Primary">@CurrentLine.ItemNumber - @CurrentLine.ItemName</MudText>
                Unit:
                <MudText Inline Typo="Typo.subtitle2" Color="Color.Primary">@CurrentLine.UnitOfMeasure</MudText>
            </div>
        }
        else
        {
            @L["Add new line"]
        }
    </TitleContent>
    <DialogContent>
        <MudForm @ref="@EditPromotionFrontMarginFormRef" Model="@CurrentLine">
            <MudGrid>
                <MudItem xs="12" sm="6" md="12">
                    @if (IsShowEditFrontMarginDialog)
                    {
                        <MudTextField Variant="Variant.Outlined" T="String" Label="@L["ItemNumber"]"
                                      Value="@($"{CurrentLine.ItemNumber} - {CurrentLine.ItemName}")"
                                      ReadOnly/>
                    }
                    else
                    {
                        <MudAutocomplete Variant="Variant.Outlined" T="DetailItemDto" ShrinkLabel
                                         Label="@L["ItemNumber"]"
                                         ShowProgressIndicator
                                         ValueChanged="@(dto => OnItemSelectedInAutoComplete(dto))"
                                         ToStringFunc="@(dto => dto == null
                                                           ? $"{CurrentLine.ItemNumber} - {CurrentLine.ItemName}"
                                                           : dto.Number + " - " + dto.Name)"
                                         Required
                                         SearchFunc="@ItemSearch">
                            <ProgressIndicatorInPopoverTemplate>
                                <MudList T="String" ReadOnly>
                                    <MudListItem>
                                        Loading...
                                    </MudListItem>
                                </MudList>
                            </ProgressIndicatorInPopoverTemplate>
                            <ItemTemplate Context="e">
                                <MudStack Row="false" StretchItems="StretchItems.All">
                                    <MudStack Spacing="0">
                                        <MudText>@e.Name</MudText>
                                        <MudStack Row Spacing="0">
                                            <MudText Typo="Typo.caption">@e.Number</MudText>
                                            <MudChip T="String" Size="Size.Small" Variant="Variant.Text"
                                                     Color="@(e.Blocked == 1 ? Color.Warning : Color.Success)">@(e.Blocked == 1 ? "Blocked" : "Active")</MudChip>
                                            <MudChip T="String" Size="Size.Small" Variant="Variant.Text"
                                                     Color="@(e.Status == 2 ? Color.Success : Color.Warning)">
                                                Status
                                            </MudChip>
                                        </MudStack>
                                    </MudStack>
                                </MudStack>
                            </ItemTemplate>
                        </MudAutocomplete>
                    }
                </MudItem>
            </MudGrid>
            <MudGrid Spacing="2">
                <MudItem xs="12" sm="3" md="6">
                    <MudSelect Variant="Variant.Outlined" T="String" Label="@L["Unit"]"
                               @bind-Value="CurrentLine.UnitOfMeasure"
                               ToStringFunc="dto => dto ?? CurrentLine.UnitOfMeasure">
                        @if (LsDetailItemUnitOfMeasureDtoEditing != null)
                        {
                            foreach (var unit in LsDetailItemUnitOfMeasureDtoEditing)
                            {
                                <MudSelectItem Value="@unit.Code"
                                               Disabled="@(unit.Block == 1)">@unit.Code.ToUpper()</MudSelectItem>
                            }
                        }
                    </MudSelect>
                </MudItem>
                <MudItem xs="12" sm="6">
                    <MudNumericField Variant="Variant.Outlined" T="Decimal" HideSpinButtons
                                     Label="@L[nameof(GetPromotionFrontMarginDto.DiscountPercentage)]"
                                     @bind-Value="CurrentLine.DiscountPercentage"
                                     Format="N2" Immediate Min="0" Required/>
                </MudItem>
            </MudGrid>
            <MudGrid>
                <MudItem xs="12">
                    <MudTextField Variant="Variant.Outlined" T="String" Label="@L["Description"]" Lines="3"
                                  @bind-Value="CurrentLine.Notes" AutoGrow/>
                </MudItem>
            </MudGrid>
        </MudForm>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Text" Color="Color.Error"
                   OnClick="@OnCloseEditPOLineDialog">@L["Cancel"]</MudButton>
        <MudButton Variant="Variant.Filled" Color="Color.Primary"
                   OnClick="@OnSavePOLineAsync">@L["Save"]</MudButton>
    </DialogActions>
</MudDialog>
<MudDialog Visible="@(!string.IsNullOrWhiteSpace(ErrorMessage))"
           Options="new DialogOptions { FullWidth = true, MaxWidth = MaxWidth.Medium, CloseOnEscapeKey = false, BackdropClick = false }"
           TitleClass="mud-secondary" ContentStyle="min-height:200px">
    <TitleContent>
        <MudText Inline Typo="Typo.h5" Align="Align.Start">@L["List error create Line"]</MudText>
    </TitleContent>
    <DialogContent>
        <div>
            @foreach (var error in ErrorMessage.Split("\n"))
            {
                <MudChip T="String" Variant="Variant.Text" Color="Color.Warning">@error</MudChip>
            }
        </div>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Text" Color="Color.Error"
                   OnClick="@(_ => ErrorMessage = string.Empty)">@L["Clear"]</MudButton>
    </DialogActions>
</MudDialog>
