@inherits CreatePromotionPage;
@using PurchaseManager.Constants.Enum

<MudDialog Visible="@IsDialogVisible"
           Options="new DialogOptions { FullWidth = true, MaxWidth = MaxWidth.Medium, CloseOnEscapeKey = false, BackdropClick = false }"
           TitleClass="mud-secondary"
           ContentStyle="min-height:400px; max-height:80vh; overflow-y:auto;">
    <TitleContent>
        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
            <MudText Typo="Typo.h6" Color="Color.Primary">
                <MudIcon Icon="@Icons.Material.Filled.Add" Class="me-2"/>
                @L["Create New Promotion"]
            </MudText>
        </MudStack>
    </TitleContent>
    <DialogContent>
        <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
            <MudForm @ref="Form" @bind-IsValid="IsFormValid" ReadOnly="IsLoading">
                <MudGrid Spacing="4">
                    <!-- Basic Information Section -->
                    <MudItem xs="12">
                        <MudStack Spacing="3">
                            <MudGrid Spacing="3">
                                <MudItem xs="12" md="8">
                                    <MudTextField Label="@L["Program Name"]"
                                                  @bind-Value="CreatePromotionHeaderDto.ProgramName"
                                                  Required
                                                  Placeholder="@L["Enter program name"]"
                                                  RequiredError="@L["Program name is required"]"
                                                  Variant="Variant.Outlined"
                                                  FullWidth/>
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudSelect Label="@L["Program Type"]"
                                               @bind-Value="CreatePromotionHeaderDto.ProgramType"
                                               Required
                                               Variant="Variant.Outlined"
                                               FullWidth>
                                        <MudSelectItem Value="@((Int32)PromotionProgramTypeEnum.FrontMargin)">
                                            @GetPromotionTypeText((Int32)PromotionProgramTypeEnum.FrontMargin)
                                        </MudSelectItem>
                                        <MudSelectItem Value="@((Int32)PromotionProgramTypeEnum.BackMargin)">
                                            @GetPromotionTypeText((Int32)PromotionProgramTypeEnum.BackMargin)
                                        </MudSelectItem>
                                    </MudSelect>
                                </MudItem>
                            </MudGrid>
                        </MudStack>
                    </MudItem>
                    <MudItem xs="12">
                        <MudStack Spacing="3">
                            <MudGrid Spacing="3">
                                <MudItem xs="12" md="8">
                                    <MudAutocomplete T="GetVendorDto"
                                                     Label="@L["Vendor"]"
                                                     ShowProgressIndicator
                                                     Clearable
                                                     Required
                                                     RequiredError="@L["Vendor is required"]"
                                                     Placeholder="@L["Search vendor by name..."]"
                                                     @ref="AutoComplete"
                                                     ValueChanged="@(vendorDto => CreatePromotionHeaderDto.VendorCode = vendorDto?.Number)"
                                                     Variant="Variant.Outlined"
                                                     FullWidth
                                                     ToStringFunc="dto => dto == null ? null : string.Concat(dto.Number, '-', dto.Name)"
                                                     SearchFunc="@VendorAutoComplete">
                                        <ProgressIndicatorInPopoverTemplate>
                                            <MudList T="String" ReadOnly>
                                                <MudListItem>
                                                    <MudProgressCircular Size="Size.Small" Indeterminate/>
                                                    <MudText Class="ml-2">@L["Loading..."]</MudText>
                                                </MudListItem>
                                            </MudList>
                                        </ProgressIndicatorInPopoverTemplate>
                                        <ItemTemplate Context="e">
                                            <MudStack Row Justify="Justify.SpaceBetween"
                                                      AlignItems="AlignItems.Center">
                                                <MudStack Spacing="1">
                                                    <MudText Typo="Typo.body1"><b>@e.Name</b></MudText>
                                                    <MudText Typo="Typo.body2">@L["Number"]: @e.Number</MudText>
                                                </MudStack>
                                                <MudChip T="Boolean" Size="Size.Small"
                                                         Color="@(e.Blocked == 1 ? Color.Error : Color.Success)"
                                                         Variant="Variant.Filled">
                                                    @(e.Blocked == 1 ? "Blocked" : "Active")
                                                </MudChip>
                                            </MudStack>
                                        </ItemTemplate>
                                    </MudAutocomplete>
                                </MudItem>
                                <MudItem xs="12" md="4">
                                    <MudSelect Label="@L["Status"]"
                                               @bind-Value="CreatePromotionHeaderDto.Status"
                                               Required
                                               Variant="Variant.Outlined"
                                               FullWidth>
                                        <MudSelectItem Value="@((Int32)PromotionStatusEnum.Draft)">
                                            @GetStatusText((Int32)PromotionStatusEnum.Draft)
                                        </MudSelectItem>
                                        <MudSelectItem Value="@((Int32)PromotionStatusEnum.Active)">
                                            @GetStatusText((Int32)PromotionStatusEnum.Active)
                                        </MudSelectItem>
                                        <MudSelectItem Value="@((Int32)PromotionStatusEnum.Inactive)">
                                            @GetStatusText((Int32)PromotionStatusEnum.Inactive)
                                        </MudSelectItem>
                                    </MudSelect>
                                </MudItem>
                            </MudGrid>
                        </MudStack>
                    </MudItem>
                    <!-- Date Range Section -->
                    <MudItem xs="12">
                        <MudStack Spacing="3">
                            <MudGrid Spacing="3">
                                <MudItem xs="12" md="6">
                                    <MudDatePicker Label="@L["Start Date"]"
                                                   @bind-Date="StartDateForPicker"
                                                   Required
                                                   RequiredError="@L["Start date is required"]"
                                                   Variant="Variant.Outlined"
                                                   FullWidth/>
                                </MudItem>
                                <MudItem xs="12" md="6">
                                    <MudDatePicker Label="@L["End Date"]"
                                                   @bind-Date="EndDateForPicker"
                                                   Required
                                                   RequiredError="@L["End date is required"]"
                                                   MinDate="StartDateForPicker"
                                                   Variant="Variant.Outlined"
                                                   FullWidth/>
                                </MudItem>
                            </MudGrid>
                        </MudStack>
                    </MudItem>
                    <!-- Configuration Section -->
                    <MudItem xs="12">
                        <MudStack Spacing="3">
                            <MudGrid Spacing="3">
                                <MudItem xs="12">
                                    <MudTextField Label="@L["Description"]"
                                                  @bind-Value="CreatePromotionHeaderDto.Description"
                                                  Lines="5"
                                                  Variant="Variant.Outlined"
                                                  FullWidth/>
                                </MudItem>
                            </MudGrid>
                        </MudStack>
                    </MudItem>
                </MudGrid>
            </MudForm>
        </MudContainer>
    </DialogContent>
    <DialogActions>
        <MudButton Variant="Variant.Text"
                   StartIcon="@Icons.Material.Filled.Cancel"
                   OnClick="Cancel"
                   Color="Color.Error"
                   Class="mr-3">
            @L["Cancel"]
        </MudButton>
        <MudButton Variant="Variant.Filled"
                   Color="Color.Primary"
                   StartIcon="@Icons.Material.Filled.Save"
                   OnClick="SavePromotion"
                   Disabled="IsLoading">
            @if (IsLoading)
            {
                <MudProgressCircular Size="Size.Small" Indeterminate Class="me-2"/>
            }
            @L["Save"]
        </MudButton>
    </DialogActions>
</MudDialog>

@if (IsLoading)
{
    <MudOverlay Visible Absolute>
        <MudProgressCircular Size="Size.Large" Indeterminate/>
    </MudOverlay>
}
