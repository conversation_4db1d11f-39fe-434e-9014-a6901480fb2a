using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Server.Services.Promotions.Interface;

public interface IFrontMarginCalculationService
{
    /// <summary>
    ///     Calculate Front Margin discount for a PO line
    /// </summary>
    Task<FrontMarginCalculationResult>
        CalculateLineDiscountAsync(POLineGetDto poLine, List<PromotionFrontMargin> applicablePromotions);

    /// <summary>
    ///     Apply Front Margin to entire PO
    /// </summary>
    Task<POFrontMarginResult> ApplyFrontMarginToPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Get applicable promotions for PO line
    /// </summary>
    Task<List<PromotionFrontMargin>> GetApplicablePromotionsAsync(POHeaderGetDto poHeader, POLineGetDto poLine);
}
