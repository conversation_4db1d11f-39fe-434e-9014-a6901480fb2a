﻿using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Models;
namespace PurchaseManager.Infrastructure.Server;

public interface IVendorManager
{
    string Metadata();
    Task<ApiResponse> BuildViewModel(string number);
    Task<ApiResponse> UpdateVendor(GetVendorDto vendorViewModel);
    Task<ApiResponse> ToggleVendorStatus(string vendorNumber);
    Task<ApiResponse> CreateVendor(GetVendorDto vendorViewModel);
    Task<ApiResponse> GetVendor(string name);
    Task<Vendor> GetVendorByNumber(string name);
    IQueryable<Vendor> GetVendors(VendorFilter filter, string? query);
    Task<ApiResponse> SearchAutocomplete(String number, String name, String queryString, CancellationToken cancellationToken = default);
}
