﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using PurchaseManager.Constants;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.PurchaseOrder.Interface;
using PurchaseManager.Server.Services.PurchaseOrder.Validation;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Services.PurchaseOrder;

public class HeaderServices : IHeaderServices
{
    private readonly IPOHeaderManager _poHeaderManager;
    private readonly IPOLineManager _poLineManager;
    private readonly HeaderValidation _headerValidation;
    private readonly IStringLocalizer<Global> _i18N;
    private readonly IAdminManager _adminManager;
    private readonly IVendorManager _vendorManager;
    private readonly ApplicationDbContext _context;

    public HeaderServices(IPOHeaderManager poHeaderManager, IAdminManager adminManager, IVendorManager vendorManager,
        ApplicationDbContext context, HeaderValidation headerValidation, IStringLocalizer<Global> i18N,
        IPOLineManager poLineManager)
    {
        _poHeaderManager = poHeaderManager;
        _adminManager = adminManager;
        _vendorManager = vendorManager;
        _context = context;
        _headerValidation = headerValidation;
        _i18N = i18N;
        _poLineManager = poLineManager;
    }
    public async Task<ApiResponse> CreateHeader(string vendorNumber, string contactNumber)
    {
        try
        {
            var vendorName = string.Empty;
            var vendorAddress = string.Empty;
            var vendorCity = string.Empty;
            var loginId = _adminManager.GetUserLogin();
            if (loginId is null)
            {
                return new ApiResponse(Status400BadRequest, "LoginID is null (must Login)");
            }
            if (!string.IsNullOrEmpty(vendorNumber))
            {
                var vendorInfo = await _poHeaderManager.ValidateVendorBeforePOCreationAsync(vendorNumber);
                if (!vendorInfo.IsSuccessStatusCode)
                {
                    return vendorInfo;
                }
                var vendor = await _vendorManager.GetVendorByNumber(vendorNumber);
                vendorName = vendor.Name ?? string.Empty;
                vendorAddress = vendor.Address ?? string.Empty;
                vendorCity = vendor.City ?? string.Empty;

            }
            else
            {
                var contact = _context.Contacts.Include(x => x.VendorNumberNavigation).AsNoTracking()
                    .FirstOrDefault(x => x.Number == contactNumber);

                if (contact != null)
                {
                    var vendor = contact.VendorNumberNavigation;
                    vendorName = vendor.Name ?? string.Empty;
                    vendorAddress = vendor.Address ?? string.Empty;
                    vendorCity = vendor.City ?? string.Empty;
                    vendorNumber = vendor.Number;
                }
            }
            var headerNumber = await _adminManager.CreateNumberSeries("PO", "AL");

            var headerToCreate = new CreatePOHeaderDto
            {
                Number = headerNumber,
                BuyFromVendorNumber = vendorNumber,
                PayToCode = contactNumber,
                PurchaserCode = _adminManager.GetUserLogin(),
                DocumentTime = DateTime.Now,
                BeginUsingTime = null,
                BuyFromVendorName = vendorName,
                BuyFromAddress = vendorAddress,
                BuyFromCity = vendorCity,
                BuyFromContact = contactNumber,
                LoginId = _adminManager.GetUserLogin()
            };

            return await _poHeaderManager.CreateHeaderAsync(headerToCreate);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> UpdateHeader(UpdatePOHeaderDto updatePOHeader)
    {
        try
        {
            var headerData = await _poHeaderManager.HeaderNumberValidAsync(updatePOHeader.Number, "update");
            if (!headerData.IsSuccessStatusCode)
            {
                return headerData;
            }
            var headerValidationDto = new HeaderValidationDto
            {
                VendorApprovalBy = updatePOHeader.VendorApprovalBy,
                PurchaserApprovalBy = updatePOHeader.PurchaserApprovalBy,
                PurchaseUser = updatePOHeader.PurchaseUser,
                VendorNumber = updatePOHeader.VendorNo
            };
            var validation = await _headerValidation.ValidationHeader(headerValidationDto);

            if (!validation.IsSuccessStatusCode)
            {
                return validation;
            }

            var header = await _poHeaderManager.UpdateHeaderAsync(updatePOHeader);

            return header;
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> OpenDocument(string documentNumber)
    {
        try
        {
            var loginId = _adminManager.GetUserLogin();
            if (loginId is null)
            {
                return new ApiResponse(Status400BadRequest, _i18N["LoginID is null (must Login)"]);
            }
            if (string.IsNullOrEmpty(documentNumber))
            {
                return ApiResponse.S404(_i18N["DocumentNumber is null"]);
            }

            var notification = await _poHeaderManager.IsDocumentLockedByAnotherUserAsync(documentNumber);
            if (!notification.IsSuccessStatusCode)
            {
                return notification;
            }

            var openDocument = await _poHeaderManager.OpenDocumentAsync(documentNumber);
            return !openDocument.IsSuccessStatusCode
                ? openDocument
                : new ApiResponse(Status200OK, _i18N["Open PO successful"]);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> ChangeDocumentStatus(string documentNumber, PurchaseOrderEnum status)
    {
        try
        {
            var loginId = _adminManager.GetUserLogin();
            if (loginId is null)
            {
                return new ApiResponse(Status400BadRequest, _i18N["LoginID is null (must Login)"]);
            }
            if (string.IsNullOrEmpty(documentNumber))
            {
                return ApiResponse.S404(_i18N["DocumentNumber is null"]);
            }
            var poHeader = await _poHeaderManager.GetHeaderAsync(documentNumber);
            if (!poHeader.IsSuccessStatusCode)
            {
                return poHeader;
            }

            var notification = await _poHeaderManager.IsLineLockedByAnotherUserAsync(documentNumber);
            if (!notification.IsSuccessStatusCode)
            {
                return notification;
            }
            var poLines = await _poLineManager.GetLinesAsync(documentNumber);

            if (!poLines.IsSuccessStatusCode)
            {
                return poLines;
            }
            var confirmHeader = await _poHeaderManager.ChangeDocumentStatusAsync(documentNumber, status);
            return confirmHeader;
        }
        catch (Exception ex)
        {
            return new ApiResponse(Status400BadRequest, ex.GetBaseException().Message);
        }
    }
    public async Task<ApiResponse> POAutoCreate()
    {
        var loginId = _adminManager.GetUserLogin();
        if (loginId is null)
        {
            return ApiResponse.S404(_i18N["LoginID is null (must Login)"]);
        }
        //Create Po in queue
        return await _poHeaderManager.PoAutoCreateAsync(loginId);
    }
}
