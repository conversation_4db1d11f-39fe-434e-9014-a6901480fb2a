using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

[Table("PromotionFrontMargins")]
public class PromotionFrontMargin : FullTrackingEntity
{
    [StringLength(50)]
    public string ProgramNumber { get; set; } = null!;

    public int LineNumber { get; set; }

    [StringLength(50)]
    public string ItemNumber { get; set; } = null!;

    [StringLength(250)]
    public string ItemName { get; set; } = null!;

    [StringLength(50)]
    public string UnitOfMeasure { get; set; } = null!;

    /// <summary>
    ///     - <PERSON><PERSON><PERSON> chiết khấu:
    ///     1 = Percentage
    ///     2 = FixedAmount
    ///     3 = SameItemGift
    ///     4 = DifferentItemGift
    /// </summary>
    public int DiscountType { get; set; } = 1;

    // CASE I.1: Percentage Discount
    [Column(TypeName = "decimal(5,2)")]
    public decimal DiscountPercentage { get; set; }

    // CASE I.2: Fixed Amount Discount
    [Column(TypeName = "decimal(18,2)")]
    public decimal FixedDiscountAmount { get; set; }

    // CASE II: Same Item Gift (Buy X Get Y Free)
    [Column(TypeName = "decimal(18,4)")]
    public decimal BuyQuantity { get; set; }

    [Column(TypeName = "decimal(18,4)")]
    public decimal GiftQuantity { get; set; }

    // CASE III: Different Item Gift
    [StringLength(50)]
    public string? GiftItemNumber { get; set; }

    [StringLength(250)]
    public string? GiftItemName { get; set; }

    [StringLength(50)]
    public string? GiftItemUOM { get; set; }

    [Column(TypeName = "decimal(18,4)")]
    public decimal GiftItemQuantity { get; set; }

    // Conditions
    [Column(TypeName = "decimal(18,4)")]
    public decimal MinimumQuantity { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal MinimumAmount { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal MaximumDiscountAmount { get; set; }

    /// <summary>
    ///     1 = Active
    ///     2 = Inactive
    /// </summary>
    public int Status { get; set; } = 1;

    [StringLength(500)]
    public string? Notes { get; set; }

    [ForeignKey("ProgramNumber")]
    public PromotionHeader PromotionHeader { get; set; } = null!;
}
