using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Interfaces;
namespace PurchaseManager.Theme.Material.Demo.Pages.Promotions.Services;

public class FrontMarginServiceHandler
{
    private readonly IPromotionApiClient _promotionApiClient;
    private readonly IViewNotifier _viewNotifier;
    public FrontMarginServiceHandler(IPromotionApiClient promotionApiClient, IViewNotifier viewNotifier)
    {
        _promotionApiClient = promotionApiClient;
        _viewNotifier = viewNotifier;
    }
    /// <summary>
    /// Promotion header and Front margin 
    /// </summary>
    /// <param name="promotionNumber"></param>
    /// <returns></returns>
    public async Task<GetPromotionHeaderDto> GetPromotionHeaderAsync(string promotionNumber)
    {
        try
        {
            if (string.IsNullOrEmpty(promotionNumber))
            {
                _viewNotifier.Show("Not Found", ViewNotifierType.Error);
                return new GetPromotionHeaderDto();
            }
            var promotionHeaderResponse = await _promotionApiClient.GetPromotionHeaderByNumberAsync(promotionNumber);
            if (promotionHeaderResponse.IsSuccessStatusCode)
            {
                return promotionHeaderResponse.Result;
            }
            _viewNotifier.Show(promotionHeaderResponse.Message, ViewNotifierType.Error);
            return new GetPromotionHeaderDto();
        }
        catch (Exception ex)
        {
            _viewNotifier.Show(ex.Message, ViewNotifierType.Error);
            return new GetPromotionHeaderDto();
        }
    }
}
