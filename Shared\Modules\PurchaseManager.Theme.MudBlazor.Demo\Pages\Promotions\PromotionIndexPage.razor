@page "/promotions"
@using PurchaseManager.Constants.Enum
@using PurchaseManager.Shared.Dto.Promotions
@inherits PromotionIndex;
@if (IsLoading)
{
    <LoadingBackground>
        <label>@L["Loading"]</label>
    </LoadingBackground>
}
else
{
    <MudToolBar Gutters="false" Class="mb-2">
    <MudText Typo="Typo.h6"><PERSON><PERSON> sách chương trình khuyến mãi</MudText>
    <MudSpacer/>
</MudToolBar>
<MudTable ServerData="@(new Func<TableState, CancellationToken, Task<TableData<GetPromotionHeaderDto>>>(ServerReload))"
          Items="@PromotionsList" T="GetPromotionHeaderDto" Bordered="false" @ref="Table" Outlined
          SelectOnRowClick="false" Dense Hover Elevation="0" FixedHeader Height="630px"
          HorizontalScrollbar="false" MultiSelection="true" LoadingProgressColor="Color.Primary"
          @bind-SelectedItems="SelectedPromotions">
    <ToolBarContent>
        <MudGrid Spacing="12" Class="mb-2">
            <MudItem sm="2">
                <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Center" Style="height: 100%">
                    <MudButton
                            Variant="Variant.Filled"
                            Color="Color.Primary"
                            StartIcon="@Icons.Material.Filled.Add"
                            OnClick="OnCreatePromotion">
                        Create
                    </MudButton>
                    @if (SelectedPromotions?.Count > 0)
                    {
                        <MudBadge Content="@SelectedPromotions.Count" Color="Color.Primary" Overlap="true">
                            <MudButton
                                    Variant="Variant.Filled"
                                    Color="Color.Error"
                                    StartIcon="@Icons.Material.Filled.Delete"
                                    OnClick="OnDeleteSelectedPromotions">
                                Delete
                            </MudButton>
                        </MudBadge>
                    }
                    else
                    {
                        <MudButton
                                StartIcon="@Icons.Material.Filled.Refresh"
                                Color="Color.Primary"
                                Variant="Variant.Outlined"
                                OnClick="OnRefreshData">
                            Refresh
                        </MudButton>
                    }
                </MudStack>
            </MudItem>
            <MudItem sm="3">
                <MudAutocomplete T="GetVendorDto" ShrinkLabel Clearable ListItemClass="m-0" ResetValueOnEmptyText
                                 Label="Tìm theo nhà cung cấp" ShowProgressIndicator Value="@VendorFilter"
                                 ValueChanged="@OnSearchByVendor"
                                 ToStringFunc="@(dto => dto.Number is null ? L["ALL"] : string.Concat(dto.Number, " - ", dto.Name))"
                                 SearchFunc="@VendorSearch" Margin="Margin.Dense">
                    <ProgressIndicatorInPopoverTemplate>
                        <MudList T="String" ReadOnly>
                            <MudListItem>
                                Loading...
                            </MudListItem>
                        </MudList>
                    </ProgressIndicatorInPopoverTemplate>
                    <ItemTemplate Context="e">
                        <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween">
                            <MudText>@e.Name</MudText>
                            <MudChip Size="Size.Small" Color="@(e.Blocked == 1 ? Color.Default : Color.Success)"
                                     T="String">@(e.Blocked == 1 ? "Block" : "Active")</MudChip>
                        </MudStack>
                        <MudText Typo="Typo.body2"><b>Number: </b>@e.Number</MudText>
                    </ItemTemplate>
                </MudAutocomplete>
            </MudItem>
            <MudItem sm="2">
                <MudTextField T="String" ShrinkLabel Variant="Variant.Text" Margin="Margin.Dense"
                              Value="@ProgramCodeQuery" Clearable
                              Label="Program Code"
                              ValueChanged="@OnSearchByProgramCode"/>
            </MudItem>
            <MudItem sm="3">
                <MudTextField T="String" ShrinkLabel Variant="Variant.Text" Margin="Margin.Dense"
                              Value="@DescriptionQuery" Clearable
                              Label="Description"
                              ValueChanged="@OnSearchByDescription"/>
            </MudItem>
            <MudItem sm="2">
                <MudSelect Clearable T="Int32" Label="Status" Margin="Margin.Dense"
                           Value="@SelectedStatus" OnClearButtonClick="@OnClearStatusFilter"
                           ValueChanged="@OnSearchByStatus">
                    <MudSelectItem Value="99">Tất cả</MudSelectItem>
                    <MudSelectItem Value="1">Draft</MudSelectItem>
                    <MudSelectItem Value="2">Active</MudSelectItem>
                    <MudSelectItem Value="3">Inactive</MudSelectItem>
                    <MudSelectItem Value="4">Expired</MudSelectItem>
                </MudSelect>
            </MudItem>
        </MudGrid>
    </ToolBarContent>
    <HeaderContent>
        <MudTh Style="width: 100px;">@L[nameof(GetPromotionHeaderDto.ProgramCode)]</MudTh>
        <MudTh>@L[nameof(GetPromotionHeaderDto.ProgramName)]</MudTh>
        <MudTh Style="width: 350px">@L["Vendor"]</MudTh>
        <MudTh Style="text-align: center;">@L[nameof(GetPromotionHeaderDto.Status)]</MudTh>
        <MudTh Style="text-align: center; width: 200px">@L[nameof(GetPromotionHeaderDto.ProgramType)]</MudTh>
        <MudTh>@L["DateRange"]</MudTh>
        <MudTh>@L[nameof(GetPromotionHeaderDto.Description)]</MudTh>
    </HeaderContent>
    <RowTemplate Context="row">
        <MudTd DataLabel="Program Code">
            <MudText Align="Align.Center">
                <MudLink Href="@NavigateToDetails(row)">
                    @row.ProgramCode
                </MudLink>
            </MudText>
        </MudTd>
        <MudTd DataLabel="Program Name">
            <MudText>@row.ProgramName</MudText>
        </MudTd>
        <MudTd DataLabel="Vendor">
            <MudText Typo="Typo.body1">
                @row.VendorName
            </MudText>
            <MudText Typo="Typo.caption">
                <b>Number:</b>
                @row.VendorCode
            </MudText>
        </MudTd>
        <MudTd DataLabel="Status">
            <MudText Style="max-width:200px; overflow: hidden;">
                @switch (row.Status)
                {
                    case (Int32)PromotionStatusEnum.Draft:
                        <MudChip Style="width: 103px" T="String" Icon="@Icons.Material.Filled.ListAlt"
                                 Color="Color.Default">Draft
                        </MudChip>
                        break;
                    case (Int32)PromotionStatusEnum.Active:
                        <MudChip Style="width: 103px" T="String" Icon="@Icons.Material.Filled.CheckCircle"
                                 Color="Color.Success">Active
                        </MudChip>
                        break;
                    case (Int32)PromotionStatusEnum.Inactive:
                        <MudChip Style="width: 103px" T="String" Icon="@Icons.Material.Filled.Block"
                                 Color="Color.Warning">Inactive
                        </MudChip>
                        break;
                    case (Int32)PromotionStatusEnum.Expired:
                        <MudChip Style="width: 103px" T="String" Icon="@Icons.Material.Filled.TimerOff"
                                 Color="Color.Error">Expired
                        </MudChip>
                        break;
                }
            </MudText>
        </MudTd>
        <MudTd DataLabel="Status">
            <MudText Style="max-width:200px; overflow: hidden;">
                @switch (row.ProgramType)
                {
                    case (Int32)PromotionProgramTypeEnum.FrontMargin:
                        <MudChip Style="width: 160px" T="String"
                                 Color="Color.Default">Chiết khấu trả trước
                        </MudChip>
                        break;
                    case (Int32)PromotionProgramTypeEnum.BackMargin:
                        <MudChip Style="width: 160px" T="String"
                                 Color="Color.Success">Chiết khấu trả sau
                        </MudChip>
                        break;
                }
            </MudText>
        </MudTd>
        <MudTd DataLabel="Start Date">
            <MudStack Row AlignItems="AlignItems.Center">@row.StartDate.ToString("dd/MM/yyyy")
                <MudIcon Icon="@Icons.Material.Filled.ArrowRightAlt"/>
                @row.EndDate.ToString("dd/MM/yyyy")
            </MudStack>
        </MudTd>
        <MudTd DataLabel="Description">
            @row.Description
        </MudTd>
    </RowTemplate>
    <PagerContent>
        <MudStack AlignItems="AlignItems.End">
            <MudPagination SelectedChanged="PageChanged"
                           Count="@((Table.GetFilteredItemsCount() + Table.RowsPerPage - 1) / Table.RowsPerPage)"
                           class="pa-2">
            </MudPagination>
        </MudStack>
    </PagerContent>
    <NoRecordsContent>
        <MudText>No promotions found</MudText>
    </NoRecordsContent>
</MudTable>

<!-- Create/Edit Promotion Dialog -->
<CreatePromotion @ref="CreatePromotionDialog"
                 IsDialogVisible="@IsShowCreatePromotionDialog"
                 IsDialogVisibleChanged="OnCreatePromotionDialogVisibleChanged"
                 OnPromotionSaved="OnPromotionSaved"/>
}
