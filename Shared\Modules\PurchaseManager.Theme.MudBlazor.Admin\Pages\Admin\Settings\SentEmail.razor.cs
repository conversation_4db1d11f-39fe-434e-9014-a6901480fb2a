using PurchaseManager.Shared.Dto.Email;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Email;
using PurchaseManager.Theme.Material.Shared.Components;
namespace PurchaseManager.Theme.Material.Admin.Pages.Admin.Settings;
public partial class SentEmailBasePage : ItemsTableBase<SentEmailDto>
{
    protected bool isLoading { get; set; }
    protected bool isShowPreview { get; set; }
    protected SentEmailDto currentSentEmail { get; set; }
    protected SentEmailFilter sentEmailFilter { get; set; } = new SentEmailFilter();
    protected DateTime sentDate { get; set; } = DateTime.Now;
    protected List<SentEmailDto> emailTemplates { get; set; } = new List<SentEmailDto>();
    protected override void OnInitialized()
    {
        isLoading = true;
        from = "GetAllSentEmail";
        queryParameters = sentEmailFilter;
        sentEmailFilter.SentAt = sentDate;
        base.OnInitialized();
        isLoading = false;
    }
    protected void ShowPreview(int id)
    {
        currentSentEmail = items.Where(x => x.Id == id).FirstOrDefault();
        if (currentSentEmail == null)
        {
            currentSentEmail = new SentEmailDto();
            currentSentEmail.Body = L["Body not found"];
        }
        isShowPreview = true;
    }
    protected async Task OnSearchByDate(DateTime? date)
    {
        if (date == null) date = DateTime.Now;
        sentEmailFilter.SentAt = date;
        sentDate = (DateTime)date;
        apiClient.ClearEntitiesCache();
        await Reload();
    }
    protected async Task FilterByPONumber(string PONumber)
    {
        if (!string.IsNullOrEmpty(PONumber))
        {
            sentEmailFilter.PoNumber = PONumber;
            apiClient.ClearEntitiesCache();
            await Reload();
        }
    }
    protected async Task FilterByUser(string userName)
    {
        if (!string.IsNullOrEmpty(userName))
        {
            sentEmailFilter.SentBy = userName;
            apiClient.ClearEntitiesCache();
            await Reload();
        }
    }
    protected async Task<IEnumerable<GetVendorDto>> ItemSearch(string value, CancellationToken token)
    {
        var apiResponse = await apiClient.SearchVendorByNameOrNumber(value, value, token);
        if (apiResponse.IsSuccessStatusCode)
        {
            return apiResponse.Result;
        }
        return new List<GetVendorDto>();
    }
    protected async Task OnSearchContactByVendorName(GetVendorDto vendorViewModel)
    {
        if (vendorViewModel is not null)
        {
            sentEmailFilter.VendorNumber = vendorViewModel.Number;
        }
        apiClient.ClearEntitiesCache();
        await Reload();
    }
}
