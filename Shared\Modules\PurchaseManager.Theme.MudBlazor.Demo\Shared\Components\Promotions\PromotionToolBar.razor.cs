using Microsoft.AspNetCore.Components;
namespace PurchaseManager.Theme.Material.Demo.Shared.Components.Promotions;

public partial class PromotionToolBar : ComponentBase
{
    // Additional logic can be added here if needed

    /// <summary>
    ///     Set button states for Front Margin promotion
    /// </summary>
    public void SetFrontMarginState(bool isEdit, int status, bool hasData = true)
    {
        IsEdit = isEdit;
        EmailButtonDisabled = status != 2;// Only active promotions can send email
        PrintButtonDisabled = !hasData;
        ExportButtonDisabled = !hasData;
        StateHasChanged();
    }

    /// <summary>
    ///     Set button states for Back Margin promotion
    /// </summary>
    public void SetBackMarginState(bool isEdit, int status, bool hasData = true)
    {
        IsEdit = isEdit;
        EmailButtonDisabled = status != 2;
        PrintButtonDisabled = !hasData;
        ExportButtonDisabled = !hasData;
        StateHasChanged();
    }

}
