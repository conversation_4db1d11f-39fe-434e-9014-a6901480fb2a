using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;

namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Item Margin Summary - Tổng hợp margin theo sản phẩm
/// </summary>
[Table("ItemMarginSummaries")]
public class ItemMarginSummary : FullTrackingEntity
{
    /// <summary>
    /// Mã sản phẩm
    /// </summary>
    [Required]
    [StringLength(50)]
    public string ItemNumber { get; set; } = string.Empty;

    /// <summary>
    /// Tên sản phẩm
    /// </summary>
    [StringLength(250)]
    public string ItemName { get; set; } = string.Empty;

    /// <summary>
    /// Đơn vị tính
    /// </summary>
    [StringLength(50)]
    public string UnitOfMeasure { get; set; } = string.Empty;

    /// <summary>
    /// Mã nhà cung cấp
    /// </summary>
    [Required]
    [StringLength(20)]
    public string VendorCode { get; set; } = string.Empty;

    /// <summary>
    /// Tên nhà cung cấp
    /// </summary>
    [StringLength(250)]
    public string VendorName { get; set; } = string.Empty;

    /// <summary>
    /// Chu kỳ báo cáo (YYYY-MM)
    /// </summary>
    [Required]
    [StringLength(20)]
    public string ReportPeriod { get; set; } = string.Empty;

    /// <summary>
    /// Ngày bắt đầu chu kỳ
    /// </summary>
    [Required]
    public DateTime PeriodStartDate { get; set; }

    /// <summary>
    /// Ngày kết thúc chu kỳ
    /// </summary>
    [Required]
    public DateTime PeriodEndDate { get; set; }

    // PURCHASE DATA (Dữ liệu mua hàng)
    /// <summary>
    /// Số lượng đã mua trong kỳ
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal PurchaseQuantity { get; set; }

    /// <summary>
    /// Nguyên giá bình quân (giá gốc từ vendor)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal AverageOriginalPrice { get; set; }

    /// <summary>
    /// Giá mua thực tế bình quân (đã trừ Front Margin discount)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal AveragePurchasePrice { get; set; }

    // SALES DATA (Dữ liệu bán hàng - từ hệ thống khác hoặc price list)
    /// <summary>
    /// Số lượng đã bán trong kỳ (có thể khác với số lượng mua)
    /// </summary>
    [Column(TypeName = "decimal(18,4)")]
    public decimal SalesQuantity { get; set; }

    /// <summary>
    /// Giá bán bình quân (từ hệ thống bán hàng hoặc price list)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal AverageSalesPrice { get; set; }

    // MARGIN CALCULATIONS
    /// <summary>
    /// Front Margin đã nhận = (Nguyên giá - Giá mua thực tế) * SL mua
    /// Đây là chiết khấu đã được áp dụng khi mua hàng
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalFrontMarginReceived { get; set; }

    /// <summary>
    /// Potential Profit = (Giá bán - Giá mua thực tế) * SL bán
    /// Lợi nhuận tiềm năng nếu bán hết với giá bán hiện tại
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal PotentialProfit { get; set; }

    /// <summary>
    /// Back Margin đã nhận = Back Margin/đơn vị * SL mua
    /// Thưởng đã nhận từ việc mua hàng
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalBackMarginReceived { get; set; }

    /// <summary>
    /// Back Margin trên đơn vị mua
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal BackMarginPerUnit { get; set; }

    /// <summary>
    /// Tổng lợi ích = Front Margin + Back Margin + Potential Profit
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalBenefit { get; set; }

    /// <summary>
    /// % Tổng lợi ích = Tổng lợi ích / (Giá bán * SL bán)
    /// </summary>
    [Column(TypeName = "decimal(5,2)")]
    public decimal TotalBenefitPercentage { get; set; }

    /// <summary>
    /// Tổng chi phí mua = Giá mua thực tế * SL mua
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalPurchaseCost { get; set; }

    /// <summary>
    /// Tổng doanh thu tiềm năng = Giá bán * SL bán
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal PotentialRevenue { get; set; }

    /// <summary>
    /// Ngày tính toán
    /// </summary>
    public DateTime CalculationDate { get; set; } = DateTime.Now;

    /// <summary>
    /// Người tính toán
    /// </summary>
    [StringLength(100)]
    public string? CalculatedBy { get; set; }

    /// <summary>
    /// Ghi chú
    /// </summary>
    [StringLength(1000)]
    public string? Notes { get; set; }
}
