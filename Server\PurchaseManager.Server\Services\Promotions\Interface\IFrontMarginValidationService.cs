using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Server.Services.Promotions.Interface;

public interface IFrontMarginValidationService
{
    /// <summary>
    ///     Validate Front Margin entity
    /// </summary>
    List<string> ValidateEntity(PromotionFrontMargin entity);

    /// <summary>
    ///     Validate Front Margin DTO
    /// </summary>
    List<string> ValidateDto(CreatePromotionFrontMarginDto dto);

    /// <summary>
    ///     Validate Front Margin update DTO
    /// </summary>
    List<string> ValidateUpdateDto(UpdatePromotionFrontMarginDto dto);
}
