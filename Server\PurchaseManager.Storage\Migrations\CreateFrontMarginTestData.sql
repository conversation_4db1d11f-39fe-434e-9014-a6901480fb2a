-- =============================================
-- Create Front Margin Test Data
-- Execute this to create comprehensive test data for all 4 discount types
-- Date: 2025-01-11
-- =============================================

USE [PurchaseManager] -- Change to your database name
GO

PRINT 'Creating Front Margin Test Data...'
PRINT 'Database: ' + DB_NAME()
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
PRINT '============================================='

-- Check if migration is complete
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'DiscountType')
BEGIN
    PRINT 'ERROR: Front Margin migration not completed. Please run migration first.'
    RETURN
END

BEGIN TRY
    BEGIN TRANSACTION

    -- Clean up existing test data
    DELETE FROM PromotionFrontMargins WHERE ProgramNumber LIKE 'TEST_FM_%'
    DELETE FROM PromotionHeaders WHERE ProgramNumber LIKE 'TEST_FM_%'
    PRINT 'Cleaned up existing test data'

    -- Create test promotion headers
    INSERT INTO PromotionHeaders (
        ProgramNumber, ProgramName, Description, VendorCode, ProgramType,
        StartDate, EndDate, Status, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES
    ('TEST_FM_2025_001', 'Test Front Margin Program 2025 - Type 1', 'Test program for percentage discount', 'VENDOR001', 1, '2025-01-01', '2025-12-31', 2, 'SYSTEM', GETDATE(), 1),
    ('TEST_FM_2025_002', 'Test Front Margin Program 2025 - Type 2', 'Test program for fixed amount discount', 'VENDOR002', 1, '2025-01-01', '2025-12-31', 2, 'SYSTEM', GETDATE(), 1),
    ('TEST_FM_2025_003', 'Test Front Margin Program 2025 - Type 3', 'Test program for same item gift', 'VENDOR003', 1, '2025-01-01', '2025-12-31', 2, 'SYSTEM', GETDATE(), 1),
    ('TEST_FM_2025_004', 'Test Front Margin Program 2025 - Type 4', 'Test program for different item gift', 'VENDOR004', 1, '2025-01-01', '2025-12-31', 2, 'SYSTEM', GETDATE(), 1)

    PRINT 'Created test promotion headers'

    -- Generate unique numbers for test data
    DECLARE @BaseNumber VARCHAR(20) = 'FM' + FORMAT(GETDATE(), 'yyyyMMdd')
    DECLARE @Counter INT = 1

    -- TYPE 1: Percentage Discount Test Data
    INSERT INTO PromotionFrontMargins (
        Number, ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, MinimumQuantity, MinimumAmount,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES
    (@BaseNumber + FORMAT(@Counter, '0000'), 'TEST_FM_2025_001', 1, 'ITEM001', 'Paracetamol 500mg', 'Viên', 1, 10.00, 100, 1000000, 1, 'Test 10% discount, min 100 units, min 1M VND', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 1, '0000'), 'TEST_FM_2025_001', 2, 'ITEM002', 'Amoxicillin 250mg', 'Viên', 1, 15.50, 50, 500000, 1, 'Test 15.5% discount, min 50 units, min 500K VND', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 2, '0000'), 'TEST_FM_2025_001', 3, 'ITEM003', 'Vitamin C 1000mg', 'Viên', 1, 20.00, NULL, 2000000, 1, 'Test 20% discount, no min quantity, min 2M VND', 'SYSTEM', GETDATE(), 1)

    SET @Counter = @Counter + 3
    PRINT 'Created Type 1 (Percentage Discount) test data'

    -- TYPE 2: Fixed Amount Discount Test Data
    INSERT INTO PromotionFrontMargins (
        Number, ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, FixedDiscountAmount, MinimumAmount, MaximumDiscountAmount,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES
    (@BaseNumber + FORMAT(@Counter, '0000'), 'TEST_FM_2025_002', 1, 'ITEM004', 'Insulin Pen', 'Cây', 2, 0, 500000, 5000000, 2000000, 1, 'Test 500K fixed discount, min 5M order, max 2M discount', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 1, '0000'), 'TEST_FM_2025_002', 2, 'ITEM005', 'Blood Glucose Meter', 'Cái', 2, 0, 1000000, 10000000, NULL, 1, 'Test 1M fixed discount, min 10M order, no max limit', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 2, '0000'), 'TEST_FM_2025_002', 3, 'ITEM006', 'Digital Thermometer', 'Cái', 2, 0, 200000, 1000000, 500000, 1, 'Test 200K fixed discount, min 1M order, max 500K discount', 'SYSTEM', GETDATE(), 1)

    SET @Counter = @Counter + 3
    PRINT 'Created Type 2 (Fixed Amount Discount) test data'

    -- TYPE 3: Same Item Gift Test Data
    INSERT INTO PromotionFrontMargins (
        Number, ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, BuyQuantity, GiftQuantity, MinimumQuantity,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES
    (@BaseNumber + FORMAT(@Counter, '0000'), 'TEST_FM_2025_003', 1, 'ITEM007', 'Face Mask N95', 'Cái', 3, 0, 10, 1, 10, 1, 'Test Buy 10 get 1 free, min 10 units', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 1, '0000'), 'TEST_FM_2025_003', 2, 'ITEM008', 'Hand Sanitizer 500ml', 'Chai', 3, 0, 5, 2, 5, 1, 'Test Buy 5 get 2 free, min 5 units', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 2, '0000'), 'TEST_FM_2025_003', 3, 'ITEM009', 'Surgical Gloves', 'Hộp', 3, 0, 20, 5, 20, 1, 'Test Buy 20 get 5 free, min 20 units', 'SYSTEM', GETDATE(), 1)

    SET @Counter = @Counter + 3
    PRINT 'Created Type 3 (Same Item Gift) test data'

    -- TYPE 4: Different Item Gift Test Data
    INSERT INTO PromotionFrontMargins (
        Number, ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, GiftItemNumber, GiftItemName, GiftItemUOM, GiftItemQuantity, MinimumQuantity,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES
    (@BaseNumber + FORMAT(@Counter, '0000'), 'TEST_FM_2025_004', 1, 'ITEM010', 'Multivitamin 30 tablets', 'Hộp', 4, 0, 'GIFT001', 'Vitamin C 500mg (Gift)', 'Viên', 30, 50, 1, 'Test Buy 50 multivitamin get 30 Vitamin C free', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 1, '0000'), 'TEST_FM_2025_004', 2, 'ITEM011', 'Omega-3 Fish Oil', 'Viên', 4, 0, 'GIFT002', 'Vitamin D3 (Gift)', 'Viên', 60, 100, 1, 'Test Buy 100 Omega-3 get 60 Vitamin D3 free', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 2, '0000'), 'TEST_FM_2025_004', 3, 'ITEM012', 'Calcium + Magnesium', 'Viên', 4, 0, 'GIFT003', 'Zinc Supplement (Gift)', 'Viên', 30, 200, 1, 'Test Buy 200 Calcium get 30 Zinc free', 'SYSTEM', GETDATE(), 1)

    PRINT 'Created Type 4 (Different Item Gift) test data'

    -- Create some edge case test data
    INSERT INTO PromotionFrontMargins (
        Number, ProgramNumber, LineNumber, ItemNumber, ItemName, UnitOfMeasure,
        DiscountType, DiscountPercentage, FixedDiscountAmount, BuyQuantity, GiftQuantity,
        MinimumQuantity, MinimumAmount, MaximumDiscountAmount,
        Status, Notes, CreatedBy, CreatedAt, ModificationStatus
    )
    VALUES
    (@BaseNumber + FORMAT(@Counter + 3, '0000'), 'TEST_FM_2025_001', 4, 'ITEM013', 'Edge Case - Max Percentage', 'Viên', 1, 100.00, NULL, NULL, NULL, 1, 100000, 50000, 1, 'Test maximum 100% discount with limits', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 4, '0000'), 'TEST_FM_2025_002', 4, 'ITEM014', 'Edge Case - Large Fixed Amount', 'Hộp', 2, 0, 10000000, NULL, NULL, NULL, 50000000, NULL, 1, 'Test large fixed amount discount', 'SYSTEM', GETDATE(), 1),
    (@BaseNumber + FORMAT(@Counter + 5, '0000'), 'TEST_FM_2025_003', 4, 'ITEM015', 'Edge Case - High Gift Ratio', 'Cái', 3, 0, NULL, 1, 1, 1, NULL, NULL, 1, 'Test Buy 1 get 1 free (50% effective discount)', 'SYSTEM', GETDATE(), 1)

    PRINT 'Created edge case test data'

    COMMIT TRANSACTION
    PRINT 'Test data creation completed successfully!'

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT 'ERROR: Test data creation failed!'
    PRINT 'Error: ' + ERROR_MESSAGE()
    THROW;
END CATCH

-- Display summary
PRINT ''
PRINT '============================================='
PRINT 'Test Data Summary:'

SELECT
    'Promotion Headers' as DataType,
    COUNT(*) as RecordCount
FROM PromotionHeaders
WHERE ProgramNumber LIKE 'TEST_FM_%'

UNION ALL

SELECT
    'Front Margin Lines' as DataType,
    COUNT(*) as RecordCount
FROM PromotionFrontMargins
WHERE ProgramNumber LIKE 'TEST_FM_%'

PRINT ''
PRINT 'Front Margin Lines by Discount Type:'
SELECT
    DiscountType,
    CASE DiscountType
        WHEN 1 THEN 'Percentage Discount'
        WHEN 2 THEN 'Fixed Amount Discount'
        WHEN 3 THEN 'Same Item Gift'
        WHEN 4 THEN 'Different Item Gift'
        ELSE 'Unknown'
    END as DiscountTypeName,
    COUNT(*) as RecordCount
FROM PromotionFrontMargins
WHERE ProgramNumber LIKE 'TEST_FM_%'
GROUP BY DiscountType
ORDER BY DiscountType

PRINT ''
PRINT 'Sample data for each discount type:'
SELECT TOP 1
    ProgramNumber,
    LineNumber,
    ItemNumber,
    ItemName,
    DiscountType,
    DiscountPercentage,
    FixedDiscountAmount,
    BuyQuantity,
    GiftQuantity,
    GiftItemNumber,
    Notes
FROM PromotionFrontMargins
WHERE ProgramNumber LIKE 'TEST_FM_%' AND DiscountType = 1

UNION ALL

SELECT TOP 1
    ProgramNumber,
    LineNumber,
    ItemNumber,
    ItemName,
    DiscountType,
    DiscountPercentage,
    FixedDiscountAmount,
    BuyQuantity,
    GiftQuantity,
    GiftItemNumber,
    Notes
FROM PromotionFrontMargins
WHERE ProgramNumber LIKE 'TEST_FM_%' AND DiscountType = 2

UNION ALL

SELECT TOP 1
    ProgramNumber,
    LineNumber,
    ItemNumber,
    ItemName,
    DiscountType,
    DiscountPercentage,
    FixedDiscountAmount,
    BuyQuantity,
    GiftQuantity,
    GiftItemNumber,
    Notes
FROM PromotionFrontMargins
WHERE ProgramNumber LIKE 'TEST_FM_%' AND DiscountType = 3

UNION ALL

SELECT TOP 1
    ProgramNumber,
    LineNumber,
    ItemNumber,
    ItemName,
    DiscountType,
    DiscountPercentage,
    FixedDiscountAmount,
    BuyQuantity,
    GiftQuantity,
    GiftItemNumber,
    Notes
FROM PromotionFrontMargins
WHERE ProgramNumber LIKE 'TEST_FM_%' AND DiscountType = 4

PRINT 'Front Margin Test Data Creation Completed!'
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
