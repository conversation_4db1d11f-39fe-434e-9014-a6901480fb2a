{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:53414/", "sslPort": 0}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": false, "inspectUri": "{wsProtocol}://{url.hostname}:{url.port}/_framework/debug/ws-proxy?browser={browserInspectUri}", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "Kestrel": {"commandName": "Project", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://localhost:5050/"}, "Https": {"commandName": "Project", "launchBrowser": false, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:53417/"}, "Docker": {"commandName": "<PERSON>er", "launchBrowser": false, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "httpPort": 53415}}}