﻿namespace PurchaseManager.Infrastructure.Storage.DataModels;

public class CJLGrHeader
{
    /// <summary>
    ///     <PERSON><PERSON> chứng từ nhập kho
    /// </summary>
    public string ReceivingNumber { get; set; }

    /// <summary>
    ///     <PERSON><PERSON> chứng từ PO_Line
    /// </summary>
    public string PoNumber { get; set; }

    public DateOnly ReceivingDate { get; set; }

    public string Buyer { get; set; }

    public string Note { get; set; }

    public DateTime CreateAt { get; set; }

    public string CreateBy { get; set; }

    public DateTime? LastUpdateAt { get; set; }

    public string LastUpdateBy { get; set; }

    public int RowId { get; set; }

    public virtual ICollection<CJLGrLine> CjlGrLines { get; set; } = new List<CJLGrLine>();

    public virtual PurchaseOrderHeader PoNumberNavigation { get; set; }
}
