using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Infrastructure.Server;

/// <summary>
///     Manager for integrating Front Margin with PO system
///     Handles the complete workflow of applying Front Margin to Purchase Orders
/// </summary>
public interface IPOFrontMarginIntegrationManager
{
    /// <summary>
    ///     Apply Front Margin to PO during creation/modification
    /// </summary>
    Task<ApiResponse> ApplyFrontMarginToPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Preview Front Margin impact before applying to PO
    /// </summary>
    Task<ApiResponse> PreviewFrontMarginImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Validate PO for Front Margin eligibility
    /// </summary>
    Task<ApiResponse> ValidatePOForFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Remove Front Margin from existing PO
    /// </summary>
    Task<ApiResponse> RemoveFrontMarginFromPOAsync(string poNumber);

    /// <summary>
    ///     Get Front Margin summary for PO
    /// </summary>
    Task<ApiResponse> GetPOFrontMarginSummaryAsync(string poNumber);

    /// <summary>
    ///     Recalculate Front Margin for existing PO
    /// </summary>
    Task<ApiResponse> RecalculateFrontMarginAsync(string poNumber);

    /// <summary>
    ///     Get Front Margin usage statistics for vendor
    /// </summary>
    Task<ApiResponse> GetVendorFrontMarginStatsAsync(string vendorCode, DateTime? fromDate = null, DateTime? toDate = null);

    /// <summary>
    ///     Auto-apply Front Margin to PO based on settings
    /// </summary>
    Task<ApiResponse> AutoApplyFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);
}
