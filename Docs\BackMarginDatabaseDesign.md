# Back Margin Database Design

## Overview

Back Margin system thiết kế dựa trên kinh nghiệm từ Front Margin system, hỗ trợ 5 loại chiết khấu:
1. Sales-based Progressive Discount (<PERSON>ết khấu lũy tiến theo do<PERSON>h số)
2. Sales-based Tiered Discount (<PERSON><PERSON><PERSON> khấu bậc thang theo do<PERSON>h số)
3. Quantity-based Progressive Discount (<PERSON><PERSON><PERSON> khấu lũy tiến theo số lượng)
4. Quantity-based Tiered Discount (<PERSON>ế<PERSON> khấu bậc thang theo số lượng)
5. Early Payment Discount (Chiết khấu thanh toán sớm)

## Database Schema Design

### 1. PromotionBackMargins Table (Main Table)

```sql
CREATE TABLE PromotionBackMargins (
    Number NVARCHAR(50) NOT NULL PRIMARY KEY,
    ProgramNumber NVARCHAR(50) NOT NULL,
    LineNumber INT NOT NULL,
    ItemNumber NVARCHAR(50) NULL, -- NULL for program-level discounts
    ItemName NVARCHAR(250) NULL,
    UnitOfMeasure NVARCHAR(50) NULL,
    
    -- Discount Type: 1=Sales Progressive, 2=Sales Tiered, 3=Qty Progressive, 4=Qty Tiered, 5=Early Payment
    DiscountType INT NOT NULL,
    
    -- Calculation Period
    CalculationPeriod NVARCHAR(20) NOT NULL, -- MONTHLY, QUARTERLY, YEARLY
    PeriodStartDate DATE NULL,
    PeriodEndDate DATE NULL,
    
    -- Payment Terms (for Early Payment Discount)
    StandardPaymentDays INT NULL,
    EarlyPaymentDays INT NULL,
    EarlyPaymentDiscountPercentage DECIMAL(5,2) NULL,
    
    -- Multiple Payment Methods Support
    SupportedPaymentMethods NVARCHAR(500) NULL, -- JSON array: ["CASH", "TRANSFER", "CHECK"]
    
    -- Status and Audit
    Status INT NOT NULL DEFAULT 1, -- 1=Draft, 2=Active, 3=Inactive, 4=Expired
    Notes NVARCHAR(MAX) NULL,
    CreatedBy NVARCHAR(100) NULL,
    CreatedAt DATETIME2 NULL,
    LastModifiedBy NVARCHAR(100) NULL,
    LastModifiedAt DATETIME2 NULL,
    ModificationStatus INT NOT NULL DEFAULT 1, -- 1=Active, 2=Deleted
    
    -- Constraints
    CONSTRAINT FK_PromotionBackMargins_PromotionHeaders 
        FOREIGN KEY (ProgramNumber) REFERENCES PromotionHeaders(ProgramNumber),
    CONSTRAINT CK_PromotionBackMargins_DiscountType 
        CHECK (DiscountType IN (1, 2, 3, 4, 5)),
    CONSTRAINT CK_PromotionBackMargins_CalculationPeriod 
        CHECK (CalculationPeriod IN ('MONTHLY', 'QUARTERLY', 'YEARLY')),
    CONSTRAINT UQ_PromotionBackMargins_ProgramLine 
        UNIQUE (ProgramNumber, LineNumber)
);
```

### 2. PromotionBackMarginTiers Table (Tier Configuration)

```sql
CREATE TABLE PromotionBackMarginTiers (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    BackMarginNumber NVARCHAR(50) NOT NULL,
    TierLevel INT NOT NULL,
    
    -- Threshold Values
    MinimumAmount DECIMAL(18,2) NULL, -- For sales-based discounts
    MaximumAmount DECIMAL(18,2) NULL,
    MinimumQuantity DECIMAL(18,4) NULL, -- For quantity-based discounts
    MaximumQuantity DECIMAL(18,4) NULL,
    
    -- Discount Configuration
    DiscountPercentage DECIMAL(5,2) NOT NULL,
    FixedDiscountAmount DECIMAL(18,2) NULL,
    MaximumDiscountAmount DECIMAL(18,2) NULL,
    
    -- Tier Type: PROGRESSIVE or TIERED
    TierType NVARCHAR(20) NOT NULL,
    
    -- Status
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT FK_PromotionBackMarginTiers_BackMargins 
        FOREIGN KEY (BackMarginNumber) REFERENCES PromotionBackMargins(Number),
    CONSTRAINT CK_PromotionBackMarginTiers_TierType 
        CHECK (TierType IN ('PROGRESSIVE', 'TIERED')),
    CONSTRAINT UQ_PromotionBackMarginTiers_BackMarginTier 
        UNIQUE (BackMarginNumber, TierLevel)
);
```

### 3. PromotionBackMarginCalculations Table (Calculation History)

```sql
CREATE TABLE PromotionBackMarginCalculations (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    BackMarginNumber NVARCHAR(50) NOT NULL,
    VendorCode NVARCHAR(50) NOT NULL,
    CalculationPeriod NVARCHAR(20) NOT NULL,
    PeriodStartDate DATE NOT NULL,
    PeriodEndDate DATE NOT NULL,
    
    -- Purchase Data
    TotalPurchaseAmount DECIMAL(18,2) NOT NULL,
    TotalPurchaseQuantity DECIMAL(18,4) NOT NULL,
    TotalInvoiceCount INT NOT NULL,
    
    -- Payment Data
    AveragePaymentDays DECIMAL(5,2) NULL,
    EarlyPaymentCount INT NULL,
    TotalPaymentCount INT NULL,
    
    -- Calculation Results
    AppliedTierLevel INT NULL,
    DiscountPercentage DECIMAL(5,2) NOT NULL,
    CalculatedDiscountAmount DECIMAL(18,2) NOT NULL,
    FinalDiscountAmount DECIMAL(18,2) NOT NULL, -- After applying maximum limits
    
    -- Calculation Details (JSON)
    CalculationDetails NVARCHAR(MAX) NULL,
    
    -- Status
    CalculationStatus NVARCHAR(20) NOT NULL, -- CALCULATED, APPROVED, PAID
    CalculatedBy NVARCHAR(100) NOT NULL,
    CalculatedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ApprovedBy NVARCHAR(100) NULL,
    ApprovedAt DATETIME2 NULL,
    
    -- Constraints
    CONSTRAINT FK_PromotionBackMarginCalculations_BackMargins 
        FOREIGN KEY (BackMarginNumber) REFERENCES PromotionBackMargins(Number),
    CONSTRAINT CK_PromotionBackMarginCalculations_Status 
        CHECK (CalculationStatus IN ('CALCULATED', 'APPROVED', 'PAID', 'CANCELLED'))
);
```

### 4. PromotionBackMarginPayments Table (Payment Tracking)

```sql
CREATE TABLE PromotionBackMarginPayments (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    CalculationId BIGINT NOT NULL,
    PaymentNumber NVARCHAR(50) NOT NULL,
    VendorCode NVARCHAR(50) NOT NULL,
    
    -- Payment Details
    PaymentAmount DECIMAL(18,2) NOT NULL,
    PaymentMethod NVARCHAR(50) NOT NULL, -- CASH, TRANSFER, CHECK, CREDIT_NOTE
    PaymentDate DATE NOT NULL,
    PaymentReference NVARCHAR(100) NULL,
    
    -- Bank Details (for transfers)
    BankAccount NVARCHAR(50) NULL,
    BankName NVARCHAR(100) NULL,
    TransactionReference NVARCHAR(100) NULL,
    
    -- Status
    PaymentStatus NVARCHAR(20) NOT NULL, -- PENDING, COMPLETED, FAILED, CANCELLED
    ProcessedBy NVARCHAR(100) NOT NULL,
    ProcessedAt DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    
    -- Constraints
    CONSTRAINT FK_PromotionBackMarginPayments_Calculations 
        FOREIGN KEY (CalculationId) REFERENCES PromotionBackMarginCalculations(Id),
    CONSTRAINT CK_PromotionBackMarginPayments_Method 
        CHECK (PaymentMethod IN ('CASH', 'TRANSFER', 'CHECK', 'CREDIT_NOTE')),
    CONSTRAINT CK_PromotionBackMarginPayments_Status 
        CHECK (PaymentStatus IN ('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED'))
);
```

## Indexes for Performance

```sql
-- Main table indexes
CREATE INDEX IX_PromotionBackMargins_DiscountType ON PromotionBackMargins(DiscountType);
CREATE INDEX IX_PromotionBackMargins_Status ON PromotionBackMargins(Status);
CREATE INDEX IX_PromotionBackMargins_CalculationPeriod ON PromotionBackMargins(CalculationPeriod);
CREATE INDEX IX_PromotionBackMargins_ItemNumber ON PromotionBackMargins(ItemNumber);

-- Tier table indexes
CREATE INDEX IX_PromotionBackMarginTiers_BackMarginNumber ON PromotionBackMarginTiers(BackMarginNumber);
CREATE INDEX IX_PromotionBackMarginTiers_TierLevel ON PromotionBackMarginTiers(TierLevel);

-- Calculation table indexes
CREATE INDEX IX_PromotionBackMarginCalculations_VendorCode ON PromotionBackMarginCalculations(VendorCode);
CREATE INDEX IX_PromotionBackMarginCalculations_Period ON PromotionBackMarginCalculations(PeriodStartDate, PeriodEndDate);
CREATE INDEX IX_PromotionBackMarginCalculations_Status ON PromotionBackMarginCalculations(CalculationStatus);

-- Payment table indexes
CREATE INDEX IX_PromotionBackMarginPayments_VendorCode ON PromotionBackMarginPayments(VendorCode);
CREATE INDEX IX_PromotionBackMarginPayments_PaymentDate ON PromotionBackMarginPayments(PaymentDate);
CREATE INDEX IX_PromotionBackMarginPayments_Status ON PromotionBackMarginPayments(PaymentStatus);
```

## Key Design Decisions

### 1. Separation of Concerns
- **PromotionBackMargins**: Main configuration table
- **PromotionBackMarginTiers**: Tier-specific configuration (supports multiple tiers per promotion)
- **PromotionBackMarginCalculations**: Historical calculation results
- **PromotionBackMarginPayments**: Payment tracking and audit trail

### 2. Flexible Tier System
- Supports both Progressive and Tiered discount models
- Multiple tiers per promotion with different thresholds
- Separate minimum/maximum values for amount and quantity

### 3. Comprehensive Calculation Tracking
- Full audit trail of calculations
- Detailed calculation metadata in JSON format
- Support for approval workflow

### 4. Payment Integration
- Multiple payment methods support
- Bank transfer details tracking
- Payment status management

### 5. Performance Considerations
- Strategic indexing for common queries
- Separate tables to avoid large row sizes
- Efficient date range queries for period calculations

## Business Rules Implementation

### 1. Discount Type Validation
```sql
-- Ensure correct tier configuration based on discount type
ALTER TABLE PromotionBackMarginTiers 
ADD CONSTRAINT CK_TierConfiguration 
CHECK (
    (BackMarginNumber IN (
        SELECT Number FROM PromotionBackMargins 
        WHERE DiscountType IN (1,2) -- Sales-based
    ) AND MinimumAmount IS NOT NULL)
    OR
    (BackMarginNumber IN (
        SELECT Number FROM PromotionBackMargins 
        WHERE DiscountType IN (3,4) -- Quantity-based
    ) AND MinimumQuantity IS NOT NULL)
    OR
    (BackMarginNumber IN (
        SELECT Number FROM PromotionBackMargins 
        WHERE DiscountType = 5 -- Early Payment
    ) AND MinimumAmount IS NULL AND MinimumQuantity IS NULL)
);
```

### 2. Period Validation
```sql
-- Ensure period dates are consistent
ALTER TABLE PromotionBackMargins 
ADD CONSTRAINT CK_PeriodDates 
CHECK (PeriodStartDate IS NULL OR PeriodEndDate IS NULL OR PeriodStartDate <= PeriodEndDate);
```

### 3. Early Payment Configuration
```sql
-- Ensure early payment discount has required fields
ALTER TABLE PromotionBackMargins 
ADD CONSTRAINT CK_EarlyPaymentConfig 
CHECK (
    DiscountType != 5 OR (
        StandardPaymentDays IS NOT NULL AND 
        EarlyPaymentDays IS NOT NULL AND 
        EarlyPaymentDiscountPercentage IS NOT NULL AND
        EarlyPaymentDays < StandardPaymentDays
    )
);
```

## Migration Strategy

### Phase 1: Core Tables
1. Create main PromotionBackMargins table
2. Create PromotionBackMarginTiers table
3. Add basic indexes

### Phase 2: Calculation System
1. Create PromotionBackMarginCalculations table
2. Add calculation-related indexes
3. Implement calculation stored procedures

### Phase 3: Payment System
1. Create PromotionBackMarginPayments table
2. Add payment-related indexes
3. Implement payment tracking procedures

### Phase 4: Integration
1. Update PromotionHeaders to support Back Margin (ProgramType = 2)
2. Add foreign key constraints
3. Create views for reporting

## Comparison with Front Margin

| Aspect | Front Margin | Back Margin |
|--------|--------------|-------------|
| **Timing** | Applied at PO creation | Calculated periodically |
| **Complexity** | Simple discount rules | Complex tier calculations |
| **Data Volume** | Low (per PO line) | High (historical calculations) |
| **Performance** | Real-time calculation | Batch processing |
| **Payment** | Immediate discount | Separate payment process |
| **Audit Trail** | Basic | Comprehensive |

## Next Steps

1. **Review and Approve Design**: Stakeholder review of database design
2. **Create Migration Scripts**: Detailed SQL scripts for table creation
3. **Implement Business Logic**: Stored procedures for calculations
4. **Develop APIs**: REST endpoints for Back Margin management
5. **Create UI Components**: User interface for configuration and monitoring
6. **Testing Strategy**: Comprehensive testing plan for all discount types
7. **Performance Optimization**: Indexing and query optimization
8. **Integration Planning**: Integration with existing PO and payment systems

## Estimated Timeline

- **Database Design & Migration**: 1 week
- **Core API Development**: 2 weeks  
- **Calculation Engine**: 2 weeks
- **Payment Integration**: 1 week
- **UI Development**: 2 weeks
- **Testing & Optimization**: 1 week

**Total Estimated Time**: 9 weeks

## Risk Considerations

1. **Data Volume**: Back Margin calculations can generate large amounts of historical data
2. **Performance**: Complex tier calculations may impact system performance
3. **Integration Complexity**: Payment system integration requires careful planning
4. **Business Rule Changes**: Tier configurations may need frequent updates
5. **Audit Requirements**: Comprehensive audit trail increases storage requirements

## Recommendations

1. **Implement Caching**: Cache frequently accessed tier configurations
2. **Batch Processing**: Use background jobs for period calculations
3. **Data Archiving**: Implement data archiving strategy for old calculations
4. **Monitoring**: Add comprehensive monitoring for calculation performance
5. **Backup Strategy**: Ensure robust backup for calculation and payment data
