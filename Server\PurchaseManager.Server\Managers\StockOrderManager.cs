﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Managers;

public class StockOrderManager : IStockOrderManager
{
    private readonly ApplicationDbContext _context;
    private readonly IMapper _mapper;
    private const string Business = "STOCKORDER";
    private const string Branch = "AL";
    private readonly IAdminManager _adminManager;
    public StockOrderManager(ApplicationDbContext context, IMapper mapper, IAdminManager adminManager)
    {
        _context = context;
        _mapper = mapper;
        _adminManager = adminManager;
    }
    public async Task<ApiResponse> GetReceiveLotsByPoHeaderAsync(StockOrderFilter filter)
    {
        try
        {
            var take = filter.PageSize ?? 10;
            var skip = (filter.PageIndex ?? 0) * take;
            var query = _context.StockOrders
                .Where(h =>
                    filter.HeaderNumber == null || h.HeaderNumber.Contains(filter.HeaderNumber));

            var totalRecords = await query.CountAsync();

            var result = await query
                .OrderByDescending(h => h.RowId)
                .Skip(skip)
                .Take(take)
                .ToListAsync();

            var pagedResult = new PagedResultDto<GetStockOrderDto>
            {
                RowCount = totalRecords,
                CurrentPage = filter.PageIndex ?? 0,
                PageSize = filter.PageSize ?? 10,
                Data = _mapper.Map<List<GetStockOrderDto>>(result)
            };

            return ApiResponse.S200(result: pagedResult);
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }
    public async Task<ApiResponse> CreateReceiveLotsAsync(CreateStockOrderDto stockOrders)
    {
        try
        {
            var stockOrderNumber = await _adminManager.CreateNumberSeries(Business, Branch);
            var stockOrder = _mapper.Map<StockOrder>(stockOrders);
            stockOrder.Number = stockOrderNumber;
            stockOrder.CreateBy = _adminManager.GetUserLogin();
            await _context.StockOrders.AddAsync(stockOrder);
            await _context.SaveChangesAsync();
            return ApiResponse.S200();
        }
        catch (Exception e)
        {
            return ApiResponse.S500(e.Message);
        }
    }
}
