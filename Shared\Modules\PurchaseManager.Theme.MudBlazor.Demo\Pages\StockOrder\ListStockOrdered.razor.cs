﻿using System.Net.Http.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Constants;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Extensions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.Account;
using PurchaseManager.Shared.Providers;
using NavigationManagerExtensions=PurchaseManager.Shared.Extensions.NavigationManagerExtensions;
using PurchaseManager.Theme.Material.Demo.Shared.Components.PO;

namespace PurchaseManager.Theme.Material.Demo.Pages.StockOrder;

public partial class ListStockOrdered : ComponentBase
{
    private DateRange DateRange { get; set; } = new DateRange();
    private ListPOByUser ListPOByUserRef { get; set; }
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationStateTask { get; set; }
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    private UserViewModel UserViewModel { get; set; }
    [Inject]
    private HttpClient HttpClient { get; set; }

    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    [Inject]
    private IVendorApiClient VendorApiClient { get; set; }
    private async Task Reload() => await ListPOByUserRef.Reload();
    [Parameter]
    public string VendorNumberParam { get; set; }
    [Parameter]
    public string VendorNameParam { get; set; }
    private int? StatusFilter { get; set; }
    private GetVendorDto VendorFilter { get; set; } = new GetVendorDto();
    private DetailItemDto ItemFilter { get; set; } = new DetailItemDto();
    [Inject]
    private NavigationManager NavigationManager { get; set; }
    private StockOrderFilerEnum _selectedStatus = StockOrderFilerEnum.All;
    private string ItemNumberParam { get; set; }
    private Dictionary<string, string> QueryParams
    {
        get;
    } = [];
    #region Parameter queries
    [Parameter]
    [SupplyParameterFromQuery]
    public DateTime FromDate { get; set; }
    [Parameter]
    [SupplyParameterFromQuery]
    public DateTime ToDate { get; set; }
    [Parameter]
    [SupplyParameterFromQuery(Name = "vendorNumber")]
    public string VendorNumberQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "contactNumber")]
    public string ContactNumberQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "vendorName")]
    public string VendorNameQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "description")]
    public string DescriptionQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "status")]
    public string StatusQuery { get; set; } = string.Empty;
    [Parameter]
    [SupplyParameterFromQuery(Name = "poNumber")]
    public string PONumberQuery { get; set; } = string.Empty;
    #endregion
    protected override void OnInitialized()
    {
        var today = DateTime.Today;
        var month = new DateTime(today.Year, today.Month, 1);
        var last = month.AddMonths(1).AddDays(-1);
        DateRange = new DateRange(month, last);
        base.OnInitialized();
    }

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateTask;
        var user = authState.User;

        var isAdmin = user.IsAdmin();
        var isVendor = user.IsVendor();
        var today = DateTime.Today;
        var month = new DateTime(today.Year, today.Month, 1);
        var last = month.AddMonths(1).AddDays(-1);
        DateRange = new DateRange(month, last);
        UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();

        if (isVendor && !isAdmin)
        {
        }
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        await GetStaticData();
        ItemFilter = new DetailItemDto();
        VendorFilter = new GetVendorDto();
        await base.OnParametersSetAsync();
    }
   
    private void OnSearchByStatus(StockOrderFilerEnum status)
    {
        StatusFilter = (int)status;
        _selectedStatus = status;
        StatusFilter = status switch
        {
            StockOrderFilerEnum.All => null,
            StockOrderFilerEnum.Approve => (int)PurchaseOrderEnum.Approve,
            StockOrderFilerEnum.PartiallyReceived => (int)PurchaseOrderEnum.PartiallyReceived,
            StockOrderFilerEnum.Completed => (int)PurchaseOrderEnum.Completed,
            _ => throw new ArgumentOutOfRangeException(nameof(status), status, null)
        };
        // SetParametersToUrl();
        StateHasChanged();
    }
    private static string GetMarginLabel(StockOrderFilerEnum status) => status switch
    {
        StockOrderFilerEnum.All => "Tất cả",
        StockOrderFilerEnum.Approve => "Đã approve",
        StockOrderFilerEnum.PartiallyReceived => "Kho đã nhận 1 phần",
        StockOrderFilerEnum.Completed => "Kho đã nhận đủ",
        _ => status.ToString()
    };
    protected void OnClearFilterByVendor()
    {
        VendorFilter = new GetVendorDto();
        VendorNumberQuery = "";
        SetParametersToUrl();
        // NavigationManager.NavigateTo("/stock-order");
        StateHasChanged();
    }
    protected void OnClearFilterByApproveStatus()
    {
        StatusFilter = null;
        _selectedStatus = StockOrderFilerEnum.All;
        StateHasChanged();
    }

    protected void SetParametersToUrl()
    {
        var queries = BuildQueryParams();
        NavigationManager.NavigateTo("/stock-order" + queries);
    }
    private string BuildQueryParams()
    {
        QueryParams
            .SetParam("fromDate", FromDate.ToString("yyyy-MM-dd"))
            .SetParam("toDate", ToDate.ToString("yyyy-MM-dd"))
            .SetParam("poNumber", PONumberQuery)
            .SetParam("vendorNumber", VendorNumberQuery)
            .SetParam("itemNumber", ItemNumberParam);
        // .SetParam("status", StatusQuery);

        return NavigationManagerExtensions.ToQueryString(QueryParams);
    }

    protected void OnClearFilterByItemNumber()
    {
        ItemNumberParam = null;
        ItemFilter = null;
        SetParametersToUrl();
        // NavigationManager.NavigateTo("/stock-order");
        StateHasChanged();
    }

    private void OnItemSelectedInAutoComplete(DetailItemDto dto)
    {
        ItemFilter = dto;
        ItemNumberParam = dto.Number;
        SetParametersToUrl();
    }

    protected async Task<IEnumerable<GetVendorDto>> VendorSearch(string value, CancellationToken token)
    {
        var result = new List<GetVendorDto>();

        if (token.IsCancellationRequested)
        {
            return result;
        }

        var allItem = new GetVendorDto
        {
            Number = null, Name = L["ALL"], Blocked = 0
        };

        result.Add(allItem);

        if (string.IsNullOrEmpty(value))
        {
            return result;
        }
        var vendorNumber = value.Trim().Equals(L["ALL"], StringComparison.OrdinalIgnoreCase) ? string.Empty
            : value.Split('-')[0].Trim();

        var apiResponse = await VendorApiClient.GetVendorsByNumber(vendorNumber, token);

        if (!apiResponse.IsSuccessStatusCode || apiResponse.Result?.Any() != true)
        {
            return result;
        }
        foreach (var item in apiResponse.Result.Where(item => result.All(r => r.Number != item.Number)))
        {
            result.Add(item);
        }

        return result;
    }

    protected void OnSearchByVendor(GetVendorDto selectedValue)
    {
        if (selectedValue == null)
        {
            VendorNameQuery = VendorNumberQuery = "";
            VendorFilter = new GetVendorDto();
        }
        else
        {
            VendorFilter = selectedValue;
            VendorNumberQuery = selectedValue.Number;
            VendorNameQuery = selectedValue.Name;
        }
        SetParametersToUrl();
    }

    private async Task<IEnumerable<DetailItemDto>> ItemSearch(string value, CancellationToken token)
    {
        try
        {
            IEnumerable<DetailItemDto> result = new List<DetailItemDto>();

            if (result.Any())
            {
                return result;
            }
            if (string.IsNullOrWhiteSpace(value) || string.IsNullOrEmpty(value))
            {
                value = string.Empty;
            }
            var apiResponse = await HttpClient.GetFromJsonAsync<ApiResponseDto<List<DetailItemDto>>>(
            $"api/data/SearchItem?number={value.Trim()}", token);

            if (!apiResponse.IsSuccessStatusCode)
            {
                return result;
            }

            result = apiResponse.Result;

            return result;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return new List<DetailItemDto>();
        }
    }
    private async Task GetStaticData()
    {
        //date range setting
        var defaultDay = new DateTime();
        var currentDate = DateTime.Now.Date;
        if (FromDate == defaultDay)
        {
            FromDate = new DateTime(currentDate.Year, currentDate.Month, 1);
            DateRange.Start = FromDate;
        }

        if (ToDate == defaultDay)
        {
            ToDate = FromDate.AddMonths(1).AddDays(-1);
            DateRange.End = ToDate;
        }

        UserViewModel = await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
    }
}
