using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Constants.Enum;

namespace PurchaseManager.Infrastructure.Storage.Extensions;

public static class PromotionFrontMarginExtensions
{
    /// <summary>
    /// Check if promotion is percentage discount type
    /// </summary>
    public static bool IsPercentageDiscount(this PromotionFrontMargin promotion)
    {
        return promotion.DiscountType == (int)FrontMarginCalculationType.PercentageDiscount;
    }

    /// <summary>
    /// Check if promotion is fixed amount discount type
    /// </summary>
    public static bool IsFixedAmountDiscount(this PromotionFrontMargin promotion)
    {
        return promotion.DiscountType == (int)FrontMarginCalculationType.FixedAmountDiscount;
    }

    /// <summary>
    /// Check if promotion is same item gift type
    /// </summary>
    public static bool IsSameItemGift(this PromotionFrontMargin promotion)
    {
        return promotion.DiscountType == (int)FrontMarginCalculationType.SameItemGift;
    }

    /// <summary>
    /// Check if promotion is different item gift type
    /// </summary>
    public static bool IsDifferentItemGift(this PromotionFrontMargin promotion)
    {
        return promotion.DiscountType == (int)FrontMarginCalculationType.DifferentItemGift;
    }

    /// <summary>
    /// Check if promotion has valid configuration
    /// </summary>
    public static bool HasValidConfiguration(this PromotionFrontMargin promotion)
    {
        return promotion.DiscountType switch
        {
            1 => promotion.DiscountPercentage > 0 && promotion.DiscountPercentage <= 100,
            2 => promotion.FixedDiscountAmount > 0 && promotion.FixedDiscountAmount > 0,
            3 => promotion.BuyQuantity > 0 && promotion.BuyQuantity > 0 &&
                 promotion.GiftQuantity> 0 && promotion.GiftQuantity > 0,
            4 => !string.IsNullOrEmpty(promotion.GiftItemNumber) &&
                 !string.IsNullOrEmpty(promotion.GiftItemName) &&
                 !string.IsNullOrEmpty(promotion.GiftItemUOM) &&
                 promotion.GiftItemQuantity > 0 && promotion.GiftItemQuantity > 0,
            _ => false
        };
    }

    /// <summary>
    /// Get promotion type name
    /// </summary>
    public static string GetDiscountTypeName(this PromotionFrontMargin promotion)
    {
        return promotion.DiscountType switch
        {
            1 => "Chiết khấu theo phần trăm",
            2 => "Chiết khấu số tiền cố định",
            3 => "Mua hàng tặng hàng cùng loại",
            4 => "Tặng hàng khác loại",
            _ => "Không xác định"
        };
    }

    /// <summary>
    /// Get promotion display text
    /// </summary>
    public static string GetDisplayText(this PromotionFrontMargin promotion)
    {
        return promotion.DiscountType switch
        {
            1 => $"Chiết khấu {promotion.DiscountPercentage:F2}%",
            2 => $"Chiết khấu {promotion.FixedDiscountAmount:N0} VNĐ",
            3 => $"Mua {promotion.BuyQuantity:F0} tặng {promotion.GiftQuantity:F0}",
            4 => $"Tặng {promotion.GiftItemQuantity:F0} {promotion.GiftItemUOM} {promotion.GiftItemName}",
            _ => "Chưa cấu hình"
        };
    }

    /// <summary>
    /// Check if promotion meets minimum quantity requirement
    /// </summary>
    public static bool MeetsMinimumQuantity(this PromotionFrontMargin promotion, decimal quantity)
    {
        return !promotion.MinimumQuantity.HasValue || quantity >= promotion.MinimumQuantity;
    }

    /// <summary>
    /// Check if promotion meets minimum amount requirement
    /// </summary>
    public static bool MeetsMinimumAmount(this PromotionFrontMargin promotion, decimal amount)
    {
        return !promotion.MinimumAmount.HasValue || amount >= promotion.MinimumAmount.Value;
    }

    /// <summary>
    /// Check if promotion is active
    /// </summary>
    public static bool IsActive(this PromotionFrontMargin promotion)
    {
        return promotion.Status == 1;
    }

    /// <summary>
    /// Check if promotion is applicable for given quantity and amount
    /// </summary>
    public static bool IsApplicable(this PromotionFrontMargin promotion, decimal quantity, decimal amount)
    {
        return promotion.IsActive() &&
               promotion.HasValidConfiguration() &&
               promotion.MeetsMinimumQuantity(quantity) &&
               promotion.MeetsMinimumAmount(amount);
    }

    /// <summary>
    /// Calculate gift quantity for same item gift promotion
    /// </summary>
    public static decimal CalculateGiftQuantity(this PromotionFrontMargin promotion, decimal orderQuantity)
    {
        if (!promotion.IsSameItemGift() || !promotion.BuyQuantity.HasValue || !promotion.GiftQuantity.HasValue)
            return 0;

        var giftSets = Math.Floor(orderQuantity / promotion.BuyQuantity.Value);
        return giftSets * promotion.GiftQuantity.Value;
    }

    /// <summary>
    /// Calculate gift quantity for different item gift promotion
    /// </summary>
    public static decimal CalculateDifferentItemGiftQuantity(this PromotionFrontMargin promotion, decimal orderQuantity)
    {
        if (!promotion.IsDifferentItemGift() || !promotion.BuyQuantity.HasValue || !promotion.GiftItemQuantity.HasValue)
            return 0;

        var giftSets = Math.Floor(orderQuantity / promotion.BuyQuantity.Value);
        return giftSets * promotion.GiftItemQuantity.Value;
    }
}
