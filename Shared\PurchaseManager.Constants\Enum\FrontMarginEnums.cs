namespace PurchaseManager.Constants.Enum;

/// <summary>
///     Front Margin Calculation Types
/// </summary>
public enum FrontMarginCalculationType
{
    /// <summary>
    ///     Chiết khấu theo % trên từng sản phẩm
    ///     Gi<PERSON> = Nguyên giá × (1 - % chiết khấu)
    /// </summary>
    PercentageDiscount = 1,

    /// <summary>
    ///     Chiết khấu theo số tiền cố định cho đơn hàng
    ///     Giá Mua = Nguyên giá - (Tổng tiền CK / Tổng giá trị ĐH × Giá trị item)
    /// </summary>
    FixedAmountDiscount = 2,

    /// <summary>
    ///     Chiết khấu bằng hàng - Cùng loại (Buy X Get Y Free)
    ///     Giá Mua = Tổng giá trị theo mã hàng / Tổng số lượng theo mã hàng
    /// </summary>
    SameItemGift = 3,

    /// <summary>
    ///     Chiết khấu bằng hàng - <PERSON><PERSON> lo<PERSON> (Buy X Get Different Y)
    ///     Tặng hàng khác loại (không ảnh hưởng giá mua)
    /// </summary>
    DifferentItemGift = 4
}
