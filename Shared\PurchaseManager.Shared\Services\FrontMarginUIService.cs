using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Dto.PO;
using System.Net.Http.Json;

namespace PurchaseManager.Shared.Services;

/// <summary>
/// Service for Front Margin integration in UI components
/// </summary>
public interface IFrontMarginUIService
{
    /// <summary>
    /// Check if vendor has active Front Margin promotions
    /// </summary>
    Task<bool> HasActiveFrontMarginAsync(string vendorCode);

    /// <summary>
    /// Get Front Margin discount for PO line
    /// </summary>
    Task<FrontMarginDiscountInfo?> GetLineDiscountAsync(string vendorCode, string itemNumber, DateTime orderDate);

    /// <summary>
    /// Get all applicable Front Margin promotions for vendor
    /// </summary>
    Task<List<FrontMarginPromotionInfo>> GetApplicablePromotionsAsync(string vendorCode, DateTime orderDate);

    /// <summary>
    /// Preview Front Margin impact on entire PO
    /// </summary>
    Task<FrontMarginPreviewResult> PreviewPOImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    /// Apply Front Margin to PO lines (for UI preview)
    /// </summary>
    Task<List<POLineGetDto>> ApplyFrontMarginToLinesAsync(List<POLineGetDto> lines, string vendorCode, DateTime orderDate);
}

public class FrontMarginUIService : IFrontMarginUIService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<FrontMarginUIService> _logger;

    public FrontMarginUIService(HttpClient httpClient, ILogger<FrontMarginUIService> logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<bool> HasActiveFrontMarginAsync(string vendorCode)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponseDto<List<GetPromotionFrontMarginDto>>>(
                $"api/FrontMargin/available/{vendorCode}");

            return response?.IsSuccessStatusCode == true && response.Result?.Any() == true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking active Front Margin for vendor {VendorCode}", vendorCode);
            return false;
        }
    }

    public async Task<FrontMarginDiscountInfo?> GetLineDiscountAsync(string vendorCode, string itemNumber, DateTime orderDate)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponseDto<GetPromotionFrontMarginDto>>(
                $"api/FrontMargin/discount/{vendorCode}/{itemNumber}?orderDate={orderDate:yyyy-MM-dd}");

            if (response?.IsSuccessStatusCode == true && response.Result != null)
            {
                var fm = response.Result;
                return new FrontMarginDiscountInfo
                {
                    PromotionNumber = fm.Number,
                    PromotionName = fm.ProgramNumber,
                    DiscountType = fm.DiscountType,
                    DiscountValue = GetDiscountValue(fm),
                    DiscountPercentage = CalculateDiscountPercentage(fm),
                    HasGifts = fm.DiscountType == 3 || fm.DiscountType == 4,
                    GiftQuantity = fm.GiftQuantity ?? 0,
                    GiftItemNumber = fm.GiftItemNumber
                };
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Front Margin discount for {VendorCode}/{ItemNumber}", vendorCode, itemNumber);
            return null;
        }
    }

    public async Task<List<FrontMarginPromotionInfo>> GetApplicablePromotionsAsync(string vendorCode, DateTime orderDate)
    {
        try
        {
            var response = await _httpClient.GetFromJsonAsync<ApiResponseDto<List<GetPromotionFrontMarginDto>>>(
                $"api/FrontMargin/available/{vendorCode}?orderDate={orderDate:yyyy-MM-dd}");

            if (response?.IsSuccessStatusCode == true && response.Result != null)
            {
                return response.Result.Select(fm => new FrontMarginPromotionInfo
                {
                    Number = fm.Number,
                    ProgramNumber = fm.ProgramNumber,
                    ItemNumber = fm.ItemNumber,
                    ItemName = fm.ItemName,
                    DiscountType = fm.DiscountType,
                    DiscountTypeName = GetDiscountTypeName(fm.DiscountType),
                    DiscountValue = fm.DiscountValue,
                    StartDate = fm.StartDate,
                    EndDate = fm.EndDate,
                    Status = fm.Status
                }).ToList();
            }

            return new List<FrontMarginPromotionInfo>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting applicable promotions for vendor {VendorCode}", vendorCode);
            return new List<FrontMarginPromotionInfo>();
        }
    }

    public async Task<FrontMarginPreviewResult> PreviewPOImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        try
        {
            var previewData = new
            {
                Header = poHeader,
                Lines = poLines
            };

            var response = await _httpClient.PostAsJsonAsync("api/POFrontMargin/preview", previewData);
            var result = await response.Content.ReadFromJsonAsync<ApiResponseDto<object>>();

            if (result?.IsSuccessStatusCode == true)
            {
                // Parse the preview result
                return ParsePreviewResult(result.Result);
            }

            return new FrontMarginPreviewResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing Front Margin impact for PO");
            return new FrontMarginPreviewResult();
        }
    }

    public async Task<List<POLineGetDto>> ApplyFrontMarginToLinesAsync(List<POLineGetDto> lines, string vendorCode, DateTime orderDate)
    {
        var updatedLines = new List<POLineGetDto>();

        foreach (var line in lines)
        {
            var updatedLine = new POLineGetDto
            {
                // Copy all properties
                LineNumber = line.LineNumber,
                ItemNumber = line.ItemNumber,
                Description = line.Description,
                Quantity = line.Quantity,
                UnitOfMeasure = line.UnitOfMeasure,
                UnitCost = line.UnitCost,
                UnitPrice = line.UnitPrice,
                Amount = line.Amount,
                DocumentType = line.DocumentType,
                LotNo = line.LotNo
            };

            // Try to apply Front Margin discount
            var discount = await GetLineDiscountAsync(vendorCode, line.ItemNumber, orderDate);
            if (discount != null)
            {
                // Apply discount
                if (discount.DiscountType == 1) // Percentage
                {
                    updatedLine.UnitCost = line.UnitCost * (1 - discount.DiscountPercentage / 100);
                }
                else if (discount.DiscountType == 2) // Fixed amount
                {
                    updatedLine.UnitCost = line.UnitCost - (discount.DiscountValue / line.Quantity);
                }

                updatedLine.Amount = updatedLine.UnitCost * updatedLine.Quantity;

                // Mark as Front Margin applied
                updatedLine.HasFrontMargin = true;
                updatedLine.FrontMarginNumber = discount.PromotionNumber;
            }

            updatedLines.Add(updatedLine);
        }

        return updatedLines;
    }

    #region Private Methods

    private static decimal GetDiscountValue(GetPromotionFrontMarginDto fm)
    {
        return fm.DiscountType switch
        {
            1 => fm.DiscountPercentage, // Percentage discount
            2 => fm.FixedDiscountAmount ?? 0, // Fixed amount discount
            3 => fm.GiftQuantity ?? 0, // Same item gift quantity
            4 => fm.GiftItemQuantity ?? 0, // Different item gift quantity
            _ => 0
        };
    }

    private static decimal CalculateDiscountPercentage(GetPromotionFrontMarginDto fm)
    {
        return fm.DiscountType switch
        {
            1 => fm.DiscountPercentage, // Already percentage
            2 => 0, // Fixed amount - percentage depends on item price
            3 => CalculateGiftDiscountPercentage(fm), // Same item gift
            4 => CalculateGiftDiscountPercentage(fm), // Different item gift
            _ => 0
        };
    }

    private static decimal CalculateGiftDiscountPercentage(GetPromotionFrontMarginDto fm)
    {
        if (fm.BuyQuantity > 0 && fm.GiftQuantity > 0)
        {
            var totalQuantity = fm.BuyQuantity.Value + fm.GiftQuantity.Value;
            return (fm.GiftQuantity.Value / totalQuantity) * 100;
        }
        return 0;
    }

    private static string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Percentage Discount",
            2 => "Fixed Amount Discount",
            3 => "Same Item Gift",
            4 => "Different Item Gift",
            _ => "Unknown"
        };
    }

    private static FrontMarginPreviewResult ParsePreviewResult(object? data)
    {
        // Parse the preview result from API response
        // This would depend on the actual structure returned by the API
        // For now, return empty result as placeholder
        return new FrontMarginPreviewResult
        {
            TotalOriginalAmount = 0,
            TotalDiscountAmount = 0,
            TotalFinalAmount = 0,
            DiscountPercentage = 0,
            AppliedPromotions = new List<string>(),
            GiftLines = new List<POLineGetDto>()
        };
    }

    #endregion
}

#region DTOs for UI

public class FrontMarginDiscountInfo
{
    public string PromotionNumber { get; set; } = string.Empty;
    public string PromotionName { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public decimal DiscountValue { get; set; }
    public decimal DiscountPercentage { get; set; }
    public bool HasGifts { get; set; }
    public decimal GiftQuantity { get; set; }
    public string? GiftItemNumber { get; set; }
}

public class FrontMarginPromotionInfo
{
    public string Number { get; set; } = string.Empty;
    public string ProgramNumber { get; set; } = string.Empty;
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public string DiscountTypeName { get; set; } = string.Empty;
    public decimal DiscountValue { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Status { get; set; }
}

public class FrontMarginPreviewResult
{
    public decimal TotalOriginalAmount { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public decimal TotalFinalAmount { get; set; }
    public decimal DiscountPercentage { get; set; }
    public List<string> AppliedPromotions { get; set; } = new();
    public List<POLineGetDto> GiftLines { get; set; } = new();
}

#endregion
