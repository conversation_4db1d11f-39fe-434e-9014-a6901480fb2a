using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Promotion Header - Supports both Front Margin and Back Margin programs
/// </summary>
[Table("PromotionHeaders")]
public class PromotionHeader : FullTrackingEntity
{
    [StringLength(250)]
    public string ProgramName { get; set; } = string.Empty;

    [Required]
    [StringLength(500)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    public string VendorCode { get; set; } = string.Empty;

    [StringLength(50)]
    public string? SupportTypeNumber { get; set; }

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Tích luỹ doanh số (true/false)
    /// FrontMargin: Không sử dụng (luôn false)
    /// BackMargin: <PERSON><PERSON> tích lũy doanh số qua các chu kỳ không
    /// </summary>
    public bool AccumulateRevenue { get; set; }

    /// <summary>
    /// Loại chương trình: 1=FrontMargin, 2=BackMargin
    /// </summary>
    [Required]
    public int ProgramType { get; set; } = 1;// 1=FrontMargin, 2=BackMargin

    /// <summary>
    /// Status: 1=Draft, 2=Active, 3=Inactive, 4=Expired
    /// </summary>
    public int Status { get; set; } = 1;

    // MINIMAL FIELDS FOR PO INTEGRATION (Simplified Design)

    /// <summary>
    /// Độ ưu tiên khi có nhiều CTKM (1 = cao nhất, 2, 3...)
    /// FrontMargin: Dùng để resolve conflicts khi có nhiều FM cho cùng item
    /// BackMargin: Dùng để sắp xếp thứ tự tính toán
    /// </summary>
    public int Priority { get; set; } = 1;

    /// <summary>
    /// Có tự động áp dụng không
    /// FrontMargin: true = tự động áp dụng khi tạo PO
    /// BackMargin: false = cần tính toán thủ công với PromotionCondition/PromotionReward
    /// </summary>
    public bool AutoApply { get; set; } = true;

    // APPROVAL WORKFLOW (Simple)
    /// <summary>
    /// 1=Pending, 2=VendorApproved, 3=PurchaserApproved, 4=FullyApproved, 5=Rejected
    /// </summary>
    public int ApprovalStatus { get; set; } = 1;

    [StringLength(100)]
    public string? ApprovalBy { get; set; }

    public DateTime? ApprovalDate { get; set; }

    // DOCUMENT LOCKING (giống PO)
    /// <summary>
    /// User đang sử dụng
    /// </summary>
    [StringLength(100)]
    public string? UsingId { get; set; }

    /// <summary>
    /// Thời gian bắt đầu sử dụng
    /// </summary>
    public DateTime? BeginUsingTime { get; set; }

    // Navigation properties

    /// <summary>
    /// Front Margin lines - Đơn giản với 3 cases (Percentage, Fixed, Gift)
    /// </summary>
    public ICollection<PromotionFrontMargin> PromotionFrontMargins { get; set; } = new List<PromotionFrontMargin>();

    /// <summary>
    /// Back Margin conditions - Phức tạp với 5 loại chiết khấu
    /// 1. Chiết khấu theo doanh số (1.1 Lũy tiến, 1.2 Mốc, 1.3 Bật thang)
    /// 2. Chiết khấu theo số lượng (2.1 Lũy tiến, 2.2 Mốc, 2.3 Bật thang)
    /// 3. Chiết khấu thanh toán sớm
    /// </summary>
    public ICollection<PromotionCondition> PromotionConditions { get; set; } = new List<PromotionCondition>();

    /// <summary>
    /// Back Margin rewards - Kết quả tính toán từ conditions
    /// </summary>
    public ICollection<PromotionReward> PromotionRewards { get; set; } = new List<PromotionReward>();

    /// <summary>
    /// Back Margin lines - Sẽ deprecated, logic chuyển sang PromotionCondition/PromotionReward
    /// </summary>
    public ICollection<PromotionBackMargin> PromotionBackMargins { get; set; } = new List<PromotionBackMargin>();

    [ForeignKey("VendorCode")]
    public Vendor Vendor { get; set; }

    [ForeignKey("SupportTypeNumber")]
    public PromotionBackMarginSupportType PromotionBackMarginSupportType { get; set; }
}
