using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
namespace PurchaseManager.Infrastructure.Storage.DataModels;

/// <summary>
/// Promotion Header - Supports both Front Margin and Back Margin programs
/// </summary>
[Table("PromotionHeaders")]
public class PromotionHeader : FullTrackingEntity
{
    [StringLength(250)]
    public string ProgramName { get; set; } = string.Empty;

    [Required]
    [StringLength(500)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    public string VendorCode { get; set; } = string.Empty;

    [StringLength(50)]
    public string? SupportTypeNumber { get; set; }

    [Required]
    public DateTime StartDate { get; set; }

    [Required]
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Tích luỹ doanh số (true/false)
    /// FrontMargin: Không sử dụng (luôn false)
    /// BackMargin: <PERSON><PERSON> tích lũy doanh số qua các chu kỳ không
    /// </summary>
    public bool AccumulateRevenue { get; set; }

    /// <summary>
    /// Loại chương trình: 1=FrontMargin, 2=BackMargin
    /// </summary>
    [Required]
    public int ProgramType { get; set; } = 1;// 1=FrontMargin, 2=BackMargin

    /// <summary>
    /// Status: 1=Draft, 2=Active, 3=Inactive, 4=Expired
    /// </summary>
    public int Status { get; set; } = 1;

    // PURCHASE ORDER INTEGRATION FIELDS

    /// <summary>
    /// Độ ưu tiên khi có nhiều CTKM
    /// </summary>
    public int Priority { get; set; } = 1;

    // APPROVAL WORKFLOW (giống PO)
    /// <summary>
    /// 1=Pending, 2=VendorApproved, 3=PurchaserApproved, 4=FullyApproved, 5=Rejected
    /// </summary>
    public int ApprovalStatus { get; set; } = 1;

    [StringLength(100)]
    public string? ApprovalBy { get; set; }

    public DateTime? ApprovalDate { get; set; }

    // BUDGET & TRACKING
    /// <summary>
    ///     Ngân sách tối đa cho chương trình
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaxBudgetAmount { get; set; }

    /// <summary>
    ///     Số tiền đã sử dụng
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal UsedAmount { get; set; }

    // REWARD SETTINGS (Áp dụng cho cả Front & Back Margin)
    /// <summary>
    ///     Hình thức chi trả/áp dụng (nullable - sẽ được set trong Condition/Reward chi tiết)
    ///     FrontMargin: Không sử dụng (logic trong PromotionFrontMargin)
    ///     BackMargin: Sử dụng làm default cho PromotionReward
    /// </summary>
    public int? DefaultPaymentMethod { get; set; }

    /// <summary>
    ///     Thời điểm chi trả/áp dụng (nullable - sẽ được set trong Condition/Reward chi tiết)
    ///     FrontMargin: Không sử dụng (luôn immediate)
    ///     BackMargin: Sử dụng làm default cho PromotionReward
    /// </summary>
    public int? DefaultPaymentTiming { get; set; }

    /// <summary>
    ///     Chu kỳ đánh giá chương trình
    ///     FrontMargin: Không sử dụng
    ///     BackMargin: Monthly, Quarterly, Yearly
    /// </summary>
    public int? EvaluationPeriod { get; set; }

    /// <summary>
    ///     Có yêu cầu phê duyệt trước khi áp dụng/trả thưởng không
    ///     FrontMargin: Thường false (tự động áp dụng)
    ///     BackMargin: Thường true (cần phê duyệt)
    /// </summary>
    public bool RequireApprovalBeforeReward { get; set; }

    /// <summary>
    ///     Có áp dụng cho tất cả sản phẩm của vendor không
    /// </summary>
    public bool ApplyToAllVendorItems { get; set; } = true;

    /// <summary>
    ///     Giá trị đơn hàng tối thiểu để áp dụng khuyến mãi (optional)
    ///     FrontMargin: Áp dụng cho từng PO
    ///     BackMargin: Không sử dụng (logic trong PromotionCondition)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MinOrderValue { get; set; }

    /// <summary>
    ///     Giá trị đơn hàng tối đa để áp dụng khuyến mãi (optional)
    ///     FrontMargin: Áp dụng cho từng PO
    ///     BackMargin: Không sử dụng (logic trong PromotionCondition)
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaxOrderValue { get; set; }

    /// <summary>
    ///     Giá trị chiết khấu/thưởng tối đa cho một đơn hàng/chu kỳ (optional)
    ///     FrontMargin: Tối đa cho một PO
    ///     BackMargin: Tối đa cho một chu kỳ đánh giá
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaxDiscountAmount { get; set; }

    /// <summary>
    ///     Loại đơn hàng áp dụng (1,2,3...) - comma separated (optional)
    ///     FrontMargin: Loại PO áp dụng
    ///     BackMargin: Loại PO được tính vào doanh số
    /// </summary>
    [StringLength(100)]
    public string? ApplicableDocTypes { get; set; }

    /// <summary>
    ///     Có tự động áp dụng/tính toán không
    ///     FrontMargin: Tự động áp dụng khi tạo PO
    ///     BackMargin: Tự động tính toán khi đạt điều kiện
    /// </summary>
    public bool AutoApply { get; set; } = true;

    /// <summary>
    ///     Ghi chú về điều kiện đặc biệt
    /// </summary>
    [StringLength(1000)]
    public string? SpecialConditions { get; set; }

    // DOCUMENT LOCKING (giống PO)
    /// <summary>
    /// User đang sử dụng
    /// </summary>
    [StringLength(100)]
    public string? UsingId { get; set; }

    /// <summary>
    /// Thời gian bắt đầu sử dụng
    /// </summary>
    public DateTime? BeginUsingTime { get; set; }

    // Navigation properties
    public ICollection<PromotionCondition> PromotionConditions { get; set; } = new List<PromotionCondition>();

    public ICollection<PromotionReward> PromotionRewards { get; set; } = new List<PromotionReward>();

    public ICollection<PromotionFrontMargin> PromotionFrontMargins { get; set; } = new List<PromotionFrontMargin>();
    public ICollection<PromotionBackMargin> PromotionBackMargins { get; set; } = new List<PromotionBackMargin>();
    // BackMargin sử dụng PromotionConditions và PromotionRewards

    [ForeignKey("VendorCode")]
    public Vendor Vendor { get; set; }

    [ForeignKey("SupportTypeNumber")]
    public PromotionBackMarginSupportType PromotionBackMarginSupportType { get; set; }
}
