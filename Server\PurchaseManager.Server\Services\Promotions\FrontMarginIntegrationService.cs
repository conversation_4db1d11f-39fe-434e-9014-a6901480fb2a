using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Shared.Dto.PO;
namespace PurchaseManager.Server.Services.Promotions;

/// <summary>
///     Service for integrating Front Margin with PO system
/// </summary>
public interface IFrontMarginIntegrationService
{
    /// <summary>
    ///     Get applicable Front Margin promotions for PO
    /// </summary>
    Task<List<PromotionFrontMargin>> GetApplicablePromotionsAsync(POHeaderGetDto poHeader);

    /// <summary>
    ///     Apply Front Margin to PO and return updated PO
    /// </summary>
    Task<POIntegrationResult> ApplyFrontMarginToPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Preview Front Margin impact without applying
    /// </summary>
    Task<POIntegrationPreview> PreviewFrontMarginImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Validate PO for Front Margin eligibility
    /// </summary>
    Task<POValidationResult> ValidatePOForFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Remove Front Margin from PO
    /// </summary>
    Task<POIntegrationResult> RemoveFrontMarginFromPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    ///     Get Front Margin history for vendor
    /// </summary>
    Task<List<FrontMarginUsageHistory>> GetUsageHistoryAsync(string vendorCode, DateTime? fromDate = null, DateTime? toDate = null);
}
public class FrontMarginIntegrationService : IFrontMarginIntegrationService
{
    private readonly IFrontMarginCalculationService _calculationService;
    private readonly IFrontMarginCacheService _cacheService;
    private readonly ILogger<FrontMarginIntegrationService> _logger;

    public FrontMarginIntegrationService(IFrontMarginCalculationService calculationService,
        IFrontMarginCacheService cacheService,
        ILogger<FrontMarginIntegrationService> logger)
    {
        _calculationService = calculationService;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<List<PromotionFrontMargin>> GetApplicablePromotionsAsync(POHeaderGetDto poHeader)
    {
        try
        {
            if (string.IsNullOrEmpty(poHeader.BuyFromVendorNumber))
            {
                return new List<PromotionFrontMargin>();
            }

            // Use cache service for better performance
            var allPromotions = await _cacheService.GetActivePromotionsAsync(poHeader.BuyFromVendorNumber);

            // Filter by date if PO has order date
            var currentDate = poHeader.OrderDate;

            var applicablePromotions = allPromotions
                .Where(p => p.PromotionHeader.StartDate <= currentDate &&
                            p.PromotionHeader.EndDate >= currentDate)
                .ToList();

            _logger.LogInformation("Found {Count} applicable Front Margin promotions for vendor {VendorCode}",
            applicablePromotions.Count, poHeader.BuyFromVendorNumber);

            return applicablePromotions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting applicable promotions for PO {PONumber}", poHeader.Number);
            return new List<PromotionFrontMargin>();
        }
    }

    public async Task<POIntegrationResult> ApplyFrontMarginToPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        var result = new POIntegrationResult
        {
            Success = true,
            OriginalPOHeader = poHeader,
            OriginalPOLines = poLines.ToList(),
            UpdatedPOLines = new List<POLineGetDto>(),
            GiftLines = new List<POLineGetDto>(),
            AppliedPromotions = new List<AppliedPromotionInfo>()
        };

        try
        {
            var applicablePromotions = await GetApplicablePromotionsAsync(poHeader);

            if (!applicablePromotions.Any())
            {
                result.UpdatedPOLines = poLines.ToList();
                result.Message = "No applicable Front Margin promotions found";
                return result;
            }

            decimal totalOriginalAmount = 0;
            decimal totalFinalAmount = 0;

            foreach (var poLine in poLines)
            {
                totalOriginalAmount += poLine.Amount;

                // Get promotions for this specific item
                var itemPromotions = applicablePromotions
                    .Where(p => p.ItemNumber == poLine.ItemNumber)
                    .ToList();

                if (itemPromotions.Any())
                {
                    var calculationResult = await _calculationService.CalculateLineDiscountAsync(poLine, itemPromotions);

                    // Update PO line with discounted price
                    var updatedLine = new POLineGetDto
                    {
                        LineNumber = poLine.LineNumber,
                        ItemNumber = poLine.ItemNumber,
                        Description = poLine.Description,
                        Quantity = poLine.Quantity,
                        UnitCost = calculationResult.FinalUnitCost,
                        UnitOfMeasure = poLine.UnitOfMeasure,
                        Amount = calculationResult.FinalAmount,
                        DocumentType = poLine.DocumentType
                    };

                    result.UpdatedPOLines.Add(updatedLine);
                    totalFinalAmount += calculationResult.FinalAmount;

                    // Add gift lines if any
                    if (calculationResult.GiftLines?.Any() == true)
                    {
                        result.GiftLines.AddRange(calculationResult.GiftLines);
                    }

                    // Track applied promotions
                    foreach (var promotion in itemPromotions)
                    {
                        result.AppliedPromotions.Add(new AppliedPromotionInfo
                        {
                            PromotionNumber = promotion.Number,
                            DiscountType = promotion.DiscountType,
                            DiscountAmount = calculationResult.TotalDiscountAmount,
                            NewUnitCost = calculationResult.FinalUnitCost,
                            GiftLines = calculationResult.GiftLines
                        });
                    }
                }
                else
                {
                    // No promotions for this item, keep original
                    result.UpdatedPOLines.Add(poLine);
                    totalFinalAmount += poLine.Amount;
                }
            }

            result.TotalOriginalAmount = totalOriginalAmount;
            result.TotalFinalAmount = totalFinalAmount;
            result.TotalSavings = totalOriginalAmount - totalFinalAmount;
            result.TotalDiscountPercentage = totalOriginalAmount > 0 ? result.TotalSavings / totalOriginalAmount * 100 : 0;

            result.Message =
                $"Front Margin applied successfully. Total savings: {result.TotalSavings:N0} VND ({result.TotalDiscountPercentage:F2}%)";

            _logger.LogInformation("Applied Front Margin to PO {PONumber}: {Savings:N0} VND savings",
            poHeader.Number, result.TotalSavings);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying Front Margin to PO {PONumber}", poHeader.Number);
            result.Success = false;
            result.Message = $"Error applying Front Margin: {ex.Message}";
            result.UpdatedPOLines = poLines.ToList();
            return result;
        }
    }

    public async Task<POIntegrationPreview> PreviewFrontMarginImpactAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        var preview = new POIntegrationPreview
        {
            PONumber = poHeader.Number,
            VendorCode = poHeader.BuyFromVendorNumber,
            LineImpacts = new List<LineImpactPreview>()
        };

        try
        {
            var applicablePromotions = await GetApplicablePromotionsAsync(poHeader);

            decimal totalOriginalAmount = 0;
            decimal totalProjectedSavings = 0;

            foreach (var poLine in poLines)
            {
                var lineOriginalAmount = poLine.Amount > 0 ? poLine.Amount : poLine.Quantity * poLine.UnitCost;
                totalOriginalAmount += lineOriginalAmount;

                var itemPromotions = applicablePromotions
                    .Where(p => p.ItemNumber == poLine.ItemNumber)
                    .ToList();

                var lineImpact = new LineImpactPreview
                {
                    LineNumber = poLine.LineNumber,
                    ItemNumber = poLine.ItemNumber,
                    ItemName = poLine.Description,
                    OriginalAmount = lineOriginalAmount,
                    HasPromotion = itemPromotions.Any()
                };

                if (itemPromotions.Any())
                {
                    var calculationResult = await _calculationService.CalculateLineDiscountAsync(poLine, itemPromotions);

                    lineImpact.ProjectedFinalAmount = calculationResult.FinalAmount;
                    lineImpact.ProjectedSavings = calculationResult.TotalSavings;
                    lineImpact.DiscountPercentage = calculationResult.TotalSavings > 0 && lineOriginalAmount > 0
                        ? calculationResult.TotalSavings / lineOriginalAmount * 100 : 0;
                    lineImpact.ApplicablePromotions = itemPromotions.Count;
                    lineImpact.HasGifts = calculationResult.GiftLines?.Any() == true;
                    lineImpact.GiftCount = calculationResult.GiftLines?.Count ?? 0;

                    totalProjectedSavings += calculationResult.TotalSavings;
                }
                else
                {
                    lineImpact.ProjectedFinalAmount = lineOriginalAmount;
                    lineImpact.ProjectedSavings = 0;
                    lineImpact.DiscountPercentage = 0;
                }

                preview.LineImpacts.Add(lineImpact);
            }

            preview.TotalOriginalAmount = totalOriginalAmount;
            preview.TotalProjectedSavings = totalProjectedSavings;
            preview.ProjectedDiscountPercentage = totalOriginalAmount > 0 ? totalProjectedSavings / totalOriginalAmount * 100 : 0;
            preview.LinesWithPromotions = preview.LineImpacts.Count(l => l.HasPromotion);
            preview.TotalApplicablePromotions = applicablePromotions.Count;

            return preview;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error previewing Front Margin impact for PO {PONumber}", poHeader.Number);
            preview.ErrorMessage = ex.Message;
            return preview;
        }
    }

    public async Task<POValidationResult> ValidatePOForFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        var result = new POValidationResult
        {
            IsValid = true,
            ValidationMessages = new List<string>(),
            Warnings = new List<string>()
        };

        try
        {
            // Basic validations
            if (string.IsNullOrEmpty(poHeader.BuyFromVendorNumber))
            {
                result.ValidationMessages.Add("PO must have a vendor to apply Front Margin");
                result.IsValid = false;
            }

            if (!poLines.Any())
            {
                result.ValidationMessages.Add("PO must have at least one line to apply Front Margin");
                result.IsValid = false;
            }

            if (!result.IsValid)
            {
                return result;
            }

            // Check for applicable promotions
            var applicablePromotions = await GetApplicablePromotionsAsync(poHeader);

            if (!applicablePromotions.Any())
            {
                result.Warnings.Add("No Front Margin promotions available for this vendor");
                return result;
            }

            // Check line-level validations
            var linesWithPromotions = 0;
            foreach (var poLine in poLines)
            {
                if (poLine.Quantity <= 0)
                {
                    result.Warnings.Add($"Line {poLine.LineNumber}: Zero or negative quantity may not qualify for promotions");
                }

                if (poLine.UnitCost <= 0)
                {
                    result.Warnings.Add($"Line {poLine.LineNumber}: Zero or negative unit cost may not qualify for promotions");
                }

                var itemPromotions = applicablePromotions.Where(p => p.ItemNumber == poLine.ItemNumber)
                    .ToList();
                if (itemPromotions.Any())
                {
                    linesWithPromotions++;
                }
            }

            if (linesWithPromotions == 0)
            {
                result.Warnings.Add("No PO lines match available Front Margin promotions");
            }
            else
            {
                result.ValidationMessages.Add($"{linesWithPromotions} PO lines are eligible for Front Margin promotions");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating PO {PONumber} for Front Margin", poHeader.Number);
            result.IsValid = false;
            result.ValidationMessages.Add($"Validation error: {ex.Message}");
            return result;
        }
    }

    public async Task<POIntegrationResult> RemoveFrontMarginFromPOAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        var result = new POIntegrationResult
        {
            Success = true,
            OriginalPOHeader = poHeader,
            OriginalPOLines = poLines.ToList(),
            UpdatedPOLines = [],
            GiftLines = [],
            AppliedPromotions = []
        };

        try
        {
            // Remove Front Margin by restoring original prices
            // This assumes the PO lines currently have Front Margin applied
            // In a real implementation, you would need to store original prices somewhere

            foreach (var poLine in poLines)
            {
                // Skip gift lines (DocumentType = 2)
                if (poLine.DocumentType == 2)
                {
                    continue;
                }

                // For regular lines, we would restore original prices
                // This is a simplified implementation
                var restoredLine = new POLineGetDto
                {
                    LineNumber = poLine.LineNumber,
                    ItemNumber = poLine.ItemNumber,
                    Description = poLine.Description,
                    Quantity = poLine.Quantity,
                    UnitCost = poLine.UnitCost,// Would restore from original
                    UnitOfMeasure = poLine.UnitOfMeasure,
                    Amount = poLine.Amount,// Would restore from original
                    DocumentType = poLine.DocumentType
                };

                result.UpdatedPOLines.Add(restoredLine);
            }

            result.TotalOriginalAmount = result.UpdatedPOLines.Sum(l => l.Amount);
            result.TotalFinalAmount = result.TotalOriginalAmount;
            result.TotalSavings = 0;
            result.TotalDiscountPercentage = 0;

            result.Message = "Front Margin removed from PO successfully";

            _logger.LogInformation("Removed Front Margin from PO {PONumber}", poHeader.Number);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing Front Margin from PO {PONumber}", poHeader.Number);
            result.Success = false;
            result.Message = $"Error removing Front Margin: {ex.Message}";
            result.UpdatedPOLines = poLines.ToList();
            return result;
        }
    }

    public async Task<List<FrontMarginUsageHistory>> GetUsageHistoryAsync(string vendorCode, DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        try
        {
            // This would query actual PO history with Front Margin applied
            // For now, return sample data
            return new List<FrontMarginUsageHistory>
            {
                new FrontMarginUsageHistory
                {
                    VendorCode = vendorCode,
                    PONumber = "PO2025001",
                    PODate = DateTime.Now.AddDays(-30),
                    OriginalAmount = 1000000,
                    DiscountAmount = 50000,
                    FinalAmount = 950000,
                    PromotionsUsed = 2
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting usage history for vendor {VendorCode}", vendorCode);
            return new List<FrontMarginUsageHistory>();
        }
    }
}
#region Result Classes
public class POIntegrationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public POHeaderGetDto OriginalPOHeader { get; set; } = null!;
    public List<POLineGetDto> OriginalPOLines { get; set; } = new List<POLineGetDto>();
    public List<POLineGetDto> UpdatedPOLines { get; set; } = new List<POLineGetDto>();
    public List<POLineGetDto> GiftLines { get; set; } = new List<POLineGetDto>();
    public List<AppliedPromotionInfo> AppliedPromotions { get; set; } = new List<AppliedPromotionInfo>();
    public decimal TotalOriginalAmount { get; set; }
    public decimal TotalFinalAmount { get; set; }
    public decimal TotalSavings { get; set; }
    public decimal TotalDiscountPercentage { get; set; }
}
public class POIntegrationPreview
{
    public string PONumber { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public List<LineImpactPreview> LineImpacts { get; set; } = new List<LineImpactPreview>();
    public decimal TotalOriginalAmount { get; set; }
    public decimal TotalProjectedSavings { get; set; }
    public decimal ProjectedDiscountPercentage { get; set; }
    public int LinesWithPromotions { get; set; }
    public int TotalApplicablePromotions { get; set; }
    public string? ErrorMessage { get; set; }
}
public class LineImpactPreview
{
    public int LineNumber { get; set; }
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public decimal OriginalAmount { get; set; }
    public decimal ProjectedFinalAmount { get; set; }
    public decimal ProjectedSavings { get; set; }
    public decimal DiscountPercentage { get; set; }
    public bool HasPromotion { get; set; }
    public int ApplicablePromotions { get; set; }
    public bool HasGifts { get; set; }
    public int GiftCount { get; set; }
}
public class POValidationResult
{
    public bool IsValid { get; set; }
    public List<string> ValidationMessages { get; set; } = new List<string>();
    public List<string> Warnings { get; set; } = new List<string>();
}
public class FrontMarginUsageHistory
{
    public string VendorCode { get; set; } = string.Empty;
    public string PONumber { get; set; } = string.Empty;
    public DateTime PODate { get; set; }
    public decimal OriginalAmount { get; set; }
    public decimal DiscountAmount { get; set; }
    public decimal FinalAmount { get; set; }
    public int PromotionsUsed { get; set; }
}
#endregion
