﻿@inherits TodoPagingBasePage
@page "/todos-paging"

<PageTitle>Todo List - Server Side Filtering, Sorting and Pagination example</PageTitle>
<MudCard Outlined="true">
    <MudCardContent>
        <DateTimeFilter Busy="isBusy" Filter="todoFilter"/>
        <MudGrid Spacing="0" Class="my-4">
            <MudItem xs="6" sm="3" Class="px-8">
                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Disabled="@isBusy"
                           @bind-Value="@todoFilter.CreatedById" FullWidth="true" Label="Creators">
                    @foreach (var user in Creators)
                    {
                        <MudSelectItem Value="@user.Id">@user.DisplayValue</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="6" sm="3" Class="px-8">
                <MudSelect Variant="Variant.Outlined" Margin="Margin.Dense" Disabled="@isBusy"
                           @bind-Value="@todoFilter.ModifiedById" FullWidth="true" Label="Editors">
                    @foreach (var user in Editors)
                    {
                        <MudSelectItem Value="@user.Id">@user.DisplayValue</MudSelectItem>
                    }
                </MudSelect>
            </MudItem>
            <MudItem xs="6" sm="3" Class="px-8">
                <MudCheckBox T="bool?" @bind-Value="@todoFilter.IsCompleted" Size="Size.Large" TriState="true">Is
                    Completed
                </MudCheckBox>
            </MudItem>
            <MudItem xs="6" sm="3" Class="px-8">

            </MudItem>
        </MudGrid>
        <MudGrid Spacing="0" Class="my-4">
            <MudItem xs="10" sm="4" Class="d-flex align-center justify-center mud-width-full px-8">
                <MudTextField Variant="Variant.Outlined" Margin="Margin.Dense" T="string"
                              ValueChanged="@(s => OnSearch(s))" Placeholder=@L["Search"] Adornment="Adornment.End"
                              AdornmentIcon="@Icons.Material.Filled.Search" AdornmentColor="Color.Secondary"/>
            </MudItem>
        </MudGrid>
    </MudCardContent>
</MudCard>
<MudCard Class="mt-6" Outlined="true">
    <MudCardHeader Class="justify-end  ">
        <MudFab Icon="@Icons.Material.Filled.Refresh" OnClick="@(() => Reload())" Size="Size.Medium" Class="ma-2"/>
    </MudCardHeader>
    <MudCardContent>
        <MudTable ServerData="ServerReload" Striped="true" Bordered="true" Dense="true" Hover="true"
                  Class="rounded-0 pa-2" Elevation="4"
                  Loading="@isBusy"
                  LoadingProgressColor="Color.Info" @ref="table">
            <HeaderContent>
                <MudTh>
                    <MudTableSortLabel SortLabel="Title" T="Todo">@L["Name"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="IsCompleted" T="Todo">@L["Completed"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="CreatedOn" T="Todo">@L["CreatedOn"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="CreatedBy.UserName" T="Todo">@L["CreatedBy"]</MudTableSortLabel>
                </MudTh>
                <MudTh>
                    <MudTableSortLabel SortLabel="ModifiedBy.UserName" T="Todo">@L["ModifiedBy"]</MudTableSortLabel>
                </MudTh>
            </HeaderContent>
            <RowTemplate Context="row">
                <MudTd DataLabel="@L["Name"]">@row.Title</MudTd>
                <MudTd DataLabel="@L["Completed"]">
                    <MudSwitch T="bool" Value="@row.IsCompleted" Color="Color.Primary"
                               ValueChanged="@((bool i) => Update(row))"/>
                </MudTd>
                <MudTd DataLabel="@L["CreatedOn"]">@row.CreatedOn</MudTd>
                <MudTd DataLabel="@L["CreatedBy"]">@row.CreatedBy?.UserName</MudTd>
                <MudTd DataLabel="@L["ModifiedBy"]">@row.ModifiedBy?.UserName</MudTd>
            </RowTemplate>
            <PagerContent>
                <MudTablePager RowsPerPageString=@L["Rows per page"]/>
            </PagerContent>
        </MudTable>
    </MudCardContent>

</MudCard>

@code {

}