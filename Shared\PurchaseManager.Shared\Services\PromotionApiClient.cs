using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models.Promotions;
using System.Net.Http.Json;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Extensions;

namespace PurchaseManager.Shared.Services;

public class PromotionApiClient : BaseApiClient, IPromotionApiClient
{
    private const string BaseUrl = "api/Promotion";

    public PromotionApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, string rootApiPath = "api/data/")
        : base(httpClient, logger, rootApiPath)
    {
    }

    #region Promotion Header
    public async Task<ApiResponseDto<PagedResult<GetPromotionHeaderDto>>> GetPromotionsAsync(PromotionFilter filter)
    {
        var query = filter.ToQuery();
        var responseApi =
            await httpClient.GetFromJsonAsync<ApiResponseDto<PagedResult<GetPromotionHeaderDto>>>(
            $"{BaseUrl}/filtered?" + query);
        return responseApi;
    }
    public async Task<ApiResponseDto<GetPromotionHeaderDto>> GetPromotionHeaderByNumberAsync(string number)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<GetPromotionHeaderDto>>($"{BaseUrl}/{number}");
    }
    public async Task<ApiResponseDto<CreatePromotionHeaderDto>> CreatePromotionHeaderAsync(CreatePromotionHeaderDto createPromotionDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<CreatePromotionHeaderDto>>($"{BaseUrl}", createPromotionDto);
    }
    public async Task<ApiResponseDto<int>> OpenDocumentAsync(string documentNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/open-document/{documentNumber}", null);
    }
    public async Task<ApiResponseDto<int>> CloseDocumentAsync(string documentNumber)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>(
        $"{BaseUrl}/close-document/{documentNumber}", null);
    }
    public async Task<ApiResponseDto<int>> UpdatePromotionHeaderAsync(string number, UpdatePromotionHeaderDto updateDto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>($"{BaseUrl}/{number}", updateDto);
    }

    public Task<ApiResponseDto<int>> ApprovePromotionAsync(string number)
    {
        throw new NotImplementedException();
    }
    public async Task<ApiResponseDto> DeletePromotionHeaderAsync(List<string> numbers)
    {
        return await httpClient.SendJsonAsync<ApiResponseDto>(HttpMethod.Delete, $"{BaseUrl}/bulk", numbers);
    }
    #endregion

    #region Promotion Front Margin
    public async Task<ApiResponseDto<int>> UpdatePromotionFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto)
    {
        return await httpClient.PutJsonAsync<ApiResponseDto<int>>($"{BaseUrl}/front-margin/{number}", updateDto);
    }
    public async Task<ApiResponseDto> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto>($"{BaseUrl}/front-margin", createDto);
    }

    public async Task<ApiResponseDto> DeletePromotionFrontMarginAsync(List<string> numbers)
    {
        return await httpClient.SendJsonAsync<ApiResponseDto>(HttpMethod.Delete, $"{BaseUrl}/front-margin", numbers);
    }

    /// <summary>
    /// Get available Front Margin promotions for vendor
    /// </summary>
    public async Task<ApiResponseDto<List<GetPromotionFrontMarginDto>>> GetAvailableFrontMarginsAsync(string vendorCode)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<List<GetPromotionFrontMarginDto>>>(
            $"api/FrontMargin/available/{vendorCode}");
    }

    /// <summary>
    /// Get Front Margin discount for specific vendor and item
    /// </summary>
    public async Task<ApiResponseDto<GetPromotionFrontMarginDto>> GetFrontMarginDiscountAsync(string vendorCode, string itemNumber)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<GetPromotionFrontMarginDto>>(
            $"api/FrontMargin/discount/{vendorCode}/{itemNumber}");
    }

    /// <summary>
    /// Check if Front Margin is applicable for PO line
    /// </summary>
    public async Task<ApiResponseDto<bool>> IsFrontMarginApplicableAsync(string vendorCode, string itemNumber, DateTime orderDate)
    {
        return await httpClient.GetFromJsonAsync<ApiResponseDto<bool>>(
            $"api/FrontMargin/applicable/{vendorCode}/{itemNumber}?orderDate={orderDate:yyyy-MM-dd}");
    }

    /// <summary>
    /// Preview Front Margin impact on PO
    /// </summary>
    public async Task<ApiResponseDto<object>> PreviewFrontMarginImpactAsync(object poData)
    {
        return await httpClient.PostJsonAsync<ApiResponseDto<object>>(
            "api/POFrontMargin/preview", poData);
    }
    #endregion
}
