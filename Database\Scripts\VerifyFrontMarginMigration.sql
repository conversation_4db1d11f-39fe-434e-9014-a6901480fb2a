-- =============================================
-- Verify Front Margin Migration Script
-- Execute this to verify migration was successful
-- Date: 2025-01-11
-- =============================================

USE [PurchaseManager] -- Change to your database name
GO

PRINT 'Verifying Front Margin Migration...'
PRINT 'Database: ' + DB_NAME()
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
PRINT '============================================='

-- Check if table exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionFrontMargins')
BEGIN
    PRINT '❌ ERROR: PromotionFrontMargins table does not exist!'
    RETURN
END
ELSE
    PRINT '✅ PromotionFrontMargins table exists'

-- Check all required columns
DECLARE @MissingColumns TABLE (ColumnName VARCHAR(50))

INSERT INTO @MissingColumns (ColumnName)
SELECT 'DiscountType' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'DiscountType')
UNION ALL
SELECT 'FixedDiscountAmount' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'FixedDiscountAmount')
UNION ALL
SELECT 'BuyQuantity' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'BuyQuantity')
UNION ALL
SELECT 'GiftQuantity' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftQuantity')
UNION ALL
SELECT 'GiftItemNumber' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftItemNumber')
UNION ALL
SELECT 'GiftItemName' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftItemName')
UNION ALL
SELECT 'GiftItemUOM' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftItemUOM')
UNION ALL
SELECT 'GiftItemQuantity' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'GiftItemQuantity')
UNION ALL
SELECT 'MinimumQuantity' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'MinimumQuantity')
UNION ALL
SELECT 'MinimumAmount' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'MinimumAmount')
UNION ALL
SELECT 'MaximumDiscountAmount' WHERE NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'MaximumDiscountAmount')

IF EXISTS (SELECT * FROM @MissingColumns)
BEGIN
    PRINT '❌ Missing columns:'
    SELECT ColumnName FROM @MissingColumns
END
ELSE
    PRINT '✅ All required columns exist'

-- Check column data types
PRINT ''
PRINT 'Column specifications:'
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    CASE 
        WHEN DATA_TYPE IN ('decimal', 'numeric') THEN CAST(NUMERIC_PRECISION AS VARCHAR(10)) + ',' + CAST(NUMERIC_SCALE AS VARCHAR(10))
        WHEN DATA_TYPE IN ('varchar', 'nvarchar', 'char', 'nchar') THEN CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR(10))
        ELSE ''
    END as Specification,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionFrontMargins'
AND COLUMN_NAME IN ('DiscountType', 'FixedDiscountAmount', 'BuyQuantity', 'GiftQuantity', 
                   'GiftItemNumber', 'GiftItemName', 'GiftItemUOM', 'GiftItemQuantity',
                   'MinimumQuantity', 'MinimumAmount', 'MaximumDiscountAmount', 'DiscountPercentage')
ORDER BY ORDINAL_POSITION

-- Check constraints
PRINT ''
PRINT 'Constraints:'
SELECT
    CONSTRAINT_NAME,
    CONSTRAINT_TYPE
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_NAME = 'PromotionFrontMargins'
AND CONSTRAINT_NAME LIKE '%DiscountType%'

-- Check indexes
PRINT ''
PRINT 'Indexes:'
SELECT
    i.name as IndexName,
    i.type_desc as IndexType,
    c.name as ColumnName
FROM sys.indexes i
INNER JOIN sys.index_columns ic ON i.object_id = ic.object_id AND i.index_id = ic.index_id
INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
WHERE i.object_id = OBJECT_ID('PromotionFrontMargins')
AND i.name LIKE 'IX_PromotionFrontMargins_%'
ORDER BY i.name

-- Check data distribution
PRINT ''
PRINT 'Data distribution by DiscountType:'
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'DiscountType')
BEGIN
    SELECT
        DiscountType,
        CASE DiscountType
            WHEN 1 THEN 'Percentage Discount'
            WHEN 2 THEN 'Fixed Amount Discount'
            WHEN 3 THEN 'Same Item Gift'
            WHEN 4 THEN 'Different Item Gift'
            ELSE 'Unknown'
        END as DiscountTypeName,
        COUNT(*) as RecordCount
    FROM PromotionFrontMargins
    GROUP BY DiscountType
    ORDER BY DiscountType
END
ELSE
    PRINT 'DiscountType column not found'

-- Summary
PRINT ''
PRINT '============================================='
PRINT 'Migration Verification Summary:'

DECLARE @TotalColumns INT
SELECT @TotalColumns = COUNT(*)
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionFrontMargins'

DECLARE @RequiredColumns INT = 11 -- Number of new columns we added
DECLARE @NewColumns INT
SELECT @NewColumns = COUNT(*)
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionFrontMargins'
AND COLUMN_NAME IN ('DiscountType', 'FixedDiscountAmount', 'BuyQuantity', 'GiftQuantity', 
                   'GiftItemNumber', 'GiftItemName', 'GiftItemUOM', 'GiftItemQuantity',
                   'MinimumQuantity', 'MinimumAmount', 'MaximumDiscountAmount')

DECLARE @Constraints INT
SELECT @Constraints = COUNT(*)
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_NAME = 'PromotionFrontMargins'
AND CONSTRAINT_NAME LIKE '%DiscountType%'

DECLARE @Indexes INT
SELECT @Indexes = COUNT(*)
FROM sys.indexes
WHERE object_id = OBJECT_ID('PromotionFrontMargins')
AND name LIKE 'IX_PromotionFrontMargins_%'

PRINT 'Total columns: ' + CAST(@TotalColumns AS VARCHAR(10))
PRINT 'New columns added: ' + CAST(@NewColumns AS VARCHAR(10)) + '/' + CAST(@RequiredColumns AS VARCHAR(10))
PRINT 'DiscountType constraints: ' + CAST(@Constraints AS VARCHAR(10))
PRINT 'New indexes: ' + CAST(@Indexes AS VARCHAR(10))

IF @NewColumns = @RequiredColumns AND @Constraints > 0 AND @Indexes > 0
    PRINT '✅ Migration verification PASSED!'
ELSE
    PRINT '❌ Migration verification FAILED!'

PRINT 'Verification completed at: ' + CONVERT(varchar, GETDATE(), 120)
