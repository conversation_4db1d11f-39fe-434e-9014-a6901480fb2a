using PurchaseManager.Infrastructure.Server;
using Quartz;
using SilkierQuartz;
namespace PurchaseManager.Server.Jobs;

/// <summary>
///     Auto create a Receiving Order (RO) by PO.
/// </summary>
[SilkierQuartz(50000, Group = "UPDATE_STATUS_PO", TriggerDescription = "UPDATE_STATUS_PO", TriggerGroup = "PO_GR")]
public class AutoUpdateStatusPO : IJob
{
    private readonly ICJLGRManager _cjlGRManager;
    public AutoUpdateStatusPO(ICJLGRManager cjlGRManager)
    {
        _cjlGRManager = cjlGRManager;
    }
    public async Task Execute(IJobExecutionContext context)
    {
        await _cjlGRManager.UpdatePOStatusByGRDataAsync();
        Console.WriteLine("AutoUpdateStatusPO job executed successfully at " + DateTime.Now);
    }
}
