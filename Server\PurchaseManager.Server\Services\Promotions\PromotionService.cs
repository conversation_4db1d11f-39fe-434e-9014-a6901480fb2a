﻿using AutoMapper;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Shared.Dto.Item;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Localizer;
using static Microsoft.AspNetCore.Http.StatusCodes;

namespace PurchaseManager.Server.Services.Promotions;

public class PromotionService : IPromotionService
{
    private readonly IPromotionManager _promotionManager;
    private readonly IStringLocalizer<Global> _i18N;
    private readonly IItemManager _itemManager;
    private readonly IMapper _mapper;
    private readonly IAdminManager _adminManager;
    public PromotionService(IPromotionManager promotionManager, IStringLocalizer<Global> i18N, IAdminManager adminManager,
        IItemManager itemManager, IMapper mapper)
    {
        _promotionManager = promotionManager;
        _i18N = i18N;
        _adminManager = adminManager;
        _itemManager = itemManager;
        _mapper = mapper;
    }

    public async Task<ApiResponse> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto)
    {
        var validationResult = await ValidatePromotionFrontMarginAsync(createDto);
        if (!validationResult.IsSuccessStatusCode)
        {
            return validationResult;
        }

        return await _promotionManager.CreatePromotionFrontMarginAsync(createDto);
    }
    public async Task<ApiResponse> UpdatePromotionFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto)
    {
        var poHeader = await _promotionManager.GetPromotionFrontMarginByNumberAsync(number);
        if (!poHeader.IsSuccessStatusCode)
        {
            return poHeader;
        }

        var statusLine = await _promotionManager.IsDocumentLockedByAnotherUserFrontMarginAsync(number);
        if (!statusLine.IsSuccessStatusCode)
        {
            return statusLine;
        }

        return await _promotionManager.UpdatePromotionFrontMarginAsync(number, updateDto);
    }
    public async Task<ApiResponse> OpenDocument(string documentNumber)
    {
        try
        {
            var loginId = _adminManager.GetUserLogin();
            if (loginId is null)
            {
                return new ApiResponse(Status400BadRequest, _i18N["LoginID is null (must Login)"]);
            }
            if (string.IsNullOrEmpty(documentNumber))
            {
                return ApiResponse.S404(_i18N["DocumentNumber is null"]);
            }

            var notification = await _promotionManager.IsDocumentLockedByAnotherUserAsync(documentNumber);
            if (!notification.IsSuccessStatusCode)
            {
                return notification;
            }

            var openDocument = await _promotionManager.OpenDocumentAsync(documentNumber);
            return !openDocument.IsSuccessStatusCode
                ? openDocument
                : new ApiResponse(Status200OK, _i18N["Open PO successful"]);
        }
        catch (Exception ex)
        {
            return ApiResponse.S500(ex.GetBaseException()
                .Message);
        }
    }

    private async Task<ApiResponse> ValidatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto promotionFrontMarginDto)
    {
        var poHeader = await _promotionManager.GetPromotionByNumberAsync(promotionFrontMarginDto.ProgramNumber);
        if (!poHeader.IsSuccessStatusCode)
        {
            return poHeader;
        }

        var statusLine = await _promotionManager.IsDocumentLockedByAnotherUserAsync(promotionFrontMarginDto.ProgramNumber);
        if (!statusLine.IsSuccessStatusCode)
        {
            return statusLine;
        }

        var item = await _itemManager.GetItem(promotionFrontMarginDto.ItemNumber);
        if (!item.IsSuccessStatusCode)
        {
            return item;
        }

        var itemDto = _mapper.Map<DetailItemDto>(item.Result);
        if (itemDto.Status < 2)
        {
            return new ApiResponse(Status404NotFound, "ItemNumber is editing");
        }

        if (itemDto.Blocked == 1)
        {
            return new ApiResponse(Status404NotFound, "ItemNumber is blocked");
        }

        if (itemDto.ItemUnitOfMeasures != null)
        {
            var uom = itemDto.ItemUnitOfMeasures
                .FirstOrDefault(x => string.Equals(x.Code, promotionFrontMarginDto.UnitOfMeasure, StringComparison.OrdinalIgnoreCase));

            if (uom == null)
            {
                return new ApiResponse(Status404NotFound, "UOM is not correct");
            }

            if (uom.Block == 1)
            {
                return new ApiResponse(Status404NotFound, "UOM is blocked");
            }
        }

        return ApiResponse.S200();

    }
}
