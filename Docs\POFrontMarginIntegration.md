# PO Front Margin Integration Documentation

## Overview

PO Front Margin Integration provides seamless integration between the Purchase Order system and Front Margin promotions. This system automatically applies Front Margin discounts to POs, manages gift items, and provides comprehensive tracking and reporting.

## Architecture

### Components

1. **POFrontMarginIntegrationManager** - Core business logic for PO integration
2. **POIntegrationController** - REST API endpoints for integration
3. **POFrontMarginEventHandler** - Event-driven processing for PO changes
4. **POFrontMarginEventProcessor** - Background service for event processing

### Integration Flow

```
PO Creation/Modification → Event Handler → Integration Manager → Front Margin Services → Updated PO
```

## Key Features

### 1. Automatic Front Margin Application
- **Auto-Apply**: Automatically applies Front Margin when PO is created
- **Real-time Calculation**: Instant discount calculation and application
- **Gift Item Management**: Automatic addition of gift lines
- **Validation**: Comprehensive validation before application

### 2. Event-Driven Processing
- **PO Created**: Auto-apply Front Margin to new POs
- **PO Modified**: Recalculate Front Margin when <PERSON><PERSON> changes
- **Line Added**: Apply Front Margin to new lines
- **Vendor Changed**: Update Front Margin for new vendor

### 3. Comprehensive Management
- **Preview**: Preview impact before applying
- **Validation**: Validate PO eligibility
- **Recalculation**: Recalculate existing Front Margin
- **Removal**: Remove Front Margin from PO
- **Statistics**: Vendor usage statistics and trends

## API Endpoints

### Base URL: `/api/po-integration`

#### Core Integration Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/apply-front-margin` | Apply Front Margin to PO |
| POST | `/preview-front-margin` | Preview Front Margin impact |
| POST | `/validate-front-margin` | Validate PO for Front Margin |
| DELETE | `/remove-front-margin/{poNumber}` | Remove Front Margin from PO |
| POST | `/auto-apply-front-margin` | Auto-apply based on settings |

#### Management Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/front-margin-summary/{poNumber}` | Get Front Margin summary for PO |
| POST | `/recalculate-front-margin/{poNumber}` | Recalculate Front Margin |
| GET | `/vendor-stats/{vendorCode}` | Get vendor usage statistics |
| POST | `/bulk-apply-front-margin` | Bulk apply to multiple POs |

#### Configuration Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/settings/{vendorCode}` | Get vendor integration settings |
| PUT | `/settings/{vendorCode}` | Update vendor integration settings |
| GET | `/dashboard` | Get integration dashboard data |
| GET | `/health` | Get integration health status |

## Usage Examples

### 1. Apply Front Margin to PO

```http
POST /api/po-integration/apply-front-margin
Content-Type: application/json

{
  "poHeader": {
    "poNumber": "*********",
    "buyFromVendorNumber": "VENDOR001",
    "orderDate": "2025-01-11T00:00:00Z"
  },
  "poLines": [
    {
      "lineNumber": 1,
      "itemNumber": "ITEM001",
      "description": "Test Item 1",
      "quantity": 20,
      "unitCost": 10000,
      "unitOfMeasure": "PCS",
      "amount": 200000
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Front Margin applied successfully to PO",
  "data": {
    "poNumber": "*********",
    "originalAmount": 200000,
    "finalAmount": 170000,
    "totalSavings": 30000,
    "discountPercentage": 15.0,
    "updatedLines": 1,
    "giftLines": 0,
    "appliedPromotions": 1
  }
}
```

### 2. Preview Front Margin Impact

```http
POST /api/po-integration/preview-front-margin
Content-Type: application/json

{
  "poHeader": {
    "poNumber": "*********",
    "buyFromVendorNumber": "VENDOR001"
  },
  "poLines": [
    {
      "lineNumber": 1,
      "itemNumber": "ITEM001",
      "quantity": 15,
      "unitCost": 10000
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Front Margin impact preview generated",
  "data": {
    "poNumber": "*********",
    "vendorCode": "VENDOR001",
    "summary": {
      "totalOriginalAmount": 150000,
      "totalProjectedSavings": 22500,
      "projectedDiscountPercentage": 15.0,
      "linesWithPromotions": 1,
      "totalApplicablePromotions": 1
    },
    "lineDetails": [
      {
        "lineNumber": 1,
        "itemNumber": "ITEM001",
        "originalAmount": 150000,
        "projectedFinalAmount": 127500,
        "projectedSavings": 22500,
        "hasPromotion": true
      }
    ]
  }
}
```

### 3. Get Vendor Statistics

```http
GET /api/po-integration/vendor-stats/VENDOR001?fromDate=2024-10-01&toDate=2025-01-11
```

**Response:**
```json
{
  "success": true,
  "message": "Front Margin statistics for vendor VENDOR001",
  "data": {
    "vendorCode": "VENDOR001",
    "period": {
      "fromDate": "2024-10-01",
      "toDate": "2025-01-11"
    },
    "summary": {
      "totalPOs": 25,
      "totalOriginalAmount": 5000000,
      "totalSavings": 750000,
      "averageDiscountPercentage": 15.0,
      "totalPromotionsUsed": 45
    },
    "monthlyTrends": [
      {
        "year": 2024,
        "month": 10,
        "poCount": 8,
        "totalSavings": 240000
      }
    ]
  }
}
```

## Event Handling

### PO Events

The system automatically handles the following PO events:

#### 1. PO Created Event
```csharp
await _eventHandler.HandlePOCreatedAsync(poHeader, poLines);
```
- Automatically applies Front Margin if auto-apply is enabled
- Validates PO eligibility
- Logs application results

#### 2. PO Modified Event
```csharp
await _eventHandler.HandlePOModifiedAsync(poHeader, poLines);
```
- Recalculates existing Front Margin
- Updates discount amounts
- Adjusts gift lines if needed

#### 3. PO Line Added Event
```csharp
await _eventHandler.HandlePOLineAddedAsync(poHeader, newLine);
```
- Checks new line for Front Margin eligibility
- Applies Front Margin to new line if applicable
- Updates PO totals

#### 4. Vendor Changed Event
```csharp
await _eventHandler.HandleVendorChangedAsync(poHeader, oldVendor, newVendor);
```
- Removes old vendor's Front Margin
- Applies new vendor's Front Margin
- Updates all affected lines

## Configuration

### Vendor Integration Settings

Each vendor can have specific integration settings:

```json
{
  "vendorCode": "VENDOR001",
  "autoApplyEnabled": true,
  "requireApproval": false,
  "maxDiscountPercentage": 50.0,
  "allowGiftItems": true,
  "notificationSettings": {
    "emailOnApply": false,
    "emailOnLargeDiscount": true,
    "largeDiscountThreshold": 20.0
  }
}
```

### System Settings

Global integration settings:

- **Auto-Apply Default**: Enable/disable auto-apply by default
- **Approval Threshold**: Discount percentage requiring approval
- **Gift Item Limit**: Maximum gift items per PO
- **Notification Settings**: Email and alert configurations

## Integration Patterns

### 1. Manual Integration

```csharp
// Apply Front Margin manually
var result = await _integrationManager.ApplyFrontMarginToPOAsync(poHeader, poLines);

if (result.IsSuccessStatusCode)
{
    // Update PO with new amounts
    UpdatePOWithFrontMargin(result.Data);
}
```

### 2. Event-Driven Integration

```csharp
// Register event handlers
services.AddScoped<IPOFrontMarginEventHandler, POFrontMarginEventHandler>();
services.AddHostedService<POFrontMarginEventProcessor>();

// Trigger events
await _eventHandler.HandlePOCreatedAsync(poHeader, poLines);
```

### 3. Background Processing

```csharp
// Background service processes events
public class POFrontMarginEventProcessor : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await ProcessQueuedEventsAsync();
            await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
        }
    }
}
```

## Error Handling

### Common Error Scenarios

1. **No Applicable Promotions**
   - Returns success with zero savings
   - Provides recommendations for better coverage

2. **Validation Failures**
   - Returns validation errors with specific messages
   - Suggests corrective actions

3. **Calculation Errors**
   - Logs detailed error information
   - Returns safe fallback values

4. **System Errors**
   - Comprehensive error logging
   - Graceful degradation

### Error Response Format

```json
{
  "success": false,
  "message": "Validation failed",
  "data": {
    "validationErrors": [
      "PO must have a vendor to apply Front Margin"
    ],
    "warnings": [
      "No Front Margin promotions available for this vendor"
    ]
  }
}
```

## Performance Considerations

### Optimization Strategies

1. **Caching**: Promotion data cached for fast access
2. **Background Processing**: Events processed asynchronously
3. **Batch Operations**: Bulk apply for multiple POs
4. **Lazy Loading**: Load promotion details only when needed

### Performance Metrics

- **Average Response Time**: <100ms for single PO
- **Bulk Processing**: 50 POs per second
- **Cache Hit Ratio**: >90% for active promotions
- **Memory Usage**: <50MB for typical workload

## Monitoring and Logging

### Key Metrics

- **Application Success Rate**: % of successful Front Margin applications
- **Average Savings**: Average discount amount per PO
- **Processing Time**: Time to apply Front Margin
- **Error Rate**: % of failed operations

### Log Levels

- **Information**: Successful operations and key events
- **Warning**: Non-critical issues and recommendations
- **Error**: Failed operations and system errors
- **Debug**: Detailed processing information

## Testing

### Unit Tests
- **POFrontMarginIntegrationManagerTests**: Manager logic tests
- **POIntegrationControllerTests**: API endpoint tests
- **POFrontMarginEventHandlerTests**: Event handling tests

### Integration Tests
- **POFrontMarginIntegrationTests**: End-to-end integration tests
- **Performance Tests**: Load and stress testing
- **Event Processing Tests**: Event handling validation

### Test Data
- **Sample POs**: Various PO scenarios for testing
- **Mock Promotions**: Test promotion configurations
- **Edge Cases**: Boundary and error conditions

## Deployment

### Prerequisites
- Front Margin system deployed and configured
- Database migrations completed
- Cache service running
- Event processing enabled

### Deployment Steps
1. Deploy integration services
2. Configure vendor settings
3. Enable event handlers
4. Start background services
5. Verify integration health

### Health Checks
- **Service Health**: All services responding
- **Database Connectivity**: Database accessible
- **Cache Performance**: Cache hit ratio acceptable
- **Event Processing**: Events being processed

## Troubleshooting

### Common Issues

1. **Front Margin Not Applied**
   - Check vendor has active promotions
   - Verify PO meets minimum requirements
   - Check auto-apply settings

2. **Incorrect Calculations**
   - Verify promotion configuration
   - Check calculation engine logs
   - Validate input data

3. **Performance Issues**
   - Monitor cache performance
   - Check database query performance
   - Review event processing backlog

### Support Tools

- **Integration Dashboard**: Real-time status and metrics
- **Health Check Endpoint**: System health verification
- **Log Analysis**: Detailed operation logs
- **Performance Metrics**: Response time and throughput data

---

**PO Front Margin Integration is ready for production use! 🚀**
