﻿@using PurchaseManager.Shared.Models.Notification
@inherits RootLayout
@inject NavigationManager navigationManager
@inject AppState appState
@inject IStringLocalizer<Global> L

<CascadingValue Value="this">
    <MudLayout>
        <MudAppBar Elevation="0">
            <MudIconButton Icon="@Icons.Material.Filled.Menu" Class="mr-2" Edge="Edge.Start"
                OnClick="@((e) => DrawerToggle())" />
            <MudText Typo="Typo.h6">PO Manager</MudText>
            <MudMenu Label="Apps" LockScroll="true" Variant="Variant.Text" PopoverClass="docs-layout-menu-shadow"
                Class="mx-3" ListClass="d-flex px-4 pb-2 docs-appbar-special-menu"
                EndIcon="@Icons.Material.Filled.KeyboardArrowDown" AnchorOrigin="Origin.BottomCenter"
                TransformOrigin="Origin.TopCenter">
                <MudList T="string">
                    <MudListSubheader>
                        System
                    </MudListSubheader>
                    <MudListItem Href="/">
                        <div class="d-flex">
                            <MudText>TS</MudText>
                            <MudText Color="Color.Primary">HRM</MudText>
                        </div>
                        <MudText Typo="Typo.body2">Human Resource Manager</MudText>
                    </MudListItem>
                    <MudListItem Href="/">
                        <div class="d-flex">
                            <MudText>Oracle</MudText>
                            <MudText Color="Color.Error"> ERP</MudText>
                        </div>
                        <MudText Typo="Typo.body2">Oralce ERP Module F&A</MudText>
                    </MudListItem>
                    <MudListItem Href="/">
                        <div class="d-flex">
                            <MudText Color="Color.Default">Oracle BI</MudText>
                        </div>
                        <MudText Typo="Typo.body2">Oralce Bussines Analytics</MudText>
                    </MudListItem>
                </MudList>
                <MudList T="string" Class="relative">
                    <MudListSubheader>
                        Office
                    </MudListSubheader>
                    <MudListItem>
                        <div class="d-flex">
                            <MudText>TS</MudText>
                            <MudText Color="Color.Warning">Groupware</MudText>
                        </div>
                        <MudText Typo="Typo.body2">Approval, Email, Schedule..</MudText>
                    </MudListItem>
                    <MudListItem>
                        <div class="d-flex">
                            <MudText Color="Color.Default">TSLearning</MudText>
                        </div>
                        <MudText Typo="Typo.body2">Learn, training and more..</MudText>
                    </MudListItem>
                    <MudListItem>
                        <div class="d-flex">
                            <MudText>TS</MudText>
                            <MudText Color="Color.Default">Suppert</MudText>
                        </div>
                        <MudText Typo="Typo.body2">Support ERP, POS,..</MudText>
                    </MudListItem>
                </MudList>
            </MudMenu>
            <MudSpacer />
            <NotificationMenu Class="mx-3" Notifications="_activeNotifications" />
            <TopRightBarSection />
            <UserProfile />
        </MudAppBar>
        <MudDrawer @bind-Open="@_navMenuOpened" Fixed="true" Variant="@DrawerVariant.Persistent"
            ClipMode="DrawerClipMode.Never" Class="mt-5" Elevation="0">
            <div class="mb-6">
                <MudStack Row="true" AlignItems="AlignItems.Center" Justify="Justify.Center">
                    <MudImage Src="images/logo/logo-trungson.png" Class="mx-3 rounded-lg" Width="50"
                        ObjectFit="ObjectFit.Cover" ObjectPosition="ObjectPosition.Center" Alt="trung son pharma" />
                    @* <MudImage Src="images/logo/logo-dongwha.png" Class="mx-3 rounded-lg" Alt="trung son pharma" ObjectFit="ObjectFit.Cover" ObjectPosition="ObjectPosition.Center" Width="50"/> *@
                </MudStack>
            </div>
            <NavMenu />
        </MudDrawer>
        <MudMainContent Class="mudblazor-main-content">
            <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Style="min-height: 86vh;">
                @Body
            </MudContainer>
        </MudMainContent>
        <MudScrollToTop TopOffset="400" Style="z-index:2000;">
            <MudFab EndIcon="@Icons.Material.Filled.KeyboardArrowUp" Color="Color.Primary" />
        </MudScrollToTop>
    </MudLayout>
</CascadingValue>
<Version />

@code {
    bool _navMenuOpened = true;
    private IEnumerable<NotificationModel> _activeNotifications;
    [CascadingParameter] Task<AuthenticationState> authenticationStateTask { get; set; }

    protected override async Task OnInitializedAsync()
    {
        var user = (await authenticationStateTask).User;

        if (user.Identity.IsAuthenticated)
        {
            var profile = await appState.GetUserProfile();
            _navMenuOpened = profile.IsNavOpen;
        }
    }

    private void DrawerToggle()
    {
        _navMenuOpened = !_navMenuOpened;
    }

}