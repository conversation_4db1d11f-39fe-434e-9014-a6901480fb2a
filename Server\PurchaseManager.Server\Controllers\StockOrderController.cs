﻿using Microsoft.AspNetCore.Mvc;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Models.StockOrder;
namespace PurchaseManager.Server.Controllers;

[SecurityHeaders]
[Route("api/[controller]")]
[ApiController]
public class StockOrderController : ControllerBase
{
    private readonly IStockOrderManager _stockOrderManager;

    public StockOrderController(IStockOrderManager stockOrderManager)
    {
        _stockOrderManager = stockOrderManager;
    }

    [HttpGet("Gets")]
    public async Task<ApiResponse> GetStockOrderByPoHeader([FromQuery] StockOrderFilter filter)
        => await _stockOrderManager.GetReceiveLotsByPoHeaderAsync(filter);

    [HttpPost("Create")]
    public async Task<ApiResponse> CreateStockOrder([FromBody] CreateStockOrderDto stockOrder)
        => await _stockOrderManager.CreateReceiveLotsAsync(stockOrder);
}
