﻿using AutoMapper;
using Karambolo.Common;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Localization;
using MudBlazor;
using NetTopologySuite.Utilities;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Dto.StockOrder;
using PurchaseManager.Shared.Dto.User;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Models.StockOrder;
using PurchaseManager.Shared.Providers;
namespace PurchaseManager.Theme.Material.Demo.Pages.StockOrder;

public partial class StockOrderDetails : ComponentBase
{
    [Parameter]
    public string DocumentNo { get; set; }
    [Parameter]
    public string DocumentStatus { get; set; } = string.Empty;
    [Inject]
    private IVendorApiClient VendorApiClient { get; set; } = default!;
    [Inject]
    public IPurchaseOrderApiClient PurchaseOrderApiClient { get; set; } = default!;
    [Inject]
    public IStockOrderApiClient StockOrderApiClient { get; set; } = default!;
    private bool IsProcessing { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    [Inject]
    protected HttpClient HttpClient { get; set; }
    [Inject]
    protected IStringLocalizer<Global> L { get; set; }
    [Inject]
    protected IApiClient ApiClient { get; set; }
    [Inject]
    protected IMapper Mapper { get; set; }
    [Inject]
    protected NavigationManager NavigationManager { get; set; }
    private List<POLineGetDto> PoLines { get; set; } = [];
    private List<POLineGetDto> OriginalPoLines { get; set; } = [];
    private bool Validate { get; set; }
    [Inject]
    private AuthenticationStateProvider AuthStateProvider { get; set; }
    private POHeaderGetDto PoHeader { get; set; } = new POHeaderGetDto();
    private GetVendorDto VendorInfo { get; set; } = new GetVendorDto();
    private bool IsEdit { get; set; }
    [Inject]
    protected IDialogService DialogService { get; set; }
    private DateTime? ExpirationDate { get; set; }
    protected UserInfoDto UserCreatedPO { get; set; } = new UserInfoDto();
    private int TotalItems { get; set; }
    private int PageIndex { get; set; }
    private int PageSize { get; set; } = 10;
    private List<GetStockOrderDto> ListStockOrderDtos { get; set; } = [];

    protected override async Task OnInitializedAsync()
    {
        IsProcessing = true;
        await GetDetailPo(DocumentNo);
        await GetHeaderPo(DocumentNo);
        await GetUserCreatePOAsync();
        await ((IdentityAuthenticationStateProvider)AuthStateProvider).GetUserViewModel();
        IsProcessing = false;
        await base.OnInitializedAsync();
    }
    private async Task GetDetailPo(string number)
    {
        if (number.IsNullOrEmpty())
        {
            ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
        }
        try
        {
            var apiResponse = await PurchaseOrderApiClient.GetLines(DocumentNo);

            if (!apiResponse.IsSuccessStatusCode)
            {
                ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
            }

            if (apiResponse.Result != null)
            {
                PoLines =
                [
                    ..apiResponse.Result.OrderByDescending(x => x.LineNumber)
                ];
                OriginalPoLines =
                [
                    ..apiResponse.Result.OrderByDescending(x => x.LineNumber)
                ];
            }
            StateHasChanged();
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
        }
    }
    private void OnArrowClick(POLineGetDto item)
    {
        item.TempReceive = Convert.ToInt32(item.QuantityToReceive);
        item.IsArrowDisabled = true;
        Validate = false;
    }
    private async Task GetHeaderPo(string number)
    {
        if (string.IsNullOrEmpty(number))
        {
            ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
        }
        else
        {
            try
            {
                var apiResponse = await PurchaseOrderApiClient.GetHeader(DocumentNo);

                if (!apiResponse.IsSuccessStatusCode)
                {
                    ViewNotifier.Show(L["Not Found"], ViewNotifierType.Error);
                    return;
                }
                PoHeader = apiResponse.Result;
                if (PoHeader != null)
                {
                    var getVendorByVendorNumber = await VendorApiClient.GetSingleVendor(PoHeader.BuyFromVendorNumber);

                    if (!getVendorByVendorNumber.IsSuccessStatusCode)
                    {
                        ViewNotifier.Show(L["Vendor Not Found"], ViewNotifierType.Error);
                    }
                    else
                    {
                        VendorInfo = getVendorByVendorNumber.Result;
                    }
                }
                StateHasChanged();
            }
            catch (Exception e)
            {
                ViewNotifier.Show(e.ToString(), ViewNotifierType.Error);
            }
        }
    }
    private async Task SaveChangeHeaderPoOrOpenPo(bool isChange)
    {
        try
        {
            if (isChange && Validate == false)
            {
                var result = await DialogService.ShowMessageBox(
                "Confirm",
                "Are you sure you want to save changes?",
                "Saved", cancelText: "Cancel");
                if (result != null)
                {
                    var allUpdatesSuccessful = true;
                    var errorMessage = string.Empty;
                    foreach (var line in PoLines)
                    {
                        var originalLine = OriginalPoLines.FirstOrDefault(l => l.LineNumber == line.LineNumber);

                        var hasChanges = line.QuantityReceived != originalLine!.QuantityReceived ||
                                         line.QuantityToReceive != originalLine.QuantityToReceive ||
                                         !string.Equals(line.UnitOfMeasure, originalLine.UnitOfMeasure,
                                         StringComparison.CurrentCultureIgnoreCase) ||
                                         line.LotNo != originalLine.LotNo ||
                                         line.ExpirationDate != originalLine.ExpirationDate ||
                                         line.TempReceive != 0;

                        if (!hasChanges)
                        {
                            continue;
                        }

                        var checkLotNo = line.LotNo.IsNullOrEmpty() ||
                                         string.Equals(line.LotNo, "notset", StringComparison.OrdinalIgnoreCase);
                        if (checkLotNo)
                        {
                            ViewNotifier.Show("Lot No is not null or default value", ViewNotifierType.Error);
                            return;
                        }

                        var checkExpiredDate = line.ExpirationDate == null || ExpirationDate == null;
                        if (checkExpiredDate)
                        {
                            ViewNotifier.Show("Expiration Date is not null.", ViewNotifierType.Error);
                            return;
                        }
                        var requestAddOrUpdate = Mapper.Map<POLineAddOrUpdate>(line);
                        requestAddOrUpdate.QuantityReceived += line.TempReceive;
                        requestAddOrUpdate.QuantityToReceive = Math.Max(0, requestAddOrUpdate.QuantityToReceive - line.TempReceive);
                        requestAddOrUpdate.UnitOfMeasure = requestAddOrUpdate.UnitOfMeasure.ToUpper();

                        var apiResponse = await PurchaseOrderApiClient.UpdatePurchaseOrderLine(requestAddOrUpdate);

                        if (apiResponse.IsSuccessStatusCode)
                        {
                            var stockOrder = new CreateStockOrderDto
                            {
                                DocumentType = line.DocumentType,
                                LineNumber = line.LineNumber,
                                LotNo = line.LotNo,
                                ExpirationDate = line.ExpirationDate ?? DateOnly.FromDateTime(DateTime.Now),
                                QuantityReceived = line.TempReceive,
                                HeaderNumber = line.DocumentNumber,
                                ItemNumber = line.ItemNumber,
                                TotalQuantity = (int)line.Quantity,
                                ItemName = line.ItemName
                            };
                            var createStockOrder = await StockOrderApiClient.CreateMultipleStockOrder(stockOrder);
                            if (!createStockOrder.IsSuccessStatusCode)
                            {
                                ViewNotifier.Show(createStockOrder.Message, ViewNotifierType.Error);
                                return;
                            }
                            continue;
                        }
                        allUpdatesSuccessful = false;
                        errorMessage = apiResponse.Message;
                        break;
                    }

                    if (allUpdatesSuccessful)
                    {
                        await GetDetailPo(DocumentNo);
                        var sumQuantity = PoLines.Sum(x => x.Quantity);
                        var sumQuantityReceived = PoLines.Sum(x => x.QuantityReceived);
                        if (sumQuantity == sumQuantityReceived)
                        {
                            var updateHeader = await PurchaseOrderApiClient.CompletedHeader(DocumentNo, true);

                            if (!updateHeader.IsSuccessStatusCode)
                            {
                                ViewNotifier.Show(updateHeader.Message, ViewNotifierType.Error);
                                return;
                            }

                        }
                        if (sumQuantity != sumQuantityReceived)
                        {
                            var updateHeader = await PurchaseOrderApiClient.CompletedHeader(DocumentNo, false);

                            if (!updateHeader.IsSuccessStatusCode)
                            {
                                ViewNotifier.Show(updateHeader.Message, ViewNotifierType.Error);
                                return;
                            }
                        }
                        //Close Document
                        var closeDocument = await PurchaseOrderApiClient.CloseDocument(DocumentNo);

                        if (!closeDocument.IsSuccessStatusCode)
                        {
                            ViewNotifier.Show(closeDocument.Message, ViewNotifierType.Error);
                            return;
                        }

                        IsEdit = false;
                        ViewNotifier.Show(L["Update purchase orders details success"], ViewNotifierType.Success);
                        return;
                    }
                    ViewNotifier.Show(errorMessage, ViewNotifierType.Error);
                    StateHasChanged();
                }
            }
            else
            {
                await ClosedPo();
                IsEdit = false;
            }
            await GetDetailPo(DocumentNo);
            StateHasChanged();
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
    }
    private async Task OpenedPo()
    {
        var openDocument = await PurchaseOrderApiClient.OpenDocument(DocumentNo);

        if (!openDocument.IsSuccessStatusCode)
        {
            ViewNotifier.Show(openDocument.Message, ViewNotifierType.Error);
            return;
        }
        IsEdit = true;
        ViewNotifier.Show($"PO:{DocumentNo} is opened", ViewNotifierType.Success);
        // IsEdit = false;
    }
    private async Task ClosedPo()
    {
        var closeDocument = await PurchaseOrderApiClient.CloseDocument(DocumentNo);

        if (!closeDocument.IsSuccessStatusCode)
        {
            ViewNotifier.Show(closeDocument.Message, ViewNotifierType.Error);
            return;
        }
        IsEdit = false;
        ViewNotifier.Show($"PO:{DocumentNo} is closed", ViewNotifierType.Success);
    }


    private bool IsArrowDisabled(POLineGetDto item)
    {
        return (item.TempReceive != 0 && item.TempReceive == item.QuantityToReceive) || !IsEdit;
    }
    private void CheckToReceived(int receive, POLineGetDto poLine)
    {
        if (receive < 0)
        {
            ViewNotifier.Show("Quantity to receive must be greater than or equal to 0", ViewNotifierType.Error);
            return;
        }
        poLine.TempReceive = receive;
        if (receive == 0)
        {
            Validate = true;
            return;
        }
        const decimal differenceRatio = (decimal)0.1;
        if (receive + poLine.QuantityReceived - poLine.Quantity < poLine.Quantity * differenceRatio)
        {
            Validate = false;
            return;
        }
        Validate = true;
        ViewNotifier.Show("Quantity to receive must be less than or equal to Quantity to receive 10%", ViewNotifierType.Error);
    }

    private void DateChanged(DateTime? date, POLineGetDto poLine)
    {
        if (date == null)
        {
            Validate = true;
            ViewNotifier.Show("Expiration date is required", ViewNotifierType.Error);
            return;
        }
        if (date < DateTime.Now)
        {
            Validate = true;
            ViewNotifier.Show("Expiration date must be greater than or equal to current date", ViewNotifierType.Error);
            return;
        }
        Validate = false;
        ExpirationDate = date;
        poLine.ExpirationDate = DateOnly.FromDateTime(date.Value);
    }

    protected async Task GetUserCreatePOAsync()
    {
        try
        {
            var resp = await PurchaseOrderApiClient.GetUserCreatedPOByPONumberAsync(DocumentNo);
            if (resp.IsSuccessStatusCode)
            {
                UserCreatedPO = resp.Result;
            }
            else
            {
                ViewNotifier.Show(resp.Message, ViewNotifierType.Error);
            }
        }
        catch (Exception e)
        {
            ViewNotifier.Show(e.Message, ViewNotifierType.Error);
        }
    }
    private async Task<TableData<GetStockOrderDto>> ServerReload(TableState state, CancellationToken token)
    {
        OnPage(state.Page, state.PageSize);
        await LoadStockOrderAsync();
        return new TableData<GetStockOrderDto>
        {
            TotalItems = TotalItems,
            Items = ListStockOrderDtos
        };
    }

    private void OnPage(int index, int size)
    {
        PageSize = size;
        PageIndex = index;
    }

    private async Task LoadStockOrderAsync()
    {
        var filter = new StockOrderFilter
        {
            PageIndex = PageIndex,
            PageSize = PageSize,
            HeaderNumber = DocumentNo
        };
        var apiResponse = await StockOrderApiClient.GetStockOrderByPoHeaderAsync(filter);
        if (apiResponse.IsSuccessStatusCode)
        {
            ListStockOrderDtos = apiResponse.Result.Data;
            TotalItems = apiResponse.Result.RowCount;
        }
        else
        {
            ViewNotifier.Show(apiResponse.Message, ViewNotifierType.Error);
        }
    }
    private void ComingSoon() => ViewNotifier.Show("This function is coming soon", ViewNotifierType.Warning);
}
