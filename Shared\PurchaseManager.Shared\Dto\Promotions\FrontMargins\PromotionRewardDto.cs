﻿using PurchaseManager.Constants.Enum;
namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
///     DTO for promotion reward response
/// </summary>
public class PromotionRewardDto
{
    public long Id { get; set; }
    public string? ConditionItemNumber { get; set; }
    public FrontMarginCalculationType RewardType { get; set; }
    public string RewardTypeName { get; set; } = string.Empty;
    public decimal? DiscountPercent { get; set; }
    public decimal? DiscountAmount { get; set; }
    public string? RewardItemNumber { get; set; }
    public string? ItemName { get; set; }
    public decimal? Quantity { get; set; }
    public string? UnitOfMeasure { get; set; }
}
