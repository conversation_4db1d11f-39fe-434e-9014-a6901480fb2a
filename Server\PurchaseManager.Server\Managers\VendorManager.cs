﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using PurchaseManager.Infrastructure.Server;
using PurchaseManager.Infrastructure.Server.Models;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Server.Aop;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models;
using PurchaseManager.Storage;
using static Microsoft.AspNetCore.Http.StatusCodes;
using GetVendorDto=PurchaseManager.Shared.Dto.Vendor.GetVendorDto;

namespace PurchaseManager.Server.Managers;

[ApiResponseException]
public class VendorManager : IVendorManager
{
    private readonly IMapper _autoMapper;
    private readonly ApplicationPersistenceManager _dbContext;
    private readonly ApplicationDbContext _applicationDbContext;

    private readonly ILogger<VendorManager> _logger;
    private readonly IStringLocalizer<Global> L;
    private readonly IMapper _mapper;
    private readonly ApplicationPersistenceManager _persistenceManager;


    public VendorManager(ILogger<VendorManager> logger,
        IStringLocalizer<Global> l, ApplicationPersistenceManager persistenceManager, IMapper autoMapper, IMapper mapper,
        ApplicationDbContext applicationDbContext)
    {
        _logger = logger;
        L = l;
        _persistenceManager = persistenceManager;
        _dbContext = persistenceManager;
        _autoMapper = autoMapper;
        _mapper = mapper;
        _applicationDbContext = applicationDbContext;

    }

    public string Metadata()
        => _dbContext.Metadata();

    public async Task<ApiResponse> BuildViewModel(string number)
    {
        var vendor = await _dbContext.Context.Vendors.FindAsync(number);

        if (vendor is null)
        {
            return new ApiResponse(Status404NotFound, L["Not Found"]);
        }

        var result = _autoMapper.Map<Vendor, Shared.Models.GetVendorDto>(vendor);

        return new ApiResponse(Status200OK, L["Operation Successful"], result);
    }

    public async Task<ApiResponse> UpdateVendor(Shared.Models.GetVendorDto vendorViewModel)
    {
        var vendor = await _dbContext.Context.Vendors.SingleOrDefaultAsync(v => v.Number == vendorViewModel.Number);

        if (vendor == null)
        {
            _logger.LogInformation(L["The vendor {0} doesn't exist", vendorViewModel.Number]);
            return new ApiResponse(Status404NotFound, L["The Vendor doesn't exist"]);
        }

        _autoMapper.Map(vendorViewModel, vendor);

        try
        {
            await _dbContext.Context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError($"Updating Vendor exception: {ex.GetBaseException().Message}");
            return new ApiResponse(Status400BadRequest, L["Operation Failed"]);
        }

        return new ApiResponse(Status200OK, L["Operation Successful"]);
    }

    public async Task<ApiResponse> CreateVendor(Shared.Models.GetVendorDto vendorViewModel)
    {
        var vendor = await _dbContext.Context.Vendors.SingleOrDefaultAsync(v => v.Number == vendorViewModel.Number);

        if (vendor != null)
        {
            _logger.LogInformation(L["The vendor {0} does exist", vendorViewModel.Number]);
            return new ApiResponse(Status404NotFound, L["The Vendor does exist"]);
        }
        _autoMapper.Map(vendorViewModel, (Vendor)null);

        try
        {
            await _dbContext.Context.SaveChangesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError($"Creating Vendor exception: {ex.GetBaseException().Message}");
            return new ApiResponse(Status400BadRequest, L["Operation Failed"]);
        }

        return new ApiResponse(Status200OK, L["Operation Successful"]);
    }

    public async Task<Vendor> GetVendorByNumber(string number) => await _dbContext.Context.Vendors.FindAsync(number);

    public async Task<ApiResponse> GetVendor(string number)
    {
        var vendor = await GetVendorByNumber(number);
        return vendor != null ?
            new ApiResponse(Status200OK, L["Operation Successful"], _autoMapper.Map<GetVendorDto>(vendor)) :
            new ApiResponse(Status404NotFound, L["Operation Failed"]);
    }

    public IQueryable<Vendor> GetVendors(VendorFilter filter, string query)
    {
        IQueryable<Vendor> vendorQueryable = _persistenceManager.GetEntities<Vendor>().AsNoTracking()
            .Where(i =>
                (filter.Query == null || i.Name.Contains(filter.Query)) &&
                (filter.Phone == null || i.Phone.Contains(filter.Phone)) &&
                (filter.Number == null || i.Number.Contains(filter.Number)) &&
                (filter.Name == null || i.Name.Contains(filter.Name) ||
                 filter.Name == null || i.SearchName.Contains(filter.Name)))
            .OrderByDescending(i => i.RowId);

        if (!query!.Contains("take"))
        {
            vendorQueryable = vendorQueryable.Take(10);
        }

        if (!query!.Contains("skip"))
        {
            vendorQueryable = vendorQueryable.Skip(0);
        }

        return vendorQueryable;
        //return _autoMapper.ProjectTo<VendorList>(vendorQueryable);
    }
    public async Task<ApiResponse> SearchAutocomplete(string number, string name, string queryString,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var query = _dbContext.Context.Vendors.AsNoTracking();

            switch (string.IsNullOrEmpty(number))
            {
                case false when !string.IsNullOrEmpty(name):
                    query = query.Where(item => item.Number.Contains(number) || item.Name.Contains(name));
                    break;
                case false:
                    query = query.Where(item => item.Number.Contains(number) || item.Name.Contains(number));
                    break;
                default:
                    {
                        if (!string.IsNullOrEmpty(name))
                        {
                            query = query.Where(item => item.Name.Contains(name) || item.Number.Contains(name));
                        }
                        break;
                    }
            }

            if (!queryString!.Contains("take"))
            {
                query = query.Take(100);
            }

            if (!queryString!.Contains("skip"))
            {
                query = query.Skip(0);
            }

            var result = await query
                .ToListAsync(cancellationToken);

            var data = _mapper.Map<List<Shared.Models.GetVendorDto>>(result);
            return new ApiResponse(Status200OK, L["Search Vendor successfully"], data);
        }
        catch
        {
            return new ApiResponse(Status404NotFound, L["Error searching Vendor"]);
        }
    }

    public async Task<ApiResponse> ToggleVendorStatus(string vendorNumber)
    {
        try
        {
            var foundVendor = _dbContext.Context.Vendors.FirstOrDefault(x => x.Number.Trim() == vendorNumber);
            if (foundVendor is null)
            {
                return new ApiResponse(Status404NotFound, L["Operation Failed"], false);
            }
            //toggle block status
            var vendorStatusAfterUpdate = foundVendor.Blocked == Constants.VendorBlockedType.Blocked
                ? Constants.VendorBlockedType.NoBlock : Constants.VendorBlockedType.Blocked;
            foundVendor.Blocked = vendorStatusAfterUpdate;
            await _dbContext.Context.SaveChangesAsync();
            return new ApiResponse(Status200OK, L["Operation Successfully"],
            vendorStatusAfterUpdate == Constants.VendorBlockedType.Blocked);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Toggle Vendor Status has exception: {ex.GetBaseException().Message}");
            return new ApiResponse(Status400BadRequest, L["Operation Failed"], false);
        }
    }

}
