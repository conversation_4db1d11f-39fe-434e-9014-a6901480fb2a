using Microsoft.Extensions.Logging;
using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.PO;
using PurchaseManager.Shared.Services;

namespace PurchaseManager.Shared.Services;

/// <summary>
/// Service for integrating Front Margin into PO creation process
/// </summary>
public interface IPOFrontMarginIntegrationService
{
    /// <summary>
    /// Auto-apply Front Margin discounts to PO when vendor is selected
    /// </summary>
    Task<POAutoApplyResult> AutoApplyFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    /// Validate PO lines against Front Margin business rules before saving
    /// </summary>
    Task<POValidationResult> ValidatePOWithFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);

    /// <summary>
    /// Get Front Margin suggestions for PO creation
    /// </summary>
    Task<List<FrontMarginSuggestion>> GetFrontMarginSuggestionsAsync(string vendorCode, DateTime orderDate, List<string> itemNumbers);

    /// <summary>
    /// Apply specific Front Margin promotion to PO lines
    /// </summary>
    Task<List<POLineGetDto>> ApplySpecificPromotionAsync(List<POLineGetDto> poLines, string promotionNumber);

    /// <summary>
    /// Remove Front Margin discounts from PO lines
    /// </summary>
    Task<List<POLineGetDto>> RemoveFrontMarginDiscountsAsync(List<POLineGetDto> poLines);

    /// <summary>
    /// Calculate potential savings with Front Margin
    /// </summary>
    Task<FrontMarginSavingsCalculation> CalculatePotentialSavingsAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines);
}

public class POFrontMarginIntegrationService : IPOFrontMarginIntegrationService
{
    private readonly IFrontMarginUIService _frontMarginService;
    private readonly ILogger<POFrontMarginIntegrationService> _logger;

    public POFrontMarginIntegrationService(
        IFrontMarginUIService frontMarginService,
        ILogger<POFrontMarginIntegrationService> logger)
    {
        _frontMarginService = frontMarginService;
        _logger = logger;
    }

    public async Task<POAutoApplyResult> AutoApplyFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        var result = new POAutoApplyResult();

        try
        {
            // Check if vendor has active Front Margin promotions
            var hasActivePromotions = await _frontMarginService.HasActiveFrontMarginAsync(poHeader.BuyFromVendorNumber);
            if (!hasActivePromotions)
            {
                result.Message = "No active Front Margin promotions found for this vendor";
                return result;
            }

            var totalOrderValue = poLines.Sum(line => line.Amount);
            var promotions = await _frontMarginService.GetApplicablePromotionsAsync(
                poHeader.BuyFromVendorNumber,
                poHeader.OrderDate,
                totalOrderValue);

            // Filter only auto-apply promotions
            var autoApplyPromotions = promotions.Where(p => p.AutoApply).ToList();
            if (!autoApplyPromotions.Any())
            {
                result.Message = "No auto-apply Front Margin promotions available";
                return result;
            }

            var updatedLines = new List<POLineGetDto>();
            var appliedPromotions = new List<string>();

            foreach (var line in poLines)
            {
                var updatedLine = new POLineGetDto
                {
                    // Copy all properties
                    LineNumber = line.LineNumber,
                    ItemNumber = line.ItemNumber,
                    Description = line.Description,
                    Quantity = line.Quantity,
                    UnitOfMeasure = line.UnitOfMeasure,
                    UnitCost = line.UnitCost,
                    UnitPrice = line.UnitPrice,
                    Amount = line.Amount,
                    DocumentType = line.DocumentType,
                    LotNo = line.LotNo,
                    OriginalUnitCost = line.UnitCost // Store original cost
                };

                // Find applicable promotion for this item (highest priority)
                var applicablePromotion = autoApplyPromotions
                    .Where(p => p.ItemNumber == line.ItemNumber)
                    .OrderBy(p => p.Priority)
                    .FirstOrDefault();

                if (applicablePromotion != null)
                {
                    // Apply discount based on type
                    var discountInfo = await _frontMarginService.GetLineDiscountAsync(
                        poHeader.BuyFromVendorNumber,
                        line.ItemNumber,
                        poHeader.OrderDate,
                        totalOrderValue);

                    if (discountInfo != null)
                    {
                        ApplyDiscountToLine(updatedLine, discountInfo);

                        updatedLine.HasFrontMargin = true;
                        updatedLine.FrontMarginNumber = discountInfo.PromotionNumber;
                        updatedLine.DiscountSource = "Front Margin";

                        if (!appliedPromotions.Contains(discountInfo.PromotionNumber))
                        {
                            appliedPromotions.Add(discountInfo.PromotionNumber);
                        }

                        result.TotalDiscountApplied += (line.Amount - updatedLine.Amount);
                    }
                }

                updatedLines.Add(updatedLine);
            }

            result.UpdatedLines = updatedLines;
            result.AppliedPromotions = appliedPromotions;
            result.IsSuccess = appliedPromotions.Any();
            result.Message = result.IsSuccess
                ? $"Applied {appliedPromotions.Count} Front Margin promotion(s)"
                : "No Front Margin discounts were applied";

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error auto-applying Front Margin to PO");
            result.IsSuccess = false;
            result.Message = "Failed to apply Front Margin discounts due to system error";
        }

        return result;
    }

    public async Task<POValidationResult> ValidatePOWithFrontMarginAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        var result = new POValidationResult();

        try
        {
            var validationResult = await _frontMarginService.ValidatePOAsync(poHeader, poLines);

            result.IsValid = validationResult.IsValid;
            result.ErrorMessages = validationResult.ErrorMessages;
            result.WarningMessages = validationResult.WarningMessages;
            result.TotalOrderValue = validationResult.TotalOrderValue;
            result.TotalDiscountAmount = validationResult.CalculatedDiscount;
            result.ExceedsMaxDiscount = validationResult.ExceedsMaxDiscount;
            result.BelowMinOrderValue = validationResult.BelowMinOrderValue;
            result.ExceedsMaxOrderValue = validationResult.ExceedsMaxOrderValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating PO with Front Margin");
            result.IsValid = false;
            result.ErrorMessages.Add("Validation failed due to system error");
        }

        return result;
    }

    public async Task<List<FrontMarginSuggestion>> GetFrontMarginSuggestionsAsync(string vendorCode, DateTime orderDate, List<string> itemNumbers)
    {
        var suggestions = new List<FrontMarginSuggestion>();

        try
        {
            var promotions = await _frontMarginService.GetApplicablePromotionsAsync(vendorCode, orderDate);

            foreach (var itemNumber in itemNumbers)
            {
                var itemPromotions = promotions.Where(p => p.ItemNumber == itemNumber).ToList();

                if (itemPromotions.Any())
                {
                    var bestPromotion = itemPromotions.OrderBy(p => p.Priority).First();

                    suggestions.Add(new FrontMarginSuggestion
                    {
                        ItemNumber = itemNumber,
                        PromotionNumber = bestPromotion.Number,
                        PromotionName = bestPromotion.ProgramNumber,
                        DiscountType = bestPromotion.DiscountType,
                        DiscountTypeName = bestPromotion.DiscountTypeName,
                        DiscountValue = bestPromotion.DiscountValue,
                        DiscountPercentage = bestPromotion.DiscountPercentage,
                        AutoApply = bestPromotion.AutoApply,
                        Priority = bestPromotion.Priority,
                        EstimatedSavings = 0 // Will be calculated when line amount is known
                    });
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Front Margin suggestions for vendor {VendorCode}", vendorCode);
        }

        return suggestions;
    }

    public async Task<List<POLineGetDto>> ApplySpecificPromotionAsync(List<POLineGetDto> poLines, string promotionNumber)
    {
        // Implementation for applying specific promotion
        // This would be used when user manually selects a promotion
        return poLines; // Placeholder
    }

    public async Task<List<POLineGetDto>> RemoveFrontMarginDiscountsAsync(List<POLineGetDto> poLines)
    {
        var updatedLines = new List<POLineGetDto>();

        foreach (var line in poLines)
        {
            var updatedLine = new POLineGetDto
            {
                // Copy all properties
                LineNumber = line.LineNumber,
                ItemNumber = line.ItemNumber,
                Description = line.Description,
                Quantity = line.Quantity,
                UnitOfMeasure = line.UnitOfMeasure,
                UnitCost = line.OriginalUnitCost > 0 ? line.OriginalUnitCost : line.UnitCost, // Restore original cost
                UnitPrice = line.UnitPrice,
                DocumentType = line.DocumentType,
                LotNo = line.LotNo,

                // Reset Front Margin properties
                HasFrontMargin = false,
                FrontMarginNumber = null,
                OriginalUnitCost = 0,
                FrontMarginDiscountPercent = 0,
                FrontMarginDiscountAmount = 0,
                DiscountSource = null
            };

            // Recalculate amount
            updatedLine.Amount = updatedLine.UnitCost * updatedLine.Quantity;

            updatedLines.Add(updatedLine);
        }

        return updatedLines;
    }

    public async Task<FrontMarginSavingsCalculation> CalculatePotentialSavingsAsync(POHeaderGetDto poHeader, List<POLineGetDto> poLines)
    {
        var calculation = new FrontMarginSavingsCalculation();

        try
        {
            var originalTotal = poLines.Sum(line => line.Amount);
            var withFrontMarginResult = await AutoApplyFrontMarginAsync(poHeader, poLines);

            if (withFrontMarginResult.IsSuccess)
            {
                var discountedTotal = withFrontMarginResult.UpdatedLines.Sum(line => line.Amount);

                calculation.OriginalTotal = originalTotal;
                calculation.DiscountedTotal = discountedTotal;
                calculation.TotalSavings = originalTotal - discountedTotal;
                calculation.SavingsPercentage = originalTotal > 0 ? (calculation.TotalSavings / originalTotal) * 100 : 0;
                calculation.ApplicablePromotions = withFrontMarginResult.AppliedPromotions;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating potential Front Margin savings");
        }

        return calculation;
    }

    #region Private Methods

    private static void ApplyDiscountToLine(POLineGetDto line, FrontMarginDiscountInfo discountInfo)
    {
        switch (discountInfo.DiscountType)
        {
            case 1: // Percentage discount
                var discountAmount = line.UnitCost * (discountInfo.DiscountPercentage / 100);
                line.UnitCost -= discountAmount;
                line.FrontMarginDiscountPercent = discountInfo.DiscountPercentage;
                line.FrontMarginDiscountAmount = discountAmount * line.Quantity;
                break;

            case 2: // Fixed amount discount
                var fixedDiscountPerUnit = discountInfo.DiscountValue / line.Quantity;
                line.UnitCost = Math.Max(0, line.UnitCost - fixedDiscountPerUnit);
                line.FrontMarginDiscountAmount = Math.Min(discountInfo.DiscountValue, line.Amount);
                break;

            case 3: // Same item gift - average unit price calculation
            case 4: // Different item gift - handled separately
                // For gift promotions, the discount is calculated differently
                // This would need more complex logic based on gift quantities
                break;
        }

        // Recalculate amount
        line.Amount = line.UnitCost * line.Quantity;

        // Apply max discount constraint if specified
        if (discountInfo.MaxDiscountAmount.HasValue)
        {
            var actualDiscount = line.OriginalUnitCost * line.Quantity - line.Amount;
            if (actualDiscount > discountInfo.MaxDiscountAmount.Value)
            {
                var excessDiscount = actualDiscount - discountInfo.MaxDiscountAmount.Value;
                line.UnitCost += excessDiscount / line.Quantity;
                line.Amount = line.UnitCost * line.Quantity;
                line.FrontMarginDiscountAmount = discountInfo.MaxDiscountAmount.Value;
            }
        }
    }

    #endregion
}

#region DTOs

public class POAutoApplyResult
{
    public bool IsSuccess { get; set; }
    public string Message { get; set; } = string.Empty;
    public List<POLineGetDto> UpdatedLines { get; set; } = new();
    public List<string> AppliedPromotions { get; set; } = new();
    public decimal TotalDiscountApplied { get; set; }
}

public class POValidationResult
{
    public bool IsValid { get; set; } = true;
    public List<string> ErrorMessages { get; set; } = new();
    public List<string> WarningMessages { get; set; } = new();
    public decimal TotalOrderValue { get; set; }
    public decimal TotalDiscountAmount { get; set; }
    public bool ExceedsMaxDiscount { get; set; }
    public bool BelowMinOrderValue { get; set; }
    public bool ExceedsMaxOrderValue { get; set; }
}

public class FrontMarginSuggestion
{
    public string ItemNumber { get; set; } = string.Empty;
    public string PromotionNumber { get; set; } = string.Empty;
    public string PromotionName { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public string DiscountTypeName { get; set; } = string.Empty;
    public decimal DiscountValue { get; set; }
    public decimal DiscountPercentage { get; set; }
    public bool AutoApply { get; set; }
    public int Priority { get; set; }
    public decimal EstimatedSavings { get; set; }
}

public class FrontMarginSavingsCalculation
{
    public decimal OriginalTotal { get; set; }
    public decimal DiscountedTotal { get; set; }
    public decimal TotalSavings { get; set; }
    public decimal SavingsPercentage { get; set; }
    public List<string> ApplicablePromotions { get; set; } = new();
}

#endregion
