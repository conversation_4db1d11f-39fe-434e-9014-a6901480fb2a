-- =============================================
-- Simple Front Margin Migration Script
-- Execute this to add Front Margin columns
-- Date: 2025-01-11
-- =============================================

USE [PurchaseManager] -- Change to your database name
GO

PRINT 'Starting Simple Front Margin Migration...'
PRINT 'Database: ' + DB_NAME()
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)

-- Check if table exists
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'PromotionFrontMargins')
BEGIN
    PRINT 'ERROR: PromotionFrontMargins table does not exist!'
    RETURN
END

-- Check if migration is needed
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'PromotionFrontMargins' AND COLUMN_NAME = 'DiscountType')
BEGIN
    PRINT 'Migration already applied.'
    RETURN
END

PRINT 'Adding Front Margin columns...'

BEGIN TRY
    BEGIN TRANSACTION

    -- Add new columns
    ALTER TABLE PromotionFrontMargins ADD
        DiscountType INT NOT NULL DEFAULT 1,
        FixedDiscountAmount DECIMAL(18,2) NULL,
        BuyQuantity DECIMAL(18,4) NULL,
        GiftQuantity DECIMAL(18,4) NULL,
        GiftItemNumber NVARCHAR(50) NULL,
        GiftItemName NVARCHAR(250) NULL,
        GiftItemUOM NVARCHAR(50) NULL,
        GiftItemQuantity DECIMAL(18,4) NULL,
        MinimumQuantity DECIMAL(18,4) NULL,
        MinimumAmount DECIMAL(18,2) NULL,
        MaximumDiscountAmount DECIMAL(18,2) NULL

    PRINT 'Added new columns successfully'

    -- Update DiscountPercentage precision
    ALTER TABLE PromotionFrontMargins ALTER COLUMN DiscountPercentage DECIMAL(5,2)
    PRINT 'Updated DiscountPercentage precision'

    -- Add basic constraint
    ALTER TABLE PromotionFrontMargins ADD CONSTRAINT CK_PromotionFrontMargins_DiscountType
    CHECK (DiscountType IN (1, 2, 3, 4))
    PRINT 'Added DiscountType constraint'

    -- Add indexes
    CREATE INDEX IX_PromotionFrontMargins_DiscountType ON PromotionFrontMargins(DiscountType)
    PRINT 'Added DiscountType index'

    -- Update existing records to use percentage discount
    UPDATE PromotionFrontMargins
    SET DiscountType = 1
    WHERE DiscountPercentage > 0

    PRINT 'Updated existing records'

    COMMIT TRANSACTION
    PRINT 'Migration completed successfully!'

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT 'ERROR: Migration failed!'
    PRINT 'Error: ' + ERROR_MESSAGE()
    THROW;
END CATCH

-- Verify results
PRINT 'Verification:'
SELECT
    COUNT(*) as TotalColumns
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionFrontMargins'

SELECT
    DiscountType,
    COUNT(*) as RecordCount
FROM PromotionFrontMargins
GROUP BY DiscountType
ORDER BY DiscountType

PRINT 'Simple Front Margin Migration completed!'
