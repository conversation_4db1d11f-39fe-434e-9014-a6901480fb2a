-- =============================================
-- Script: Update PromotionHeader to match simplified entity
-- Description: Add/Remove columns to match the simplified PromotionHeader entity
-- Date: 2025-01-15
-- Note: Sync database with simplified entity design
-- =============================================

USE [PurchaseManager] -- Replace with your database name
GO

-- Check if table exists
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND type in (N'U'))
BEGIN
    PRINT 'ERROR: PromotionHeaders table does not exist!'
    RETURN
END

PRINT 'Updating PromotionHeader table to match simplified entity...'

BEGIN TRANSACTION

BEGIN TRY
    -- STEP 1: Add required columns that match the entity

    -- Priority column (required for PO integration)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'Priority')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [Priority] INT NOT NULL DEFAULT 1
        PRINT '✓ Added Priority column'
    END
    ELSE
        PRINT '- Priority column already exists'

    -- AutoApply column (required for PO integration)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'AutoApply')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [AutoApply] BIT NOT NULL DEFAULT 1
        PRINT '✓ Added AutoApply column'
    END
    ELSE
        PRINT '- AutoApply column already exists'

    -- ApprovalStatus column (part of simplified approval workflow)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApprovalStatus')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [ApprovalStatus] INT NOT NULL DEFAULT 1
        PRINT '✓ Added ApprovalStatus column'
    END
    ELSE
        PRINT '- ApprovalStatus column already exists'

    -- ApprovalBy column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApprovalBy')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [ApprovalBy] NVARCHAR(100) NULL
        PRINT '✓ Added ApprovalBy column'
    END
    ELSE
        PRINT '- ApprovalBy column already exists'

    -- ApprovalDate column
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApprovalDate')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [ApprovalDate] DATETIME2 NULL
        PRINT '✓ Added ApprovalDate column'
    END
    ELSE
        PRINT '- ApprovalDate column already exists'

    -- UsingId column (document locking)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'UsingId')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [UsingId] NVARCHAR(100) NULL
        PRINT '✓ Added UsingId column'
    END
    ELSE
        PRINT '- UsingId column already exists'

    -- BeginUsingTime column (document locking)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'BeginUsingTime')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [BeginUsingTime] DATETIME2 NULL
        PRINT '✓ Added BeginUsingTime column'
    END
    ELSE
        PRINT '- BeginUsingTime column already exists'

    -- STEP 2: Remove complex columns that don't exist in simplified entity
    PRINT ''
    PRINT 'Removing complex columns not in simplified entity...'

    -- List of complex columns to remove
    DECLARE @ComplexColumns TABLE (ColumnName NVARCHAR(128))
    INSERT INTO @ComplexColumns VALUES
        ('MaxBudgetAmount'), ('UsedAmount'), ('MinOrderValue'), ('MaxOrderValue'),
        ('MaxDiscountAmount'), ('ApplicableDocTypes'), ('ApplyToAllVendorItems'),
        ('RequireApprovalBeforeReward'), ('DefaultPaymentMethod'), ('DefaultPaymentTiming'),
        ('EvaluationPeriod'), ('SpecialConditions')

    DECLARE @ColumnName NVARCHAR(128)
    DECLARE column_cursor CURSOR FOR
        SELECT ColumnName FROM @ComplexColumns
        WHERE ColumnName IN (
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'PromotionHeaders'
        )

    OPEN column_cursor
    FETCH NEXT FROM column_cursor INTO @ColumnName

    WHILE @@FETCH_STATUS = 0
    BEGIN
        DECLARE @SQL NVARCHAR(MAX) = 'ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [' + @ColumnName + ']'
        EXEC sp_executesql @SQL
        PRINT '✓ Removed ' + @ColumnName + ' column'
        FETCH NEXT FROM column_cursor INTO @ColumnName
    END

    CLOSE column_cursor
    DEALLOCATE column_cursor

    COMMIT TRANSACTION
    PRINT ''
    PRINT '✅ PromotionHeader table updated to match simplified entity!'
    PRINT ''

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT ''
    PRINT '❌ Error updating PromotionHeader table:'
    PRINT ERROR_MESSAGE()
    PRINT ''
END CATCH

-- Verify the final table structure
PRINT 'Verifying final table structure...'
PRINT ''
PRINT '📋 Essential columns (should exist):'
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'ProgramName', 'Description', 'VendorCode', 'SupportTypeNumber',
        'StartDate', 'EndDate', 'AccumulateRevenue', 'ProgramType', 'Status',
        'Priority', 'AutoApply', 'ApprovalStatus', 'ApprovalBy', 'ApprovalDate',
        'UsingId', 'BeginUsingTime'
    )
ORDER BY COLUMN_NAME

-- Check if any complex columns still exist (should be empty)
PRINT ''
PRINT '⚠️  Complex columns (should NOT exist):'
SELECT
    COLUMN_NAME,
    DATA_TYPE
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'MaxBudgetAmount', 'UsedAmount', 'MinOrderValue', 'MaxOrderValue',
        'MaxDiscountAmount', 'ApplicableDocTypes', 'ApplyToAllVendorItems',
        'RequireApprovalBeforeReward', 'DefaultPaymentMethod', 'DefaultPaymentTiming',
        'EvaluationPeriod', 'SpecialConditions'
    )
ORDER BY COLUMN_NAME

-- Count complex columns remaining
DECLARE @RemainingComplexColumns INT
SELECT @RemainingComplexColumns = COUNT(*)
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'MaxBudgetAmount', 'UsedAmount', 'MinOrderValue', 'MaxOrderValue',
        'MaxDiscountAmount', 'ApplicableDocTypes', 'ApplyToAllVendorItems',
        'RequireApprovalBeforeReward', 'DefaultPaymentMethod', 'DefaultPaymentTiming',
        'EvaluationPeriod', 'SpecialConditions'
    )

IF @RemainingComplexColumns = 0
BEGIN
    PRINT ''
    PRINT '✅ Table structure matches simplified entity perfectly!'
END
ELSE
BEGIN
    PRINT ''
    PRINT '⚠️  Warning: ' + CAST(@RemainingComplexColumns AS VARCHAR(10)) + ' complex columns still exist!'
    PRINT '⚠️  The entity may not match the database structure.'
END

-- Create performance index for PO integration
PRINT ''
PRINT 'Creating performance index...'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionHeaders_VendorCode_ProgramType_Priority')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_PromotionHeaders_VendorCode_ProgramType_Priority]
    ON [dbo].[PromotionHeaders] ([VendorCode] ASC, [ProgramType] ASC, [Priority] ASC)
    INCLUDE ([Status], [StartDate], [EndDate], [AutoApply])
    WHERE [Status] = 2 AND [ModificationStatus] = 1 -- Only active promotions
    PRINT '✓ Created index for PO integration queries'
END
ELSE
    PRINT '- Performance index already exists'

PRINT ''
PRINT '📋 Simplified PromotionHeader Design:'
PRINT ''
PRINT '✅ Essential Fields:'
PRINT '- ProgramName, Description, VendorCode, StartDate, EndDate'
PRINT '- ProgramType (1=FM, 2=BM), Status, AccumulateRevenue'
PRINT '- Priority (1=cao nhất), AutoApply (FM=true, BM=false)'
PRINT '- ApprovalStatus, ApprovalBy, ApprovalDate'
PRINT '- UsingId, BeginUsingTime (document locking)'
PRINT ''
PRINT '✅ Front Margin (ProgramType=1):'
PRINT '- Đơn giản với 3 cases: Percentage, Fixed Amount, Gift'
PRINT '- Logic trong PromotionFrontMargin table'
PRINT '- Chỉ cần Priority + AutoApply từ Header'
PRINT ''
PRINT '✅ Back Margin (ProgramType=2):'
PRINT '- Phức tạp với 5 loại chiết khấu:'
PRINT '  1. Chiết khấu theo doanh số (Lũy tiến, Mốc, Bật thang)'
PRINT '  2. Chiết khấu theo số lượng (Lũy tiến, Mốc, Bật thang)'
PRINT '  3. Chiết khấu thanh toán sớm'
PRINT '- Logic trong PromotionCondition + PromotionReward tables'
PRINT ''
PRINT '❌ Removed Complex Fields:'
PRINT '- Budget management (MaxBudgetAmount, UsedAmount)'
PRINT '- Order constraints (MinOrderValue, MaxOrderValue, MaxDiscountAmount)'
PRINT '- Complex settings (ApplicableDocTypes, RequireApprovalBeforeReward, etc.)'
PRINT ''
PRINT '🎯 Next Steps:'
PRINT '1. Run UpdatePromotionHeader_SampleData.sql to set default values'
PRINT '2. Update application code to match simplified entity'
PRINT '3. Test Front Margin PO integration'
PRINT '4. Implement Back Margin with PromotionCondition/PromotionReward later'
