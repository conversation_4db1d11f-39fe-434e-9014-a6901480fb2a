-- =============================================
-- Script: Update PromotionHeader for Front Margin Integration
-- Description: Add new columns to support Front Margin business rules and PO integration
-- Date: 2025-01-15
-- =============================================

USE [PurchaseManager] -- Replace with your database name
GO

-- Check if table exists
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND type in (N'U'))
BEGIN
    PRINT 'ERROR: PromotionHeaders table does not exist!'
    RETURN
END

PRINT 'Starting PromotionHeader table update...'

-- Add new columns for business rules and PO integration
BEGIN TRANSACTION

BEGIN TRY
    -- 1. Priority for handling multiple promotions
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'Priority')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [Priority] INT NOT NULL DEFAULT 1
        PRINT '✓ Added Priority column'
    END
    ELSE
        PRINT '- Priority column already exists'

    -- 2. Auto-apply flag
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'AutoApply')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [AutoApply] BIT NOT NULL DEFAULT 1
        PRINT '✓ Added AutoApply column'
    END
    ELSE
        PRINT '- AutoApply column already exists'

    -- 3. Budget tracking
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxBudgetAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [MaxBudgetAmount] DECIMAL(18,2) NULL
        PRINT '✓ Added MaxBudgetAmount column'
    END
    ELSE
        PRINT '- MaxBudgetAmount column already exists'

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'UsedAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [UsedAmount] DECIMAL(18,2) NOT NULL DEFAULT 0
        PRINT '✓ Added UsedAmount column'
    END
    ELSE
        PRINT '- UsedAmount column already exists'

    -- 4. Order value constraints
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MinOrderValue')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [MinOrderValue] DECIMAL(18,2) NULL
        PRINT '✓ Added MinOrderValue column'
    END
    ELSE
        PRINT '- MinOrderValue column already exists'

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxOrderValue')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [MaxOrderValue] DECIMAL(18,2) NULL
        PRINT '✓ Added MaxOrderValue column'
    END
    ELSE
        PRINT '- MaxOrderValue column already exists'

    -- 5. Discount constraints
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxDiscountAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [MaxDiscountAmount] DECIMAL(18,2) NULL
        PRINT '✓ Added MaxDiscountAmount column'
    END
    ELSE
        PRINT '- MaxDiscountAmount column already exists'

    -- 6. Document type constraints
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApplicableDocTypes')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [ApplicableDocTypes] NVARCHAR(100) NULL
        PRINT '✓ Added ApplicableDocTypes column'
    END
    ELSE
        PRINT '- ApplicableDocTypes column already exists'

    -- 7. Vendor item scope
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApplyToAllVendorItems')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [ApplyToAllVendorItems] BIT NOT NULL DEFAULT 1
        PRINT '✓ Added ApplyToAllVendorItems column'
    END
    ELSE
        PRINT '- ApplyToAllVendorItems column already exists'

    -- 8. Approval requirements
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'RequireApprovalBeforeReward')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [RequireApprovalBeforeReward] BIT NOT NULL DEFAULT 0
        PRINT '✓ Added RequireApprovalBeforeReward column'
    END
    ELSE
        PRINT '- RequireApprovalBeforeReward column already exists'

    -- 9. Payment method and timing (for BackMargin future use)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'DefaultPaymentMethod')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [DefaultPaymentMethod] INT NULL
        PRINT '✓ Added DefaultPaymentMethod column'
    END
    ELSE
        PRINT '- DefaultPaymentMethod column already exists'

    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'DefaultPaymentTiming')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [DefaultPaymentTiming] INT NULL
        PRINT '✓ Added DefaultPaymentTiming column'
    END
    ELSE
        PRINT '- DefaultPaymentTiming column already exists'

    -- 10. Evaluation period (for BackMargin)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'EvaluationPeriod')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [EvaluationPeriod] INT NULL
        PRINT '✓ Added EvaluationPeriod column'
    END
    ELSE
        PRINT '- EvaluationPeriod column already exists'

    -- 11. Special conditions
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'SpecialConditions')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] 
        ADD [SpecialConditions] NVARCHAR(1000) NULL
        PRINT '✓ Added SpecialConditions column'
    END
    ELSE
        PRINT '- SpecialConditions column already exists'

    COMMIT TRANSACTION
    PRINT ''
    PRINT '✅ PromotionHeader table updated successfully!'
    PRINT ''

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT ''
    PRINT '❌ Error updating PromotionHeader table:'
    PRINT ERROR_MESSAGE()
    PRINT ''
END CATCH

-- Verify the changes
PRINT 'Verifying table structure...'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'Priority', 'AutoApply', 'MaxBudgetAmount', 'UsedAmount',
        'MinOrderValue', 'MaxOrderValue', 'MaxDiscountAmount',
        'ApplicableDocTypes', 'ApplyToAllVendorItems', 'RequireApprovalBeforeReward',
        'DefaultPaymentMethod', 'DefaultPaymentTiming', 'EvaluationPeriod', 'SpecialConditions'
    )
ORDER BY COLUMN_NAME

PRINT ''
PRINT '📋 Column Descriptions:'
PRINT '- Priority: Độ ưu tiên khi có nhiều CTKM (1 = cao nhất)'
PRINT '- AutoApply: Tự động áp dụng khi tạo PO (true/false)'
PRINT '- MaxBudgetAmount: Ngân sách tối đa cho chương trình'
PRINT '- UsedAmount: Số tiền đã sử dụng'
PRINT '- MinOrderValue: Giá trị đơn hàng tối thiểu'
PRINT '- MaxOrderValue: Giá trị đơn hàng tối đa'
PRINT '- MaxDiscountAmount: Chiết khấu tối đa cho một đơn hàng'
PRINT '- ApplicableDocTypes: Loại đơn hàng áp dụng (comma separated)'
PRINT '- ApplyToAllVendorItems: Áp dụng cho tất cả sản phẩm của vendor'
PRINT '- RequireApprovalBeforeReward: Yêu cầu phê duyệt trước khi áp dụng'
PRINT '- DefaultPaymentMethod: Hình thức chi trả (cho BackMargin)'
PRINT '- DefaultPaymentTiming: Thời điểm chi trả (cho BackMargin)'
PRINT '- EvaluationPeriod: Chu kỳ đánh giá (cho BackMargin)'
PRINT '- SpecialConditions: Ghi chú điều kiện đặc biệt'

PRINT ''
PRINT '🎯 Next Steps:'
PRINT '1. Update existing PromotionHeader records with appropriate values'
PRINT '2. Test Front Margin integration with new columns'
PRINT '3. Update application code to use new business rules'
