-- =============================================
-- Script: Update PromotionHeader for Front Margin PO Integration (Simplified)
-- Description: Add minimal columns needed for Front Margin PO integration only
-- Date: 2025-01-15
-- Note: Keep it simple - FM chỉ cần Priority và AutoApply, BM sẽ dùng PromotionCondition/PromotionReward
-- =============================================

USE [PurchaseManager] -- Replace with your database name
GO

-- Check if table exists
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND type in (N'U'))
BEGIN
    PRINT 'ERROR: PromotionHeaders table does not exist!'
    RETURN
END

PRINT 'Starting PromotionHeader table update (Simplified for FM PO Integration)...'

-- Add minimal columns needed for PO integration
BEGIN TRANSACTION

BEGIN TRY
    -- 1. Priority for handling multiple promotions (cần thiết cho PO integration)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'Priority')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [Priority] INT NOT NULL DEFAULT 1
        PRINT '✓ Added Priority column'
    END
    ELSE
        PRINT '- Priority column already exists'

    -- 2. Auto-apply flag (cần thiết cho FM auto-apply trong PO)
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'AutoApply')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders]
        ADD [AutoApply] BIT NOT NULL DEFAULT 1
        PRINT '✓ Added AutoApply column'
    END
    ELSE
        PRINT '- AutoApply column already exists'

    COMMIT TRANSACTION
    PRINT ''
    PRINT '✅ PromotionHeader table updated successfully (Simplified)!'
    PRINT ''

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT ''
    PRINT '❌ Error updating PromotionHeader table:'
    PRINT ERROR_MESSAGE()
    PRINT ''
END CATCH

-- Verify the changes
PRINT 'Verifying table structure...'
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN ('Priority', 'AutoApply')
ORDER BY COLUMN_NAME

-- Check if there are any existing complex columns that should be removed
PRINT ''
PRINT 'Checking for complex columns that may need cleanup...'
SELECT
    COLUMN_NAME
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'MaxBudgetAmount', 'UsedAmount', 'MinOrderValue', 'MaxOrderValue',
        'MaxDiscountAmount', 'ApplicableDocTypes', 'ApplyToAllVendorItems',
        'RequireApprovalBeforeReward', 'DefaultPaymentMethod', 'DefaultPaymentTiming',
        'EvaluationPeriod', 'SpecialConditions'
    )
ORDER BY COLUMN_NAME

PRINT ''
PRINT '📋 Column Descriptions:'
PRINT '- Priority: Độ ưu tiên khi có nhiều CTKM (1 = cao nhất, 2, 3...)'
PRINT '- AutoApply: Tự động áp dụng FM khi tạo PO (true/false)'
PRINT ''
PRINT '📝 Design Philosophy:'
PRINT '✓ Front Margin: Đơn giản với 3 case (Percentage, Fixed, Gift) - chỉ cần Priority + AutoApply'
PRINT '✓ Back Margin: Phức tạp với 5 loại chiết khấu - sẽ dùng PromotionCondition + PromotionReward'
PRINT '✓ Header: Chỉ chứa thông tin chung, không có budget management'
PRINT ''
PRINT '🎯 Front Margin Cases:'
PRINT '1. Percentage discount (% hoặc fixed amount)'
PRINT '2. Same item gift (mua A tặng A, price = total value / total quantity)'
PRINT '3. Different item gift (mua A tặng B)'
PRINT ''
PRINT '🎯 Back Margin Cases (sẽ implement sau):'
PRINT '1. Chiết khấu theo doanh số (1.1 Lũy tiến, 1.2 Mốc, 1.3 Bật thang)'
PRINT '2. Chiết khấu theo số lượng (2.1 Lũy tiến, 2.2 Mốc, 2.3 Bật thang)'
PRINT '3. Chiết khấu thanh toán sớm'

-- Create performance index for PO integration
PRINT ''
PRINT 'Creating performance index...'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionHeaders_VendorCode_ProgramType_Priority')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_PromotionHeaders_VendorCode_ProgramType_Priority]
    ON [dbo].[PromotionHeaders] ([VendorCode] ASC, [ProgramType] ASC, [Priority] ASC)
    INCLUDE ([Status], [StartDate], [EndDate], [AutoApply])
    WHERE [Status] = 2 AND [ModificationStatus] = 1 -- Only active promotions
    PRINT '✓ Created index for PO integration queries'
END
ELSE
    PRINT '- Performance index already exists'

PRINT ''
PRINT '🎯 Next Steps:'
PRINT '1. Update existing PromotionHeader records: Priority = 1, AutoApply = true for FM'
PRINT '2. Test Front Margin PO integration'
PRINT '3. Implement Back Margin với PromotionCondition/PromotionReward sau'
