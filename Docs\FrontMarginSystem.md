# Front Margin System Documentation

## Overview

The Front Margin system is a comprehensive promotion management solution that supports 4 different types of discounts for purchase orders. It integrates seamlessly with the Purchase Manager system to provide automatic discount calculations and gift item management.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Discount Types](#discount-types)
3. [Database Schema](#database-schema)
4. [API Endpoints](#api-endpoints)
5. [Calculation Engine](#calculation-engine)
6. [Integration with PO System](#integration-with-po-system)
7. [Usage Examples](#usage-examples)
8. [Testing](#testing)

## System Architecture

### Components

- **FrontMarginController**: REST API endpoints for CRUD operations
- **FrontMarginManager**: Business logic layer
- **FrontMarginCalculationService**: Discount calculation engine
- **FrontMarginValidationService**: Data validation service
- **AutoMapper**: Object mapping between DTOs and entities

### Data Flow

```
Client Request → Controller → Manager → Validation/Calculation Services → Database
```

## Discount Types

The Front Margin system supports 4 distinct discount types:

### Type 1: Percentage Discount

- **Description**: Applies a percentage discount to the item price
- **Formula**: `New Price = Original Price × (1 - Discount%)`
- **Use Case**: General percentage-based discounts (e.g., 10% off)
- **Fields**: `DiscountPercentage`, `MinimumQuantity`, `MinimumAmount`, `MaximumDiscountAmount`

### Type 2: Fixed Amount Discount

- **Description**: Applies a fixed monetary discount
- **Formula**: `New Price = Original Price - (Fixed Amount / Quantity)`
- **Use Case**: Fixed amount discounts for bulk orders
- **Fields**: `FixedDiscountAmount`, `MinimumAmount`, `MaximumDiscountAmount`

### Type 3: Same Item Gift (Buy X Get Y Free)

- **Description**: Buy a certain quantity and get additional items of the same type for free
- **Formula**: `New Unit Cost = Total Value / (Purchase Qty + Gift Qty)`
- **Use Case**: Buy 10 get 2 free promotions
- **Fields**: `BuyQuantity`, `GiftQuantity`, `MinimumQuantity`

### Type 4: Different Item Gift

- **Description**: Purchase an item and receive a different item as a gift
- **Formula**: No price change, adds gift line to PO
- **Use Case**: Buy Product A, get Product B free
- **Fields**: `GiftItemNumber`, `GiftItemName`, `GiftItemUOM`, `GiftItemQuantity`, `MinimumQuantity`

## Database Schema

### PromotionFrontMargins Table

| Column                | Type          | Description                                   |
| --------------------- | ------------- | --------------------------------------------- |
| Number                | NVARCHAR(50)  | Unique identifier (PK)                        |
| ProgramNumber         | NVARCHAR(50)  | Reference to PromotionHeader                  |
| LineNumber            | INT           | Line sequence number                          |
| ItemNumber            | NVARCHAR(50)  | Target item code                              |
| ItemName              | NVARCHAR(250) | Target item name                              |
| UnitOfMeasure         | NVARCHAR(50)  | Unit of measure                               |
| DiscountType          | INT           | 1=Percentage, 2=Fixed, 3=SameGift, 4=DiffGift |
| DiscountPercentage    | DECIMAL(5,2)  | Percentage discount (0-100)                   |
| FixedDiscountAmount   | DECIMAL(18,2) | Fixed discount amount                         |
| BuyQuantity           | DECIMAL(18,4) | Required purchase quantity for gifts          |
| GiftQuantity          | DECIMAL(18,4) | Gift quantity for same item                   |
| GiftItemNumber        | NVARCHAR(50)  | Gift item code                                |
| GiftItemName          | NVARCHAR(250) | Gift item name                                |
| GiftItemUOM           | NVARCHAR(50)  | Gift item unit of measure                     |
| GiftItemQuantity      | DECIMAL(18,4) | Gift item quantity                            |
| MinimumQuantity       | DECIMAL(18,4) | Minimum purchase quantity                     |
| MinimumAmount         | DECIMAL(18,2) | Minimum purchase amount                       |
| MaximumDiscountAmount | DECIMAL(18,2) | Maximum discount limit                        |
| Status                | INT           | 1=Active, 2=Inactive                          |
| Notes                 | NVARCHAR(MAX) | Additional notes                              |

### Constraints

- `CK_PromotionFrontMargins_DiscountType`: DiscountType IN (1, 2, 3, 4)
- Indexes on DiscountType and GiftItemNumber for performance

## API Endpoints

### Base URL: `/api/FrontMargin`

#### GET /filtered

Get Front Margin promotions with filtering and pagination.

**Query Parameters:**

- `programNumber` (string): Filter by program number
- `itemNumber` (string): Filter by item number
- `itemName` (string): Filter by item name
- `vendorCode` (string): Filter by vendor code
- `discountType` (int): Filter by discount type (1-4)
- `status` (int): Filter by status
- `pageSize` (int): Page size (default: 10)
- `pageIndex` (int): Page index (default: 0)

**Response:**

```json
{
  "success": true,
  "message": "Front Margins retrieved successfully",
  "data": {
    "data": [...],
    "currentPage": 0,
    "pageSize": 10,
    "rowCount": 25
  }
}
```

#### GET /{number}

Get Front Margin by unique number.

#### POST /

Create new Front Margin promotion.

**Request Body:**

```json
{
  "programNumber": "FM2025001",
  "lineNumber": 1,
  "itemNumber": "ITEM001",
  "itemName": "Test Item",
  "unitOfMeasure": "PCS",
  "discountType": 1,
  "discountPercentage": 10.5,
  "minimumQuantity": 100,
  "minimumAmount": 1000000,
  "status": 1,
  "notes": "10.5% discount promotion"
}
```

#### PUT /{number}

Update existing Front Margin promotion.

#### DELETE /bulk

Bulk delete Front Margin promotions.

**Request Body:**

```json
["FM202501010001", "FM202501010002"]
```

#### GET /discount-types

Get available discount types for dropdown.

#### POST /validate

Validate Front Margin configuration.

## Calculation Engine

### FrontMarginCalculationService

The calculation engine processes discount applications with the following methods:

#### CalculateLineDiscountAsync

Calculates discount for a single PO line.

**Parameters:**

- `POLineGetDto poLine`: PO line data
- `List<PromotionFrontMargin> applicablePromotions`: Applicable promotions

**Returns:**

- `FrontMarginCalculationResult`: Calculation results including original/final costs, savings, and gift lines

#### ApplyFrontMarginToPOAsync

Applies Front Margin to entire PO.

**Parameters:**

- `POHeaderGetDto poHeader`: PO header data
- `List<POLineGetDto> poLines`: PO lines

**Returns:**

- `POFrontMarginResult`: Complete PO calculation results

### Calculation Logic

#### Type 1: Percentage Discount

```csharp
var discountPercent = promotion.DiscountPercentage / 100;
var newUnitCost = poLine.UnitCost * (1 - discountPercent);
var discountAmount = (poLine.UnitCost - newUnitCost) * poLine.Quantity;

// Apply maximum discount limit if set
if (promotion.MaximumDiscountAmount.HasValue &&
    discountAmount > promotion.MaximumDiscountAmount.Value)
{
    discountAmount = promotion.MaximumDiscountAmount.Value;
    newUnitCost = poLine.UnitCost - (discountAmount / poLine.Quantity);
}
```

#### Type 2: Fixed Amount Discount

```csharp
var discountAmount = promotion.FixedDiscountAmount.Value;

// Apply maximum discount limit if set
if (promotion.MaximumDiscountAmount.HasValue &&
    discountAmount > promotion.MaximumDiscountAmount.Value)
    discountAmount = promotion.MaximumDiscountAmount.Value;

var newUnitCost = Math.Max(0, poLine.UnitCost - (discountAmount / poLine.Quantity));
```

#### Type 3: Same Item Gift

```csharp
var giftSets = Math.Floor(poLine.Quantity / promotion.BuyQuantity.Value);
var totalGiftQuantity = giftSets * promotion.GiftQuantity.Value;

if (totalGiftQuantity > 0)
{
    var totalQuantityIncludingGifts = poLine.Quantity + totalGiftQuantity;
    var newUnitCost = (poLine.UnitCost * poLine.Quantity) / totalQuantityIncludingGifts;
    // Create gift line with zero cost
}
```

#### Type 4: Different Item Gift

```csharp
// No price change to original item
// Create separate gift line
var giftLine = new POLineGetDto
{
    ItemNumber = promotion.GiftItemNumber,
    Description = $"{promotion.GiftItemName} (GIFT)",
    Quantity = promotion.GiftItemQuantity.Value,
    UnitCost = 0,
    DocumentType = 2 // Promotional item
};
```

## Integration with PO System

### Workflow

1. **PO Creation**: When creating a PO, system checks for applicable Front Margin promotions
2. **Promotion Lookup**: Based on vendor code, item numbers, and date range
3. **Calculation**: Apply discount calculations to eligible lines
4. **Gift Lines**: Add gift lines for applicable promotions
5. **PO Update**: Update PO with discounted prices and gift lines

### POFrontMarginController

Provides endpoints for PO integration:

- `POST /api/po-front-margin/applicable-promotions`: Get applicable promotions for PO
- `POST /api/po-front-margin/calculate-line-discount`: Calculate discount for specific line
- `POST /api/po-front-margin/apply-to-po`: Apply Front Margin to entire PO
- `POST /api/po-front-margin/preview`: Preview calculations without applying

## Usage Examples

### Example 1: Create Percentage Discount

```http
POST /api/FrontMargin
Content-Type: application/json

{
  "programNumber": "FM2025001",
  "lineNumber": 1,
  "itemNumber": "PARACETAMOL500",
  "itemName": "Paracetamol 500mg",
  "unitOfMeasure": "Viên",
  "discountType": 1,
  "discountPercentage": 15.0,
  "minimumQuantity": 100,
  "minimumAmount": 500000,
  "status": 1,
  "notes": "15% discount for bulk orders"
}
```

### Example 2: Create Buy 10 Get 2 Free

```http
POST /api/FrontMargin
Content-Type: application/json

{
  "programNumber": "FM2025002",
  "lineNumber": 1,
  "itemNumber": "VITAMIN_C",
  "itemName": "Vitamin C 1000mg",
  "unitOfMeasure": "Viên",
  "discountType": 3,
  "buyQuantity": 10,
  "giftQuantity": 2,
  "minimumQuantity": 10,
  "status": 1,
  "notes": "Buy 10 get 2 free promotion"
}
```

### Example 3: Apply to PO

```http
POST /api/po-front-margin/apply-to-po
Content-Type: application/json

{
  "poHeader": {
    "poNumber": "*********",
    "buyFromVendorNumber": "VENDOR001"
  },
  "poLines": [
    {
      "itemNumber": "PARACETAMOL500",
      "quantity": 200,
      "unitCost": 1000,
      "amount": 200000
    }
  ]
}
```

## Testing

### Unit Tests

- `FrontMarginCalculationTests.cs`: Comprehensive calculation engine tests
  - Tests all 4 discount types
  - Edge cases and error handling
  - Performance validation
- `FrontMarginApiTests.cs`: API endpoint tests
  - CRUD operations
  - Validation scenarios
  - Error handling

### Integration Tests

- Postman collection: `FrontMarginAPI.postman_collection.json`
  - 12 comprehensive test cases
  - Covers all API endpoints
  - Includes validation tests
- SQL test scripts: `TestFrontMarginCalculations.sql`
  - Real data calculation tests
  - Performance benchmarks
  - Edge case scenarios

### Test Data

- Use `CreateFrontMarginTestData.sql` to create comprehensive test data
- Covers all 4 discount types with various scenarios:
  - Type 1: 3 percentage discount scenarios
  - Type 2: 3 fixed amount discount scenarios
  - Type 3: 3 same item gift scenarios
  - Type 4: 3 different item gift scenarios
  - Edge cases for maximum discounts and limits

### Performance Testing

- Calculation engine tested with 1000+ PO lines
- Average processing time: <1ms per line
- Supports concurrent calculations
- Memory usage optimized for large datasets

### Test Execution

#### Running Unit Tests

```bash
dotnet test Server/PurchaseManager.Server.Tests/FrontMarginCalculationTests.cs
dotnet test Server/PurchaseManager.Server.Tests/FrontMarginApiTests.cs
```

#### Running SQL Tests

```sql
-- 1. Run migration
EXEC Server/PurchaseManager.Storage/Migrations/SimpleFrontMarginMigration.sql

-- 2. Verify migration
EXEC Server/PurchaseManager.Storage/Migrations/VerifyFrontMarginMigration.sql

-- 3. Create test data
EXEC Server/PurchaseManager.Storage/Migrations/CreateFrontMarginTestData.sql

-- 4. Run calculation tests
EXEC Server/PurchaseManager.Storage/Migrations/TestFrontMarginCalculations.sql
```

#### Running Postman Tests

1. Import `FrontMarginAPI.postman_collection.json`
2. Set environment variables:
   - `baseUrl`: Your API base URL
   - `testProgramNumber`: Test program number
3. Run collection with test data

## Migration Scripts

1. `SimpleFrontMarginMigration.sql`: Basic migration to add Front Margin columns
2. `RunFrontMarginMigration.sql`: Complete migration with constraints and indexes
3. `VerifyFrontMarginMigration.sql`: Verification script
4. `CreateFrontMarginTestData.sql`: Test data creation
5. `TestFrontMarginCalculations.sql`: Calculation testing

## Best Practices

1. **Validation**: Always validate discount configurations before saving
2. **Performance**: Use indexes on DiscountType and GiftItemNumber
3. **Constraints**: Enforce business rules at database level
4. **Testing**: Test all discount types with edge cases
5. **Documentation**: Keep API documentation updated
6. **Monitoring**: Monitor calculation performance and accuracy

## Troubleshooting

### Common Issues

1. **Migration Errors**: Check SQL syntax and database permissions
2. **Calculation Errors**: Verify promotion configuration and minimum requirements
3. **Performance Issues**: Check indexes and query optimization
4. **Validation Failures**: Review business rules and data constraints

### Support

For technical support or questions, refer to:

- API documentation
- Unit test examples
- SQL test scripts
- Postman collection for testing
