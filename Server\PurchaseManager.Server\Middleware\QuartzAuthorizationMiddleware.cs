using PurchaseManager.Constants;
namespace PurchaseManager.Server.Middleware;

/// <summary>
///     Middleware để kiểm tra authorization cho Quartz UI
///     Chỉ cho phép user có role Administrator t<PERSON>y cập
/// </summary>
public class QuartzAuthorizationMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<QuartzAuthorizationMiddleware> _logger;

    public QuartzAuthorizationMiddleware(RequestDelegate next, ILogger<QuartzAuthorizationMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // <PERSON><PERSON><PERSON> tra nếu request đến Quartz UI
        if (context.Request.Path.StartsWithSegments("/quartz"))
        {
            // Kiểm tra user đã đăng nhập
            if (context.User.Identity is { IsAuthenticated: false })
            {
                _logger.LogWarning("Unauthorized access attempt to Quartz UI from IP: {IP}",
                context.Connection.RemoteIpAddress);
                context.Response.StatusCode = 401;
                await context.Response.WriteAsync("Unauthorized - Authentication required");
                return;
            }

            // Kiểm tra user có role Administrator
            if (!context.User.IsInRole(DefaultRoleNames.Administrator))
            {
                if (context.User.Identity != null)
                {
                    _logger.LogWarning("Forbidden access attempt to Quartz UI by user: {User} from IP: {IP}",
                    context.User.Identity.Name, context.Connection.RemoteIpAddress);
                }
                context.Response.StatusCode = 403;
                await context.Response.WriteAsync("Forbidden - Administrator role required");
                return;
            }

            if (context.User.Identity != null)
            {
                _logger.LogInformation("Authorized access to Quartz UI by Administrator: {User}",
                context.User.Identity.Name);
            }
        }

        await _next(context);
    }
}
