﻿using System.ComponentModel.DataAnnotations;
namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
///     DTO for creating a new Front Margin promotion
/// </summary>
public class CreateFrontMarginPromotionDto
{
    [Display(Name = "Mã chương trình")]
    public string ProgramCode { get; set; } = string.Empty;
    public string ProgramName { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Priority { get; set; } = 1;
    public decimal? BudgetAmount { get; set; }
    public decimal? MinOrderValue { get; set; }
    public decimal? MaxOrderValue { get; set; }
    public decimal? MaxDiscountAmount { get; set; }
    public string? ApplicableDocTypes { get; set; }
    public bool AccumulateRevenue { get; set; } = false;

    public List<CreatePromotionConditionDto> Conditions { get; set; } = [];
    public List<CreatePromotionRewardDto> Rewards { get; set; } = [];
}
