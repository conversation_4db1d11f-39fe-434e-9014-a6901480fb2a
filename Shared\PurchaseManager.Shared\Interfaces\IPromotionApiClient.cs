using PurchaseManager.Shared.Dto;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
using PurchaseManager.Shared.Models.Promotions;
namespace PurchaseManager.Shared.Interfaces;

public interface IPromotionApiClient
{
    #region Promotion Header
    Task<ApiResponseDto<PagedResult<GetPromotionHeaderDto>>> GetPromotionsAsync(PromotionFilter filter);
    Task<ApiResponseDto<GetPromotionHeaderDto>> GetPromotionHeaderByNumberAsync(string number);
    Task<ApiResponseDto<CreatePromotionHeaderDto>> CreatePromotionHeaderAsync(CreatePromotionHeaderDto createDto);
    Task<ApiResponseDto<int>> OpenDocumentAsync(string documentNumber);
    Task<ApiResponseDto<int>> CloseDocumentAsync(string documentNumber);
    Task<ApiResponseDto<int>> ApprovePromotionAsync(string number);
    Task<ApiResponseDto<int>> UpdatePromotionHeaderAsync(string number, UpdatePromotionHeaderDto updateDto);
    Task<ApiResponseDto> DeletePromotionHeaderAsync(List<string> numbers);
    #endregion

    #region Promotion Front Margin
    Task<ApiResponseDto> CreatePromotionFrontMarginAsync(CreatePromotionFrontMarginDto createDto);
    Task<ApiResponseDto<int>> UpdatePromotionFrontMarginAsync(string number, UpdatePromotionFrontMarginDto updateDto);
    Task<ApiResponseDto> DeletePromotionFrontMarginAsync(List<string> number);
    #endregion
}
