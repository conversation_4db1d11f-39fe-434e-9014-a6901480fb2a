using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PurchaseManager.Infrastructure.Storage.DataModels;
namespace PurchaseManager.Storage.Configurations;

/// <summary>
/// Entity Framework configuration for PromotionHeader
/// </summary>
public class PromotionHeaderConfiguration : IEntityTypeConfiguration<PromotionHeader>
{
    public void Configure(EntityTypeBuilder<PromotionHeader> builder)
    {
        builder.ToTable("PromotionHeaders");

        // Primary Key
        builder.HasKey(e => e.Number);
        builder.HasAlternateKey(e => e.RowId);

        // Properties
        builder.Property(e => e.Number)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.Description)
            .HasMaxLength(500)
            .IsRequired();

        builder.Property(e => e.VendorCode)
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(e => e.ApprovalBy)
            .HasMaxLength(100);

        builder.Property(e => e.UsingId)
            .HasMaxLength(100);

        // Default values
        builder.Property(e => e.AccumulateRevenue)
            .HasDefaultValue(false);

        builder.Property(e => e.ProgramType)
            .HasDefaultValue(1);

        builder.Property(e => e.Status)
            .HasDefaultValue(1);

        builder.Property(e => e.Priority)
            .HasDefaultValue(1);

        builder.Property(e => e.ApprovalStatus)
            .HasDefaultValue(1);

        // Indexes
        builder.HasIndex(e => e.ProgramType);
        builder.HasIndex(e => e.Status);
        builder.HasIndex(e => e.VendorCode);
        builder.HasIndex(e => new { e.StartDate, e.EndDate });
        builder.HasIndex(e => e.ApprovalStatus);
        builder.HasIndex(e => e.UsingId);
        builder.HasIndex(e => e.Priority);

        // Navigation properties
        builder.HasMany(e => e.PromotionConditions)
            .WithOne(e => e.PromotionHeader)
            .HasForeignKey(e => e.PromotionNumber)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(e => e.PromotionRewards)
            .WithOne(e => e.PromotionHeader)
            .HasForeignKey(e => e.PromotionNumber)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
