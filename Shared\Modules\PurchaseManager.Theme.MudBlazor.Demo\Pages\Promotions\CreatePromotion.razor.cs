using Microsoft.AspNetCore.Components;
using MudBlazor;
using PurchaseManager.Constants.Enum;
using PurchaseManager.Shared.Dto.Promotions;
using PurchaseManager.Shared.Models;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.UI.Base.Shared.Components;

namespace PurchaseManager.Theme.Material.Demo.Pages.Promotions;

public class CreatePromotionPage : BaseComponent
{
    #region DIs
    [Inject] protected IViewNotifier ViewNotifier { get; set; }
    #endregion

    #region Parameters
    [Parameter] public string? Number { get; set; }
    [Parameter] public bool IsDialogVisible { get; set; }
    [Parameter] public EventCallback<bool> IsDialogVisibleChanged { get; set; }
    [Parameter] public EventCallback OnPromotionSaved { get; set; }
    #endregion

    #region Properties
    protected bool IsLoading { get; set; }
    protected CreatePromotionHeaderDto CreatePromotionHeaderDto { get; set; } = new CreatePromotionHeaderDto();
    protected GetVendorDto? SelectedVendor { get; set; }
    protected MudForm Form { get; set; }
    protected bool IsFormValid { get; set; }
    protected MudAutocomplete<GetVendorDto> AutoComplete { get; set; }
    protected string SelectedVendorNumber { get; set; }
    [Inject]
    protected HttpClient HttpClient { get; set; }
    [Inject]
    protected IVendorApiClient VendorApiClient { get; set; }
    [Inject]
    protected IPromotionApiClient PromotionApiClient { get; set; }
    // Helper properties for MudDatePicker binding (requires DateTime?)
    protected DateTime? StartDateForPicker
    {
        get => CreatePromotionHeaderDto.StartDate;
        set => CreatePromotionHeaderDto.StartDate = value ?? DateTime.Now.Date;
    }

    protected DateTime? EndDateForPicker
    {
        get => CreatePromotionHeaderDto.EndDate;
        set => CreatePromotionHeaderDto.EndDate = value ?? DateTime.Now.Date.AddDays(30);
    }
    #endregion

    #region Methods
    protected async Task<IEnumerable<GetVendorDto>> VendorAutoComplete(string value, CancellationToken token)
    {
        try
        {
            var apiResponse = await VendorApiClient.GetVendorsByNumber(value, token);
            return apiResponse.IsSuccessStatusCode ? apiResponse.Result : Array.Empty<GetVendorDto>();
        }
        catch (Exception)
        {
            ViewNotifier.Show(L["Error searching vendors"], ViewNotifierType.Error);
            return Array.Empty<GetVendorDto>();
        }
    }

    protected async Task SavePromotion()
    {
        await Form.Validate();
        if (!IsFormValid) return;

        IsLoading = true;
        try
        {
            var createPromotionResponse = await PromotionApiClient.CreatePromotionHeaderAsync(CreatePromotionHeaderDto);
            if (createPromotionResponse.IsSuccessStatusCode)
            {
                ViewNotifier.Show(L["Promotion created successfully"], ViewNotifierType.Success);

                navigationManager.NavigateTo(createPromotionResponse.Result.ProgramType
                                             == (int)PromotionProgramTypeEnum.FrontMargin
                    ? $"/promotional/front-margin/{createPromotionResponse.Result.Number}"
                    : $"/promotional/back-margin/{createPromotionResponse.Result.Number}");
            }
            else
            {
                ViewNotifier.Show(L["Error creating promotion"], ViewNotifierType.Error);
                return;
            }
            await OnPromotionSaved.InvokeAsync();
            await CloseDialog();
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.Message, ViewNotifierType.Error);
        }
        finally
        {
            IsLoading = false;
        }
    }

    protected async Task Cancel()
    {
        await CloseDialog();
    }

    protected async Task CloseDialog()
    {
        await IsDialogVisibleChanged.InvokeAsync(false);
    }

    public async Task OpenDialog(string? promotionNumber = null)
    {
        Number = promotionNumber;
        // Reset form for new promotion
        CreatePromotionHeaderDto = new CreatePromotionHeaderDto();
        SelectedVendor = null;
        await IsDialogVisibleChanged.InvokeAsync(true);
    }

    protected static string GetPromotionTypeText(int type)
    {
        return type switch
        {
            (int)PromotionProgramTypeEnum.FrontMargin => "Chiết khấu trả trước",
            (int)PromotionProgramTypeEnum.BackMargin => "Chiết khấu trả sau",
            _ => "Unknown"
        };
    }

    protected static string GetStatusText(int status)
    {
        return status switch
        {
            (int)PromotionStatusEnum.Draft => "Draft",
            (int)PromotionStatusEnum.Active => "Active",
            (int)PromotionStatusEnum.Inactive => "Inactive",
            (int)PromotionStatusEnum.Expired => "Expired",
            _ => "Unknown"
        };
    }
    #endregion
}
