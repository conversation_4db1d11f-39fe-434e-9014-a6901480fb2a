-- =============================================
-- Script: Rollback PromotionHeader Front Margin Integration Changes
-- Description: Remove columns added for Front Margin integration (USE WITH CAUTION!)
-- Date: 2025-01-15
-- WARNING: This will permanently delete data in the new columns!
-- =============================================

USE [PurchaseManager] -- Replace with your database name
GO

PRINT '⚠️  WARNING: This script will permanently delete Front Margin integration columns!'
PRINT '⚠️  Make sure you have a backup before proceeding!'
PRINT ''

-- Uncomment the line below to enable rollback (safety measure)
-- DECLARE @ENABLE_ROLLBACK BIT = 1

IF NOT EXISTS (SELECT * FROM sys.objects WHERE name = 'ENABLE_ROLLBACK')
BEGIN
    PRINT '❌ Rollback is disabled for safety.'
    PRINT '❌ To enable rollback, uncomment the DECLARE @ENABLE_ROLLBACK line above.'
    RETURN
END

PRINT 'Starting rollback of PromotionHeader changes...'

BEGIN TRANSACTION

BEGIN TRY
    -- Drop indexes first
    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionHeaders_Priority_AutoApply')
    BEGIN
        DROP INDEX [IX_PromotionHeaders_Priority_AutoApply] ON [dbo].[PromotionHeaders]
        PRINT '✓ Dropped index IX_PromotionHeaders_Priority_AutoApply'
    END

    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_PromotionHeaders_VendorCode_ProgramType_Status')
    BEGIN
        DROP INDEX [IX_PromotionHeaders_VendorCode_ProgramType_Status] ON [dbo].[PromotionHeaders]
        PRINT '✓ Dropped index IX_PromotionHeaders_VendorCode_ProgramType_Status'
    END

    -- Remove columns in reverse order of creation
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'SpecialConditions')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [SpecialConditions]
        PRINT '✓ Removed SpecialConditions column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'EvaluationPeriod')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [EvaluationPeriod]
        PRINT '✓ Removed EvaluationPeriod column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'DefaultPaymentTiming')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [DefaultPaymentTiming]
        PRINT '✓ Removed DefaultPaymentTiming column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'DefaultPaymentMethod')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [DefaultPaymentMethod]
        PRINT '✓ Removed DefaultPaymentMethod column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'RequireApprovalBeforeReward')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [RequireApprovalBeforeReward]
        PRINT '✓ Removed RequireApprovalBeforeReward column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApplyToAllVendorItems')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [ApplyToAllVendorItems]
        PRINT '✓ Removed ApplyToAllVendorItems column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'ApplicableDocTypes')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [ApplicableDocTypes]
        PRINT '✓ Removed ApplicableDocTypes column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxDiscountAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [MaxDiscountAmount]
        PRINT '✓ Removed MaxDiscountAmount column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxOrderValue')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [MaxOrderValue]
        PRINT '✓ Removed MaxOrderValue column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MinOrderValue')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [MinOrderValue]
        PRINT '✓ Removed MinOrderValue column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'UsedAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [UsedAmount]
        PRINT '✓ Removed UsedAmount column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'MaxBudgetAmount')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [MaxBudgetAmount]
        PRINT '✓ Removed MaxBudgetAmount column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'AutoApply')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [AutoApply]
        PRINT '✓ Removed AutoApply column'
    END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[PromotionHeaders]') AND name = 'Priority')
    BEGIN
        ALTER TABLE [dbo].[PromotionHeaders] DROP COLUMN [Priority]
        PRINT '✓ Removed Priority column'
    END

    COMMIT TRANSACTION
    PRINT ''
    PRINT '✅ Rollback completed successfully!'
    PRINT ''

END TRY
BEGIN CATCH
    ROLLBACK TRANSACTION
    PRINT ''
    PRINT '❌ Error during rollback:'
    PRINT ERROR_MESSAGE()
    PRINT ''
END CATCH

-- Verify the rollback
PRINT 'Verifying rollback...'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'Priority', 'AutoApply', 'MaxBudgetAmount', 'UsedAmount',
        'MinOrderValue', 'MaxOrderValue', 'MaxDiscountAmount',
        'ApplicableDocTypes', 'ApplyToAllVendorItems', 'RequireApprovalBeforeReward',
        'DefaultPaymentMethod', 'DefaultPaymentTiming', 'EvaluationPeriod', 'SpecialConditions'
    )
ORDER BY COLUMN_NAME

DECLARE @RemainingColumns INT
SELECT @RemainingColumns = COUNT(*)
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'PromotionHeaders'
    AND COLUMN_NAME IN (
        'Priority', 'AutoApply', 'MaxBudgetAmount', 'UsedAmount',
        'MinOrderValue', 'MaxOrderValue', 'MaxDiscountAmount',
        'ApplicableDocTypes', 'ApplyToAllVendorItems', 'RequireApprovalBeforeReward',
        'DefaultPaymentMethod', 'DefaultPaymentTiming', 'EvaluationPeriod', 'SpecialConditions'
    )

IF @RemainingColumns = 0
BEGIN
    PRINT ''
    PRINT '✅ All Front Margin integration columns have been removed successfully!'
END
ELSE
BEGIN
    PRINT ''
    PRINT '⚠️  Warning: ' + CAST(@RemainingColumns AS VARCHAR(10)) + ' columns still remain!'
    PRINT '⚠️  Please check the output above for any errors.'
END

PRINT ''
PRINT '📋 Post-Rollback Actions Required:'
PRINT '1. Update application code to remove references to deleted columns'
PRINT '2. Update DTOs and mapping profiles'
PRINT '3. Remove Front Margin integration services if no longer needed'
PRINT '4. Test application to ensure no errors'
PRINT ''
PRINT '⚠️  Remember: This rollback cannot be undone without re-running the original scripts!'
