-- =============================================
-- Test Front Margin Calculations
-- Execute this to test calculation logic with real data
-- Date: 2025-01-11
-- =============================================

USE [PurchaseManager] -- Change to your database name
GO

PRINT 'Testing Front Margin Calculations...'
PRINT 'Database: ' + DB_NAME()
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
PRINT '============================================='

-- Check if test data exists
IF NOT EXISTS (SELECT * FROM PromotionFrontMargins WHERE ProgramNumber LIKE 'TEST_FM_%')
BEGIN
    PRINT 'ERROR: Test data not found. Please run CreateFrontMarginTestData.sql first.'
    RETURN
END

-- Test scenarios for each discount type
PRINT 'Testing calculation scenarios...'
PRINT ''

-- =============================================
-- TYPE 1: Percentage Discount Tests
-- =============================================
PRINT '1. PERCENTAGE DISCOUNT TESTS:'
PRINT '============================================='

-- Test Case 1.1: Valid percentage discount
DECLARE @ItemCost1 DECIMAL(18,2) = 10000 -- 10,000 VND per unit
DECLARE @Quantity1 DECIMAL(18,4) = 100 -- 100 units
DECLARE @TotalAmount1 DECIMAL(18,2) = @ItemCost1 * @Quantity1 -- 1,000,000 VND

SELECT 
    'Test 1.1: Valid Percentage Discount' as TestCase,
    fm.ItemNumber,
    fm.ItemName,
    fm.DiscountPercentage,
    @ItemCost1 as OriginalUnitCost,
    @Quantity1 as Quantity,
    @TotalAmount1 as OriginalAmount,
    @ItemCost1 * (1 - fm.DiscountPercentage / 100) as NewUnitCost,
    (@ItemCost1 * (1 - fm.DiscountPercentage / 100)) * @Quantity1 as NewAmount,
    @TotalAmount1 - ((@ItemCost1 * (1 - fm.DiscountPercentage / 100)) * @Quantity1) as Savings,
    CASE 
        WHEN @Quantity1 >= ISNULL(fm.MinimumQuantity, 0) AND @TotalAmount1 >= ISNULL(fm.MinimumAmount, 0) 
        THEN 'APPLICABLE' 
        ELSE 'NOT APPLICABLE' 
    END as Status
FROM PromotionFrontMargins fm
WHERE fm.DiscountType = 1 AND fm.ProgramNumber LIKE 'TEST_FM_%'

-- Test Case 1.2: Below minimum quantity
DECLARE @ItemCost1_2 DECIMAL(18,2) = 10000
DECLARE @Quantity1_2 DECIMAL(18,4) = 50 -- Below minimum
DECLARE @TotalAmount1_2 DECIMAL(18,2) = @ItemCost1_2 * @Quantity1_2

SELECT 
    'Test 1.2: Below Minimum Quantity' as TestCase,
    fm.ItemNumber,
    fm.MinimumQuantity,
    @Quantity1_2 as ActualQuantity,
    CASE 
        WHEN @Quantity1_2 >= ISNULL(fm.MinimumQuantity, 0) 
        THEN 'DISCOUNT APPLIES' 
        ELSE 'NO DISCOUNT' 
    END as Result
FROM PromotionFrontMargins fm
WHERE fm.DiscountType = 1 AND fm.ProgramNumber LIKE 'TEST_FM_%'

PRINT ''

-- =============================================
-- TYPE 2: Fixed Amount Discount Tests
-- =============================================
PRINT '2. FIXED AMOUNT DISCOUNT TESTS:'
PRINT '============================================='

-- Test Case 2.1: Valid fixed amount discount
DECLARE @ItemCost2 DECIMAL(18,2) = 15000
DECLARE @Quantity2 DECIMAL(18,4) = 100
DECLARE @TotalAmount2 DECIMAL(18,2) = @ItemCost2 * @Quantity2 -- 1,500,000 VND

SELECT 
    'Test 2.1: Valid Fixed Amount Discount' as TestCase,
    fm.ItemNumber,
    fm.ItemName,
    fm.FixedDiscountAmount,
    fm.MaximumDiscountAmount,
    @ItemCost2 as OriginalUnitCost,
    @Quantity2 as Quantity,
    @TotalAmount2 as OriginalAmount,
    CASE 
        WHEN fm.MaximumDiscountAmount IS NOT NULL AND fm.FixedDiscountAmount > fm.MaximumDiscountAmount
        THEN fm.MaximumDiscountAmount
        ELSE fm.FixedDiscountAmount
    END as AppliedDiscount,
    @ItemCost2 - (
        CASE 
            WHEN fm.MaximumDiscountAmount IS NOT NULL AND fm.FixedDiscountAmount > fm.MaximumDiscountAmount
            THEN fm.MaximumDiscountAmount
            ELSE fm.FixedDiscountAmount
        END / @Quantity2
    ) as NewUnitCost,
    CASE 
        WHEN @TotalAmount2 >= ISNULL(fm.MinimumAmount, 0) 
        THEN 'APPLICABLE' 
        ELSE 'NOT APPLICABLE' 
    END as Status
FROM PromotionFrontMargins fm
WHERE fm.DiscountType = 2 AND fm.ProgramNumber LIKE 'TEST_FM_%'

PRINT ''

-- =============================================
-- TYPE 3: Same Item Gift Tests
-- =============================================
PRINT '3. SAME ITEM GIFT TESTS:'
PRINT '============================================='

-- Test Case 3.1: Valid same item gift
DECLARE @ItemCost3 DECIMAL(18,2) = 8000
DECLARE @Quantity3 DECIMAL(18,4) = 25 -- Should get gifts for 2 complete sets
DECLARE @TotalAmount3 DECIMAL(18,2) = @ItemCost3 * @Quantity3

SELECT 
    'Test 3.1: Valid Same Item Gift' as TestCase,
    fm.ItemNumber,
    fm.ItemName,
    fm.BuyQuantity,
    fm.GiftQuantity,
    @Quantity3 as PurchaseQuantity,
    FLOOR(@Quantity3 / fm.BuyQuantity) as CompleteSets,
    FLOOR(@Quantity3 / fm.BuyQuantity) * fm.GiftQuantity as TotalGiftQuantity,
    @Quantity3 + (FLOOR(@Quantity3 / fm.BuyQuantity) * fm.GiftQuantity) as TotalQuantityIncludingGifts,
    (@ItemCost3 * @Quantity3) / (@Quantity3 + (FLOOR(@Quantity3 / fm.BuyQuantity) * fm.GiftQuantity)) as NewUnitCost,
    @ItemCost3 - ((@ItemCost3 * @Quantity3) / (@Quantity3 + (FLOOR(@Quantity3 / fm.BuyQuantity) * fm.GiftQuantity))) as UnitCostSavings,
    CASE 
        WHEN @Quantity3 >= ISNULL(fm.MinimumQuantity, 0) 
        THEN 'APPLICABLE' 
        ELSE 'NOT APPLICABLE' 
    END as Status
FROM PromotionFrontMargins fm
WHERE fm.DiscountType = 3 AND fm.ProgramNumber LIKE 'TEST_FM_%'

PRINT ''

-- =============================================
-- TYPE 4: Different Item Gift Tests
-- =============================================
PRINT '4. DIFFERENT ITEM GIFT TESTS:'
PRINT '============================================='

-- Test Case 4.1: Valid different item gift
DECLARE @ItemCost4 DECIMAL(18,2) = 12000
DECLARE @Quantity4 DECIMAL(18,4) = 100 -- Above minimum
DECLARE @TotalAmount4 DECIMAL(18,2) = @ItemCost4 * @Quantity4

SELECT 
    'Test 4.1: Valid Different Item Gift' as TestCase,
    fm.ItemNumber,
    fm.ItemName,
    fm.GiftItemNumber,
    fm.GiftItemName,
    fm.GiftItemQuantity,
    @Quantity4 as PurchaseQuantity,
    @ItemCost4 as OriginalUnitCost,
    @ItemCost4 as NewUnitCost, -- No change for different item gift
    fm.GiftItemQuantity as GiftQuantityReceived,
    fm.GiftItemQuantity * 0 as GiftValue, -- Gift items have zero cost
    CASE 
        WHEN @Quantity4 >= ISNULL(fm.MinimumQuantity, 0) 
        THEN 'GIFT APPLIES' 
        ELSE 'NO GIFT' 
    END as Status
FROM PromotionFrontMargins fm
WHERE fm.DiscountType = 4 AND fm.ProgramNumber LIKE 'TEST_FM_%'

PRINT ''

-- =============================================
-- EDGE CASES TESTS
-- =============================================
PRINT '5. EDGE CASES TESTS:'
PRINT '============================================='

-- Test Case 5.1: Maximum percentage discount (100%)
SELECT 
    'Test 5.1: Maximum Percentage Discount' as TestCase,
    fm.ItemNumber,
    fm.DiscountPercentage,
    10000 as OriginalUnitCost,
    10000 * (1 - fm.DiscountPercentage / 100) as NewUnitCost,
    CASE 
        WHEN fm.DiscountPercentage = 100 THEN 'FREE ITEM' 
        ELSE 'DISCOUNTED' 
    END as Result
FROM PromotionFrontMargins fm
WHERE fm.DiscountType = 1 AND fm.DiscountPercentage = 100 AND fm.ProgramNumber LIKE 'TEST_FM_%'

-- Test Case 5.2: Buy 1 Get 1 Free scenario
SELECT 
    'Test 5.2: Buy 1 Get 1 Free' as TestCase,
    fm.ItemNumber,
    fm.BuyQuantity,
    fm.GiftQuantity,
    CAST(fm.GiftQuantity AS DECIMAL(5,2)) / CAST(fm.BuyQuantity AS DECIMAL(5,2)) * 100 as EffectiveDiscountPercentage
FROM PromotionFrontMargins fm
WHERE fm.DiscountType = 3 AND fm.BuyQuantity = fm.GiftQuantity AND fm.ProgramNumber LIKE 'TEST_FM_%'

PRINT ''

-- =============================================
-- SUMMARY CALCULATIONS
-- =============================================
PRINT '6. SUMMARY CALCULATIONS:'
PRINT '============================================='

-- Summary of all discount types and their potential savings
SELECT 
    DiscountType,
    CASE DiscountType
        WHEN 1 THEN 'Percentage Discount'
        WHEN 2 THEN 'Fixed Amount Discount'
        WHEN 3 THEN 'Same Item Gift'
        WHEN 4 THEN 'Different Item Gift'
    END as DiscountTypeName,
    COUNT(*) as NumberOfPromotions,
    AVG(CASE WHEN DiscountType = 1 THEN DiscountPercentage ELSE NULL END) as AvgPercentageDiscount,
    AVG(CASE WHEN DiscountType = 2 THEN FixedDiscountAmount ELSE NULL END) as AvgFixedDiscount,
    AVG(CASE WHEN DiscountType = 3 THEN CAST(GiftQuantity AS DECIMAL(10,2)) / CAST(BuyQuantity AS DECIMAL(10,2)) * 100 ELSE NULL END) as AvgGiftPercentage
FROM PromotionFrontMargins
WHERE ProgramNumber LIKE 'TEST_FM_%' AND ModificationStatus = 1
GROUP BY DiscountType
ORDER BY DiscountType

-- Test calculation performance
DECLARE @StartTime DATETIME2 = SYSDATETIME()

-- Simulate calculation for 1000 PO lines
DECLARE @Counter INT = 1
DECLARE @TestResults TABLE (
    LineNumber INT,
    DiscountType INT,
    OriginalAmount DECIMAL(18,2),
    DiscountAmount DECIMAL(18,2),
    CalculationTime_ms INT
)

WHILE @Counter <= 100 -- Reduced for demo
BEGIN
    DECLARE @LineStartTime DATETIME2 = SYSDATETIME()
    
    -- Simulate calculation logic here
    INSERT INTO @TestResults (LineNumber, DiscountType, OriginalAmount, DiscountAmount, CalculationTime_ms)
    SELECT 
        @Counter,
        1,
        100000,
        10000,
        DATEDIFF(MICROSECOND, @LineStartTime, SYSDATETIME()) / 1000
    
    SET @Counter = @Counter + 1
END

DECLARE @EndTime DATETIME2 = SYSDATETIME()
DECLARE @TotalTime INT = DATEDIFF(MILLISECOND, @StartTime, @EndTime)

SELECT 
    'Performance Test Results' as TestType,
    COUNT(*) as LinesProcessed,
    @TotalTime as TotalTime_ms,
    @TotalTime / COUNT(*) as AvgTimePerLine_ms,
    SUM(OriginalAmount) as TotalOriginalAmount,
    SUM(DiscountAmount) as TotalDiscountAmount,
    (SUM(DiscountAmount) / SUM(OriginalAmount)) * 100 as OverallDiscountPercentage
FROM @TestResults

PRINT ''
PRINT '============================================='
PRINT 'Front Margin Calculation Tests Completed!'
PRINT 'Time: ' + CONVERT(varchar, GETDATE(), 120)
PRINT '============================================='
