using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PurchaseManager.Infrastructure.Storage.DataModels;

namespace PurchaseManager.Storage.Configurations;

/// <summary>
/// Entity Framework configuration for PromotionReward
/// </summary>
public class PromotionRewardConfiguration : IEntityTypeConfiguration<PromotionReward>
{
    public void Configure(EntityTypeBuilder<PromotionReward> builder)
    {
        builder.ToTable("PromotionRewards");

        // Primary Key
        builder.HasKey(e => e.Number);
        builder.HasAlternateKey(e => e.RowId);

        // Properties
        builder.Property(e => e.Number)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.PromotionNumber)
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(e => e.RewardType)
            .IsRequired();

        builder.Property(e => e.ConditionItemNumber)
            .HasMaxLength(50);

        builder.Property(e => e.RewardItemNumber)
            .HasMaxLength(50);

        builder.Property(e => e.ItemName)
            .HasMaxLength(500)
            .HasDefaultValue(string.Empty);

        builder.Property(e => e.UnitOfMeasure)
            .HasMaxLength(20)
            .HasDefaultValue(string.Empty);

        builder.Property(e => e.Quantity)
            .HasColumnType("decimal(18,4)");

        builder.Property(e => e.DiscountPercent)
            .HasColumnType("decimal(18,4)");

        builder.Property(e => e.DiscountAmount)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.VoucherValue)
            .HasColumnType("decimal(18,2)");

        builder.Property(e => e.VoucherCode)
            .HasMaxLength(100);

        builder.Property(e => e.Notes)
            .HasMaxLength(1000);

        builder.Property(e => e.ConditionDescription)
            .HasMaxLength(2000);

        // Indexes
        builder.HasIndex(e => e.PromotionNumber);
        builder.HasIndex(e => e.RewardType);
        builder.HasIndex(e => e.ConditionItemNumber);
        builder.HasIndex(e => e.RewardItemNumber);

        // Foreign key
        builder.HasOne(e => e.PromotionHeader)
            .WithMany(e => e.PromotionRewards)
            .HasForeignKey(e => e.PromotionNumber)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
