﻿namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

/// <summary>
///     DTO for updating Front Margin promotion
/// </summary>
public class UpdateFrontMarginPromotionDto
{
    public string ProgramCode { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public int Priority { get; set; }
    public decimal? BudgetAmount { get; set; }
    public decimal? MinOrderValue { get; set; }
    public decimal? MaxOrderValue { get; set; }
    public decimal? MaxDiscountAmount { get; set; }
    public string? ApplicableDocTypes { get; set; }
    public bool AccumulateRevenue { get; set; }
    public int Status { get; set; }

    public List<CreatePromotionConditionDto> Conditions { get; set; } = [];
    public List<CreatePromotionRewardDto> Rewards { get; set; } = [];
}
