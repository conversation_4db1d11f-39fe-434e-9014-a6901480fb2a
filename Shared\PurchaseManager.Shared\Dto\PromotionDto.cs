namespace PurchaseManager.Shared.Dto;

/// <summary>
///     DTO for promotion search/filter
/// </summary>
public class FrontMarginPromotionSearchDto
{
    public string? VendorCode { get; set; }
    public string? Number { get; set; }
    public string? ProgramCode { get; set; }
    public string? Description { get; set; }
    public int? Status { get; set; }
    public int? ApprovalStatus { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? StartDateFrom { get; set; }
    public DateTime? StartDateTo { get; set; }
    public DateTime? EndDateFrom { get; set; }
    public DateTime? EndDateTo { get; set; }
    public int Page { get; set; } = 1;
    public int PageSize { get; set; } = 20;
    public string? SortField { get; set; }
    public bool SortDescending { get; set; } = true;
}
