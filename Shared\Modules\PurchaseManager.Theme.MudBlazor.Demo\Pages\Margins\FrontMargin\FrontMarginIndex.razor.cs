using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using MudBlazor;
using PurchaseManager.Shared.Dto.MarginDto.FrontMargin.Header;
using PurchaseManager.Shared.Interfaces;
using PurchaseManager.Shared.Localizer;
using PurchaseManager.Shared.Models.MarginFilter;
namespace PurchaseManager.Theme.Material.Demo.Pages.Margins.FrontMargin;

public partial class FrontMarginIndex : ComponentBase
{
    [Inject]
    private IStringLocalizer<Global> L { get; set; }
    [Inject]
    private IViewNotifier ViewNotifier { get; set; }
    private bool IsLoading { get; set; }
    private int PageIndex { get; set; }
    private int PageSize { get; set; } = 10;
    private int TotalItems { get; set; }
    private FrontMarginHeaderFilter FrontMarginHeaderFilter { get; set; } = new FrontMarginHeaderFilter();
    public List<GetFontMarginHeaderDto> ListFrontMarginHeader { get; set; } = [];

    protected override void OnInitialized()
    {
        base.OnInitialized();
    }

    private async Task<TableData<GetFontMarginHeaderDto>> ServerReload(TableState state, CancellationToken token)
    {
        OnPage(state.Page, state.PageSize);
        await LoadAllPurchasePriceAsync();
        return new TableData<GetFontMarginHeaderDto>
        {
            TotalItems = TotalItems, Items = ListFrontMarginHeader
        };
    }
    private async Task LoadAllPurchasePriceAsync()
    {
        try
        {
            IsLoading = true;
            // TotalItems = 0;
            // FrontMarginHeaderFilter.PageIndex = PageIndex;
            // FrontMarginHeaderFilter.PageSize = PageSize;
            //
            // if (ListFrontMarginHeader.Count != 0)
            // {
            //     ListFrontMarginHeader.Clear();
            // }
            // FrontMarginHeaderFilter.VendorNumber = null;
            // if (!string.IsNullOrEmpty(SelectedVendor?.Number))
            // {
            //     FrontMarginHeaderFilter.VendorNumber = SelectedVendor.Number;
            // }
            // var responseApi = await PurchasePriceApiClient.GetPurchasePriceByFilterAsync(FrontMarginHeaderFilter);
            // if (responseApi is null)
            // {
            //     ViewNotifier.Show(L["LoadDataFailed"], ViewNotifierType.Error, L["Operation Failed"]);
            // }
            // else
            // {
            //     if (responseApi.Result.RowCount > 0)
            //     {
            //         ListFrontMarginHeader = [.. responseApi.Result.Data.ToList()];
            //         TotalItems = responseApi.Result.RowCount;
            //     }
            //     else
            //     {
            //         ViewNotifier.Show("Not found any data", ViewNotifierType.Info);
            //     }
            // }
        }
        catch (Exception ex)
        {
            ViewNotifier.Show(ex.GetBaseException().Message, ViewNotifierType.Error, L["Operation Failed"]);
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }
    private void OnPage(int index, int size)
    {
        PageSize = size;
        PageIndex = index;
    }

}
