﻿using MudBlazor;
using PurchaseManager.Constants;
namespace PurchaseManager.Theme.Material.Demo.Shared.Components.PO.Services;

public static class POComponentExtension
{
    public static string GetDocNoOccurrenceText(int docNoOccurrence) => docNoOccurrence switch
    {
        (int)DocNoOccurrenceEnum.Order => "Đơn mua.",
        (int)DocNoOccurrenceEnum.Consigned => "Đơn hàng ký gửi.",
        _ => "Đơn hàng khuyến mãi."
    };

    public static string GetStatusText(int status)
    {
        var finalStatus = status switch
        {
            (int)PurchaseOrderEnum.NewCreate => "PO có thể chỉnh sửa",
            (int)PurchaseOrderEnum.Confirm => "PO đang chờ Approve",
            (int)PurchaseOrderEnum.Approve => "PO hoàn tất",
            (int)PurchaseOrderEnum.PartiallyReceived => "Kho đã nhận một phần",
            (int)PurchaseOrderEnum.Completed => "Kho đã nhận đủ",
            (int)PurchaseOrderEnum.CJStatus => "Đơn hàng đã được gửi sang CJL",
            _ => "PO"
        };
        return finalStatus;
    }

    public static Color GetColorByPOStatus(int status) => status switch
    {
        3 => Color.Info,
        4 => Color.Warning,
        5 => Color.Success,
        50 => Color.Primary,
        _ => Color.Tertiary
    };

    public static string DisplayStatus(int status)
    {
        var finalStatus = status switch
        {
            (int)PurchaseOrderEnum.Approve => "Finished",
            (int)PurchaseOrderEnum.PartiallyReceived => "Partially",
            (int)PurchaseOrderEnum.Completed => "Received",
            (int)PurchaseOrderEnum.CJStatus => "CJL",
            _ => "Opened"
        };
        return finalStatus;
    }
}
