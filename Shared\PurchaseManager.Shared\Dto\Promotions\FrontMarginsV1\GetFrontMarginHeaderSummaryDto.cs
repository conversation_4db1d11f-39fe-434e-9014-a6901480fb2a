namespace PurchaseManager.Shared.Dto.Promotions.FrontMargins;

public class GetFrontMarginHeaderSummaryDto
{
    public string Number { get; set; }
    public string ProgramCode { get; set; }
    public string VendorCode { get; set; }
    public DateTime StartDate { get; set; }
    public string Description { get; set; }
    public DateTime EndDate { get; set; }
    public int RowId { get; set; }
    public string CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? LastModifiedBy { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public int ModificationStatus { get; set; }
    public int LinesCount { get; set; }
    public bool IsActive { get; set; }
}
