﻿namespace PurchaseManager.Infrastructure.Storage.DataModels;

public class CJL<PERSON>rLine
{
    public string ReceivingNumber { get; set; }
    public int LineNumber { get; set; }

    public string ItemCode { get; set; }

    public int? DocumentType { get; set; }

    /// <summary>
    ///     <PERSON><PERSON> lượng đặt hàng
    /// </summary>
    public int Quantity { get; set; }

    /// <summary>
    ///     Đơn vị mua
    /// </summary>
    public string UnitOfMeasure { get; set; }

    public string Note { get; set; }

    public DateTime CreateAt { get; set; }

    public string CreateBy { get; set; }

    public DateTime? LastUpdateAt { get; set; }

    public string LastUpdateBy { get; set; }

    public int RowId { get; set; }

    public virtual CJLGrHeader ReceivingNumberNavigation { get; set; }
}
