using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Shared.Dto.Promotions;

public class GetPromotionHeaderDto
{
    public string ProgramCode { get; set; } = string.Empty;
    public string ProgramName { get; set; } = string.Empty;
    public int ProgramType { get; set; } // 1=FrontMargin, 2=BackMargin
    public string Description { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public string VendorName { get; set; } = string.Empty;
    public string ApprovedBy { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    /// <summary>
    /// 1 = Draft,
    /// 2 = Active,
    /// 3 = Inactive,
    /// 4 = Expired
    /// </summary>
    public int Status { get; set; }

    // Business Rules Properties from PromotionHeader
    public int Priority { get; set; } = 1;
    public bool AutoApply { get; set; } = true;
    public decimal? MaxBudgetAmount { get; set; }
    public decimal UsedAmount { get; set; }
    public decimal? MinOrderValue { get; set; }
    public decimal? MaxOrderValue { get; set; }
    public decimal? MaxDiscountAmount { get; set; }
    public string? ApplicableDocTypes { get; set; }
    public bool RequireApprovalBeforeReward { get; set; }
    public bool AccumulateRevenue { get; set; }
    public int? DefaultPaymentMethod { get; set; }
    public int? DefaultPaymentTiming { get; set; }
    public int? EvaluationPeriod { get; set; }
    public bool ApplyToAllVendorItems { get; set; } = true;
    public string? SpecialConditions { get; set; }

    // Approval Workflow
    public int ApprovalStatus { get; set; } = 1;
    public DateTime? ApprovalDate { get; set; }

    public List<GetPromotionFrontMarginDto> FrontMargins { get; set; } = [];
}
