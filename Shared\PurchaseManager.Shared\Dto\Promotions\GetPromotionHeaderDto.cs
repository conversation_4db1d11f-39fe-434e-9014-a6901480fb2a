using PurchaseManager.Shared.Dto.Promotions.FrontMargins;
namespace PurchaseManager.Shared.Dto.Promotions;

public class GetPromotionHeaderDto
{
    public string ProgramCode { get; set; } = string.Empty;
    public string ProgramName { get; set; } = string.Empty;
    public int ProgramType { get; set; } // 1=FrontMargin, 2=BackMargin
    public string Description { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public string VendorName { get; set; } = string.Empty;
    public string ApprovedBy { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    /// <summary>
    /// 1 = Draft,
    /// 2 = Active,
    /// 3 = Inactive,
    /// 4 = Expired
    /// </summary>
    public int Status { get; set; }

    public List<GetPromotionFrontMarginDto> FrontMargins { get; set; } = [];
}
