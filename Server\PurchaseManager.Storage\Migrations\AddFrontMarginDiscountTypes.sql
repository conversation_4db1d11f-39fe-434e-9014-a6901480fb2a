-- Migration: Add Front Margin Discount Types Support
-- Date: 2025-01-11
-- Description: Add fields to support 4 types of Front Margin discounts

-- Add new columns to PromotionFrontMargins table
ALTER TABLE PromotionFrontMargins ADD
    DiscountType INT NOT NULL DEFAULT 1,
    FixedDiscountAmount DECIMAL(18,2) NULL,
    BuyQuantity DECIMAL(18,4) NULL,
    GiftQuantity DECIMAL(18,4) NULL,
    GiftItemNumber NVARCHAR(50) NULL,
    GiftItemName NVARCHAR(250) NULL,
    GiftItemUOM NVARCHAR(50) NULL,
    GiftItemQuantity DECIMAL(18,4) NULL,
    MinimumQuantity DECIMAL(18,4) NULL,
    MinimumAmount DECIMAL(18,2) NULL,
    MaximumDiscountAmount DECIMAL(18,2) NULL;

-- Update existing DiscountPercentage column type for better precision
ALTER TABLE PromotionFrontMargins ALTER COLUMN DiscountPercentage DECIMAL(5,2);

-- Add indexes for performance
CREATE INDEX IX_PromotionFrontMargins_DiscountType ON PromotionFrontMargins(DiscountType);
CREATE INDEX IX_PromotionFrontMargins_GiftItemNumber ON PromotionFrontMargins(GiftItemNumber) WHERE GiftItemNumber IS NOT NULL;

-- Add check constraints for data integrity
ALTER TABLE PromotionFrontMargins ADD CONSTRAINT CK_PromotionFrontMargins_DiscountType 
CHECK (DiscountType IN (1, 2, 3, 4));

-- Conditional constraints for each discount type
ALTER TABLE PromotionFrontMargins ADD CONSTRAINT CK_PromotionFrontMargins_PercentageDiscount
CHECK (
    (DiscountType = 1 AND DiscountPercentage >= 0 AND DiscountPercentage <= 100) OR
    (DiscountType != 1)
);

ALTER TABLE PromotionFrontMargins ADD CONSTRAINT CK_PromotionFrontMargins_FixedAmount
CHECK (
    (DiscountType = 2 AND FixedDiscountAmount > 0) OR
    (DiscountType != 2 OR FixedDiscountAmount IS NULL)
);

ALTER TABLE PromotionFrontMargins ADD CONSTRAINT CK_PromotionFrontMargins_SameItemGift
CHECK (
    (DiscountType = 3 AND BuyQuantity > 0 AND GiftQuantity > 0) OR
    (DiscountType != 3 OR (BuyQuantity IS NULL AND GiftQuantity IS NULL))
);

ALTER TABLE PromotionFrontMargins ADD CONSTRAINT CK_PromotionFrontMargins_DifferentItemGift
CHECK (
    (DiscountType = 4 AND GiftItemNumber IS NOT NULL AND GiftItemQuantity > 0) OR
    (DiscountType != 4 OR (GiftItemNumber IS NULL AND GiftItemQuantity IS NULL))
);

-- Update existing records to have DiscountType = 1 (Percentage) if they have DiscountPercentage > 0
UPDATE PromotionFrontMargins 
SET DiscountType = 1 
WHERE DiscountPercentage > 0;

-- Add comments to document the discount types
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Loại chiết khấu: 1=Percentage, 2=FixedAmount, 3=SameItemGift, 4=DifferentItemGift', 
    @level0type = N'Schema', @level0name = 'dbo', 
    @level1type = N'Table', @level1name = 'PromotionFrontMargins', 
    @level2type = N'Column', @level2name = 'DiscountType';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Chiết khấu theo phần trăm (0-100)', 
    @level0type = N'Schema', @level0name = 'dbo', 
    @level1type = N'Table', @level1name = 'PromotionFrontMargins', 
    @level2type = N'Column', @level2name = 'DiscountPercentage';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Số tiền chiết khấu cố định cho Case I.2', 
    @level0type = N'Schema', @level0name = 'dbo', 
    @level1type = N'Table', @level1name = 'PromotionFrontMargins', 
    @level2type = N'Column', @level2name = 'FixedDiscountAmount';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Số lượng mua để được tặng (Case II)', 
    @level0type = N'Schema', @level0name = 'dbo', 
    @level1type = N'Table', @level1name = 'PromotionFrontMargins', 
    @level2type = N'Column', @level2name = 'BuyQuantity';

EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Số lượng được tặng (Case II)', 
    @level0type = N'Schema', @level0name = 'dbo', 
    @level1type = N'Table', @level1name = 'PromotionFrontMargins', 
    @level2type = N'Column', @level2name = 'GiftQuantity';

PRINT 'Migration completed successfully: AddFrontMarginDiscountTypes';
