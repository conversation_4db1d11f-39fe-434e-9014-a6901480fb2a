# PO Front Margin Integration Guide

## 🎯 Overview

Hướng dẫn tích hợp Front Margin vào PO Creation process, sử dụng PromotionHeader với đầy đủ business rules và auto-apply logic.

## 📋 Services Đã Tạo

### 1. FrontMarginUIService (Updated)
- **Location**: `Shared\PurchaseManager.Shared\Services\FrontMarginUIService.cs`
- **Cập nhật**: Sử dụng PromotionHeader với Priority, AutoApply, MinOrderValue, MaxOrderValue, MaxDiscountAmount
- **Key Methods**:
  - `GetApplicablePromotionsAsync(vendorCode, orderDate, orderValue)` - Lấy promotions với business rules validation
  - `GetLineDiscountAsync(vendorCode, itemNumber, orderDate, orderValue)` - Lấy discount với priority handling
  - `ValidatePOAsync(poHeader, poLines)` - Validate PO theo business rules
  - `GetPromotionsByPriorityAsync(vendorCode, orderDate, orderValue)` - Lấy promotions theo priority

### 2. POFrontMarginIntegrationService (New)
- **Location**: `Shared\PurchaseManager.Shared\Services\POFrontMarginIntegrationService.cs`
- **Purpose**: Tích hợp Front Margin vào PO creation process
- **Key Methods**:
  - `AutoApplyFrontMarginAsync(poHeader, poLines)` - Tự động áp dụng FM discount
  - `ValidatePOWithFrontMarginAsync(poHeader, poLines)` - Validate PO với FM rules
  - `GetFrontMarginSuggestionsAsync(vendorCode, orderDate, itemNumbers)` - Gợi ý FM cho items
  - `CalculatePotentialSavingsAsync(poHeader, poLines)` - Tính toán tiết kiệm tiềm năng

## 🔧 Cách Tích Hợp vào PO Creation UI

### Bước 1: Đăng ký Services trong DI Container

```csharp
// Trong Program.cs hoặc Startup.cs
builder.Services.AddScoped<IFrontMarginUIService, FrontMarginUIService>();
builder.Services.AddScoped<IPOFrontMarginIntegrationService, POFrontMarginIntegrationService>();
```

### Bước 2: Inject Services vào PO Creation Component

```csharp
@inject IFrontMarginUIService FrontMarginService
@inject IPOFrontMarginIntegrationService POFrontMarginService

public partial class POCreationPage : ComponentBase
{
    private POHeaderGetDto POHeader = new();
    private List<POLineGetDto> POLines = new();
    private bool HasActiveFrontMargin = false;
    private List<FrontMarginSuggestion> FrontMarginSuggestions = new();
    private FrontMarginSavingsCalculation SavingsCalculation = new();
}
```

### Bước 3: Kiểm tra Front Margin khi chọn Vendor

```csharp
private async Task OnVendorSelectedAsync(string vendorCode)
{
    POHeader.BuyFromVendorNumber = vendorCode;
    
    // Check if vendor has active Front Margin promotions
    HasActiveFrontMargin = await FrontMarginService.HasActiveFrontMarginAsync(vendorCode);
    
    if (HasActiveFrontMargin)
    {
        // Show Front Margin indicator in UI
        StateHasChanged();
        
        // Get suggestions for existing items
        if (POLines.Any())
        {
            await RefreshFrontMarginSuggestionsAsync();
        }
    }
}
```

### Bước 4: Auto-Apply Front Margin khi thêm Items

```csharp
private async Task OnItemAddedAsync(POLineGetDto newLine)
{
    POLines.Add(newLine);
    
    if (HasActiveFrontMargin)
    {
        // Auto-apply Front Margin if enabled
        var autoApplyResult = await POFrontMarginService.AutoApplyFrontMarginAsync(POHeader, POLines);
        
        if (autoApplyResult.IsSuccess)
        {
            POLines = autoApplyResult.UpdatedLines;
            
            // Show success message
            Snackbar.Add($"Applied Front Margin: {autoApplyResult.Message}", Severity.Success);
            
            // Update savings calculation
            await UpdateSavingsCalculationAsync();
        }
    }
    
    StateHasChanged();
}
```

### Bước 5: Validate PO trước khi Save

```csharp
private async Task<bool> ValidatePOAsync()
{
    if (HasActiveFrontMargin)
    {
        var validationResult = await POFrontMarginService.ValidatePOWithFrontMarginAsync(POHeader, POLines);
        
        if (!validationResult.IsValid)
        {
            // Show validation errors
            foreach (var error in validationResult.ErrorMessages)
            {
                Snackbar.Add(error, Severity.Error);
            }
            return false;
        }
        
        // Show warnings if any
        foreach (var warning in validationResult.WarningMessages)
        {
            Snackbar.Add(warning, Severity.Warning);
        }
    }
    
    return true;
}
```

## 🎨 UI Components Cần Thêm

### 1. Front Margin Indicator

```razor
@if (HasActiveFrontMargin)
{
    <MudAlert Severity="Severity.Info" Icon="@Icons.Material.Filled.LocalOffer">
        <strong>Front Margin Available</strong> - This vendor has active promotions
        @if (SavingsCalculation.TotalSavings > 0)
        {
            <br />Potential savings: <strong>@SavingsCalculation.TotalSavings.ToString("C")</strong> 
            (@SavingsCalculation.SavingsPercentage.ToString("F1")%)
        }
    </MudAlert>
}
```

### 2. Front Margin Suggestions Panel

```razor
@if (FrontMarginSuggestions.Any())
{
    <MudExpansionPanels>
        <MudExpansionPanel Text="Front Margin Suggestions">
            <MudTable Items="FrontMarginSuggestions" Dense="true">
                <HeaderContent>
                    <MudTh>Item</MudTh>
                    <MudTh>Promotion</MudTh>
                    <MudTh>Discount</MudTh>
                    <MudTh>Auto Apply</MudTh>
                    <MudTh>Action</MudTh>
                </HeaderContent>
                <RowTemplate>
                    <MudTd>@context.ItemNumber</MudTd>
                    <MudTd>@context.PromotionName</MudTd>
                    <MudTd>
                        @if (context.DiscountType == 1)
                        {
                            @context.DiscountPercentage.ToString("F1")%
                        }
                        else
                        {
                            @context.DiscountValue.ToString("C")
                        }
                    </MudTd>
                    <MudTd>
                        <MudIcon Icon="@(context.AutoApply ? Icons.Material.Filled.Check : Icons.Material.Filled.Close)" 
                                 Color="@(context.AutoApply ? Color.Success : Color.Default)" />
                    </MudTd>
                    <MudTd>
                        @if (!context.AutoApply)
                        {
                            <MudButton Size="Size.Small" Variant="Variant.Outlined" 
                                       OnClick="() => ApplySpecificPromotionAsync(context.PromotionNumber)">
                                Apply
                            </MudButton>
                        }
                    </MudTd>
                </RowTemplate>
            </MudTable>
        </MudExpansionPanel>
    </MudExpansionPanels>
}
```

### 3. PO Line với Front Margin Indicators

```razor
<MudTable Items="POLines">
    <HeaderContent>
        <MudTh>Item</MudTh>
        <MudTh>Quantity</MudTh>
        <MudTh>Unit Cost</MudTh>
        <MudTh>Amount</MudTh>
        <MudTh>Front Margin</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd>@context.ItemNumber</MudTd>
        <MudTd>@context.Quantity</MudTd>
        <MudTd>
            @if (context.HasFrontMargin)
            {
                <div>
                    <s style="color: gray;">@context.OriginalUnitCost.ToString("C")</s>
                    <br />
                    <strong style="color: green;">@context.UnitCost.ToString("C")</strong>
                </div>
            }
            else
            {
                @context.UnitCost.ToString("C")
            }
        </MudTd>
        <MudTd>@context.Amount.ToString("C")</MudTd>
        <MudTd>
            @if (context.HasFrontMargin)
            {
                <MudChip Size="Size.Small" Color="Color.Success" Icon="@Icons.Material.Filled.LocalOffer">
                    @context.FrontMarginNumber
                </MudChip>
                <br />
                <small>-@context.FrontMarginDiscountAmount.ToString("C")</small>
            }
            else
            {
                <MudIcon Icon="@Icons.Material.Filled.Remove" Color="Color.Default" />
            }
        </MudTd>
    </RowTemplate>
</MudTable>
```

## 🔄 Helper Methods

```csharp
private async Task RefreshFrontMarginSuggestionsAsync()
{
    if (HasActiveFrontMargin && POLines.Any())
    {
        var itemNumbers = POLines.Select(l => l.ItemNumber).ToList();
        FrontMarginSuggestions = await POFrontMarginService.GetFrontMarginSuggestionsAsync(
            POHeader.BuyFromVendorNumber, 
            POHeader.OrderDate, 
            itemNumbers);
    }
}

private async Task UpdateSavingsCalculationAsync()
{
    if (HasActiveFrontMargin && POLines.Any())
    {
        SavingsCalculation = await POFrontMarginService.CalculatePotentialSavingsAsync(POHeader, POLines);
    }
}

private async Task ApplySpecificPromotionAsync(string promotionNumber)
{
    var updatedLines = await POFrontMarginService.ApplySpecificPromotionAsync(POLines, promotionNumber);
    POLines = updatedLines;
    await UpdateSavingsCalculationAsync();
    StateHasChanged();
}

private async Task RemoveAllFrontMarginDiscountsAsync()
{
    var updatedLines = await POFrontMarginService.RemoveFrontMarginDiscountsAsync(POLines);
    POLines = updatedLines;
    SavingsCalculation = new FrontMarginSavingsCalculation();
    StateHasChanged();
}
```

## 🎯 Business Rules Implementation

### Priority Handling
- Promotions được sắp xếp theo Priority (số nhỏ = priority cao)
- Chỉ áp dụng promotion có priority cao nhất cho mỗi item
- Hiển thị warning nếu có conflicting promotions (cùng priority)

### Auto-Apply Logic
- Chỉ áp dụng promotions có `AutoApply = true`
- Validate MinOrderValue, MaxOrderValue trước khi áp dụng
- Respect MaxDiscountAmount constraints

### Validation Rules
- Check MinOrderValue và MaxOrderValue từ PromotionHeader
- Validate MaxDiscountAmount cho từng promotion
- Check budget constraints (UsedAmount vs MaxBudgetAmount)
- Validate ApplicableDocTypes nếu được set

## 🚀 Next Steps

1. **Implement UI Components** - Tạo các components theo mẫu trên
2. **Test Integration** - Test với dữ liệu thực tế
3. **Add Error Handling** - Xử lý các edge cases
4. **Performance Optimization** - Cache promotion data
5. **User Experience** - Thêm loading states và animations

## 📝 Notes

- Services đã được thiết kế để handle tất cả business rules từ PromotionHeader
- Auto-apply logic respect Priority và AutoApply flags
- Validation comprehensive với error và warning messages
- UI components có thể customize theo design system của bạn
