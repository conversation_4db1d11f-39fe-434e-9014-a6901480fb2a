using Microsoft.EntityFrameworkCore;
using PurchaseManager.Infrastructure.Storage.DataModels;
using PurchaseManager.Infrastructure.Storage.DataModels.Base;
using PurchaseManager.Server.Services.Promotions.Interface;
using PurchaseManager.Storage;
namespace PurchaseManager.Server.Services.Promotions;

/// <summary>
/// Service for Front Margin approval workflow
/// </summary>
public class FrontMarginApprovalService : IFrontMarginApprovalService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<FrontMarginApprovalService> _logger;

    public FrontMarginApprovalService(
        ApplicationDbContext context,
        ILogger<FrontMarginApprovalService> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<List<FrontMarginApprovalItem>> GetPendingApprovalsAsync(string? vendorCode = null)
    {
        var query = _context.PromotionFrontMargins
            .Include(p => p.PromotionHeader)
            .ThenInclude(h => h.Vendor)
            .Where(p => p.Status == 1 && // Draft status
                       p.ModificationStatus == (int)ModificationStatusEnum.ACTIVE);

        if (!string.IsNullOrEmpty(vendorCode))
        {
            query = query.Where(p => p.PromotionHeader.VendorCode == vendorCode);
        }

        var items = await query
            .OrderBy(p => p.CreatedAt)
            .Select(p => new FrontMarginApprovalItem
            {
                Number = p.Number,
                ProgramNumber = p.ProgramNumber,
                ProgramName = p.PromotionHeader.ProgramName,
                VendorCode = p.PromotionHeader.VendorCode,
                VendorName = p.PromotionHeader.Vendor != null ? p.PromotionHeader.Vendor.Name : "",
                ItemNumber = p.ItemNumber,
                ItemName = p.ItemName,
                DiscountType = p.DiscountType,
                DiscountTypeName = GetDiscountTypeName(p.DiscountType),
                DiscountValue = GetDiscountValue(p),
                CreatedBy = p.CreatedBy,
                CreatedAt = p.CreatedAt,
                DaysWaiting = (DateTime.Now - p.CreatedAt).Days,
                Priority = CalculatePriority(p),
                EstimatedImpact = CalculateEstimatedImpact(p)
            })
            .ToListAsync();

        return items;
    }

    public async Task<FrontMarginApprovalResult> ApproveAsync(string number, string approvedBy, string? comments = null)
    {
        var frontMargin = await _context.PromotionFrontMargins
            .Include(p => p.PromotionHeader)
            .FirstOrDefaultAsync(p => p.Number == number &&
                                    p.ModificationStatus == (int)ModificationStatusEnum.ACTIVE);

        if (frontMargin == null)
        {
            return new FrontMarginApprovalResult
            {
                Success = false,
                Message = "Không tìm thấy Front Margin"
            };
        }

        if (frontMargin.Status != 1)
        {
            return new FrontMarginApprovalResult
            {
                Success = false,
                Message = "Front Margin không ở trạng thái chờ approval"
            };
        }

        try
        {
            // Update status to Active
            frontMargin.Status = 2; // Active
            frontMargin.LastModifiedBy = approvedBy;
            frontMargin.LastModifiedAt = DateTime.UtcNow;

            // Log approval history
            await LogApprovalHistoryAsync(number, "APPROVED", approvedBy, comments);

            await _context.SaveChangesAsync();

            _logger.LogInformation("Front Margin {Number} approved by {ApprovedBy}", number, approvedBy);

            return new FrontMarginApprovalResult
            {
                Success = true,
                Message = "Front Margin đã được approve thành công",
                NewStatus = 2,
                ApprovedBy = approvedBy,
                ApprovedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error approving Front Margin {Number}", number);
            return new FrontMarginApprovalResult
            {
                Success = false,
                Message = "Lỗi hệ thống khi approve"
            };
        }
    }

    public async Task<FrontMarginApprovalResult> RejectAsync(string number, string rejectedBy, string reason)
    {
        var frontMargin = await _context.PromotionFrontMargins
            .FirstOrDefaultAsync(p => p.Number == number &&
                                    p.ModificationStatus == (int)ModificationStatusEnum.ACTIVE);

        if (frontMargin == null)
        {
            return new FrontMarginApprovalResult
            {
                Success = false,
                Message = "Không tìm thấy Front Margin"
            };
        }

        if (frontMargin.Status != 1)
        {
            return new FrontMarginApprovalResult
            {
                Success = false,
                Message = "Front Margin không ở trạng thái chờ approval"
            };
        }

        try
        {
            // Update status to Inactive (rejected)
            frontMargin.Status = 3; // Inactive
            frontMargin.LastModifiedBy = rejectedBy;
            frontMargin.LastModifiedAt = DateTime.UtcNow;
            frontMargin.Notes = (frontMargin.Notes ?? "") + $"\n[REJECTED] {reason}";

            // Log approval history
            await LogApprovalHistoryAsync(number, "REJECTED", rejectedBy, reason);

            await _context.SaveChangesAsync();

            _logger.LogInformation("Front Margin {Number} rejected by {RejectedBy}. Reason: {Reason}",
                number, rejectedBy, reason);

            return new FrontMarginApprovalResult
            {
                Success = true,
                Message = "Front Margin đã được reject",
                NewStatus = 3,
                ApprovedBy = rejectedBy,
                ApprovedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error rejecting Front Margin {Number}", number);
            return new FrontMarginApprovalResult
            {
                Success = false,
                Message = "Lỗi hệ thống khi reject"
            };
        }
    }

    public async Task<FrontMarginBulkApprovalResult> BulkApproveAsync(List<string> numbers, string approvedBy)
    {
        var result = new FrontMarginBulkApprovalResult
        {
            TotalItems = numbers.Count, SuccessfulItems = [], FailedItems = []
        };

        foreach (var number in numbers)
        {
            var approvalResult = await ApproveAsync(number, approvedBy);

            if (approvalResult.Success)
            {
                result.SuccessfulItems.Add(number);
            }
            else
            {
                result.FailedItems.Add(new BulkApprovalFailure
                {
                    Number = number,
                    Reason = approvalResult.Message
                });
            }
        }

        result.SuccessCount = result.SuccessfulItems.Count;
        result.FailureCount = result.FailedItems.Count;

        _logger.LogInformation("Bulk approval completed: {Success}/{Total} successful",
            result.SuccessCount, result.TotalItems);

        return result;
    }

    public async Task<List<FrontMarginApprovalHistory>> GetApprovalHistoryAsync(string number)
    {
        // For now, return empty list. In real implementation, this would query approval history table
        return [];
    }

    public async Task<bool> CanApproveAsync(string number, string userId)
    {
        var frontMargin = await _context.PromotionFrontMargins
            .FirstOrDefaultAsync(p => p.Number == number);

        if (frontMargin == null || frontMargin.Status != 1)
            return false;

        // Business rule: User cannot approve their own Front Margin
        if (frontMargin.CreatedBy == userId)
            return false;

        // Additional business rules can be added here
        // e.g., role-based approval, approval limits, etc.

        return true;
    }

    #region Private Methods
    private static string GetDiscountTypeName(int discountType)
    {
        return discountType switch
        {
            1 => "Chiết khấu %",
            2 => "Chiết khấu cố định",
            3 => "Tặng hàng cùng loại",
            4 => "Tặng hàng khác loại",
            _ => "Không xác định"
        };
    }

    private static string GetDiscountValue(PromotionFrontMargin p)
    {
        return p.DiscountType switch
        {
            1 => $"{p.DiscountPercentage}%",
            2 => $"{p.FixedDiscountAmount:N0} VND",
            3 => $"Mua {p.BuyQuantity} tặng {p.GiftQuantity}",
            4 => $"Tặng {p.GiftItemQuantity} {p.GiftItemName}",
            _ => "N/A"
        };
    }

    private static string CalculatePriority(PromotionFrontMargin p)
    {
        var daysWaiting = (DateTime.Now - p.CreatedAt).Days;

        return daysWaiting switch
        {
            > 7 => "HIGH",
            > 3 => "MEDIUM",
            _ => "LOW"
        };
    }

    private static string CalculateEstimatedImpact(PromotionFrontMargin p)
    {
        // Simple impact calculation based on discount type and value
        return p.DiscountType switch
        {
            1 when p.DiscountPercentage > 20 => "HIGH",
            1 when p.DiscountPercentage > 10 => "MEDIUM",
            2 when p.FixedDiscountAmount > 1000000 => "HIGH",
            2 when p.FixedDiscountAmount > 500000 => "MEDIUM",
            _ => "LOW"
        };
    }

    private async Task LogApprovalHistoryAsync(string number, string action, string actionBy, string? comments)
    {
        // In real implementation, this would log to an approval history table
        _logger.LogInformation("Front Margin {Number}: {Action} by {ActionBy}. Comments: {Comments}",
            number, action, actionBy, comments ?? "None");
    }

    #endregion
}

#region Result Classes

public class FrontMarginApprovalItem
{
    public string Number { get; set; } = string.Empty;
    public string ProgramNumber { get; set; } = string.Empty;
    public string ProgramName { get; set; } = string.Empty;
    public string VendorCode { get; set; } = string.Empty;
    public string VendorName { get; set; } = string.Empty;
    public string ItemNumber { get; set; } = string.Empty;
    public string ItemName { get; set; } = string.Empty;
    public int DiscountType { get; set; }
    public string DiscountTypeName { get; set; } = string.Empty;
    public string DiscountValue { get; set; } = string.Empty;
    public string? CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public int DaysWaiting { get; set; }
    public string Priority { get; set; } = string.Empty;
    public string EstimatedImpact { get; set; } = string.Empty;
}

public class FrontMarginApprovalResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public int NewStatus { get; set; }
    public string? ApprovedBy { get; set; }
    public DateTime? ApprovedAt { get; set; }
}

public class FrontMarginBulkApprovalResult
{
    public int TotalItems { get; set; }
    public int SuccessCount { get; set; }
    public int FailureCount { get; set; }
    public List<string> SuccessfulItems { get; set; } = [];
    public List<BulkApprovalFailure> FailedItems { get; set; } = [];
}

public class BulkApprovalFailure
{
    public string Number { get; set; } = string.Empty;
    public string Reason { get; set; } = string.Empty;
}

public class FrontMarginApprovalHistory
{
    public string Number { get; set; } = string.Empty;
    public string Action { get; set; } = string.Empty;
    public string ActionBy { get; set; } = string.Empty;
    public DateTime ActionAt { get; set; }
    public string? Comments { get; set; }
}

#endregion
