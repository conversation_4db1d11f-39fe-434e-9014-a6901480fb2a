# Front Margin Database Schema Update

## Overview

Cập nhật database schema để support 4 loại Front Margin discount types:

1. **Case I.1**: <PERSON><PERSON><PERSON> khấu theo phần trăm
2. **Case I.2**: <PERSON><PERSON><PERSON> khấu số tiền cố định
3. **Case II**: <PERSON><PERSON> hàng tặng hàng cùng loại
4. **Case III**: Tặng hàng khác loại

## Database Changes

### 1. PromotionFrontMargin Entity Updates

#### New Fields Added:

```csharp
// Discount Type Identifier
public int DiscountType { get; set; } = 1;

// Case I.2: Fixed Amount Discount
public decimal? FixedDiscountAmount { get; set; }

// Case II: Same Item Gift
public decimal? BuyQuantity { get; set; }
public decimal? GiftQuantity { get; set; }

// Case III: Different Item Gift
public string? GiftItemNumber { get; set; }
public string? GiftItemName { get; set; }
public string? GiftItemUOM { get; set; }
public decimal? GiftItemQuantity { get; set; }

// Conditions
public decimal? MinimumQuantity { get; set; }
public decimal? MinimumAmount { get; set; }
public decimal? MaximumDiscountAmount { get; set; }
```

#### Field Changes:

- `DiscountPercentage`: Changed from `decimal(18,2)` to `decimal(5,2)` for better precision

### 2. Migration Script

- **File**: `Server/PurchaseManager.Storage/Migrations/AddFrontMarginDiscountTypes.sql`
- **Features**:
  - Add new columns with appropriate data types
  - Add indexes for performance
  - Add check constraints for data integrity
  - Update existing records to DiscountType = 1
  - Add column descriptions

### 3. DTO Updates

#### Updated DTOs:

- `CreatePromotionFrontMarginDto`
- `GetPromotionFrontMarginDto`
- `UpdatePromotionFrontMarginDto`

All DTOs now include all new fields to support 4 discount types.

### 4. Validation

#### New Validators:

- `CreatePromotionFrontMarginDtoValidator`
- `UpdatePromotionFrontMarginDtoValidator`

#### Validation Rules:

- **Type 1**: DiscountPercentage must be 0-100
- **Type 2**: FixedDiscountAmount must be > 0
- **Type 3**: BuyQuantity and GiftQuantity must be > 0
- **Type 4**: GiftItem fields must be provided and GiftItemQuantity > 0

### 5. Helper Classes

#### FrontMarginHelper

- `GetDiscountTypeName()`: Get display names
- `ValidatePromotionData()`: Validate promotion configuration
- `GetRequiredFields()`: Get required fields per type
- `IsValidConfiguration()`: Check if promotion is properly configured
- `GetPromotionDisplayText()`: Get display text for UI

#### PromotionFrontMarginExtensions

- Type checking methods: `IsPercentageDiscount()`, `IsFixedAmountDiscount()`, etc.
- Validation methods: `HasValidConfiguration()`, `IsApplicable()`
- Calculation methods: `CalculateGiftQuantity()`, etc.

## Database Constraints

### Check Constraints Added:

1. `CK_PromotionFrontMargins_DiscountType`: DiscountType must be 1,2,3,4
2. `CK_PromotionFrontMargins_PercentageDiscount`: Type 1 validation
3. `CK_PromotionFrontMargins_FixedAmount`: Type 2 validation
4. `CK_PromotionFrontMargins_SameItemGift`: Type 3 validation
5. `CK_PromotionFrontMargins_DifferentItemGift`: Type 4 validation

### Indexes Added:

- `IX_PromotionFrontMargins_DiscountType`
- `IX_PromotionFrontMargins_GiftItemNumber`

## Discount Type Mapping

| Type | Name                  | Description                  | Required Fields                                             |
| ---- | --------------------- | ---------------------------- | ----------------------------------------------------------- |
| 1    | Percentage Discount   | Chiết khấu theo %            | DiscountPercentage                                          |
| 2    | Fixed Amount Discount | Chiết khấu số tiền cố định   | FixedDiscountAmount                                         |
| 3    | Same Item Gift        | Mua hàng tặng hàng cùng loại | BuyQuantity, GiftQuantity                                   |
| 4    | Different Item Gift   | Tặng hàng khác loại          | GiftItemNumber, GiftItemName, GiftItemUOM, GiftItemQuantity |

## Business Logic Formulas

### Case I.1: Percentage Discount

```
Giá Mua = Nguyên giá × (1 - % chiết khấu)
```

### Case I.2: Fixed Amount Discount

```
Giá Mua = Nguyên giá - (Tổng tiền CK / Tổng giá trị ĐH × Giá trị item)
```

### Case II: Same Item Gift

```
Giá Mua = Tổng giá trị theo mã hàng / Tổng số lượng theo mã hàng
VD: Mua 10 HOP tặng 1 HOP, Giá 10 triệu => Giá mua = 10 triệu/11
```

### Case III: Different Item Gift

```
Giá mua không đổi, tạo thêm gift line với giá = 0
```

## KHAI BÁO FRONT MARGIN - WORKFLOW CHI TIẾT

### Bước 1: Tạo Promotion Header

```csharp
var header = new PromotionHeader
{
    ProgramName = "SAIKO_2025_Q1_FM",
    Description = "Front Margin Q1 2025",
    VendorCode = "SAIKO",
    ProgramType = 1, // FrontMargin
    StartDate = new DateTime(2025, 1, 1),
    EndDate = new DateTime(2025, 3, 31),
    Status = 1, // Draft
    RequireApprovalBeforeReward = false,
    AutoApply = true
};
```

### Bước 2: Khai báo từng loại Front Margin

#### Case I.1: Chiết khấu theo phần trăm

```csharp
var percentageDiscount = new PromotionFrontMargin
{
    ProgramNumber = "SAIKO_2025_Q1_FM",
    LineNumber = 1,
    ItemNumber = "931280",
    ItemName = "BB ZINC",
    UnitOfMeasure = "Vi",
    DiscountType = 1, // Percentage
    DiscountPercentage = 10.5m, // 10.5%
    MinimumQuantity = 100, // Tối thiểu 100 vi
    MinimumAmount = 1000000, // Tối thiểu 1 triệu
    MaximumDiscountAmount = 500000, // Tối đa CK 500k
    Status = 1,
    Notes = "Chiết khấu 10.5% cho BB ZINC"
};
```

#### Case I.2: Chiết khấu số tiền cố định

```csharp
var fixedAmountDiscount = new PromotionFrontMargin
{
    ProgramNumber = "SAIKO_2025_Q1_FM",
    LineNumber = 2,
    ItemNumber = "930003",
    ItemName = "CHI NHA KHOA YUMIKO 40M",
    UnitOfMeasure = "Vi",
    DiscountType = 2, // Fixed Amount
    FixedDiscountAmount = 2000000, // 2 triệu cho toàn đơn hàng
    MinimumAmount = 10000000, // Đơn hàng tối thiểu 10 triệu
    Status = 1,
    Notes = "Chiết khấu 2 triệu cho đơn hàng từ 10 triệu"
};
```

#### Case II: Mua hàng tặng hàng cùng loại

```csharp
var sameItemGift = new PromotionFrontMargin
{
    ProgramNumber = "SAIKO_2025_Q1_FM",
    LineNumber = 3,
    ItemNumber = "931280",
    ItemName = "BB ZINC",
    UnitOfMeasure = "Vi",
    DiscountType = 3, // Same Item Gift
    BuyQuantity = 10, // Mua 10
    GiftQuantity = 1, // Tặng 1
    MinimumQuantity = 10,
    Status = 1,
    Notes = "Mua 10 tặng 1 BB ZINC"
};
```

#### Case III: Tặng hàng khác loại

```csharp
var differentItemGift = new PromotionFrontMargin
{
    ProgramNumber = "SAIKO_2025_Q1_FM",
    LineNumber = 4,
    ItemNumber = "930003",
    ItemName = "CHI NHA KHOA YUMIKO 40M",
    UnitOfMeasure = "Vi",
    DiscountType = 4, // Different Item Gift
    GiftItemNumber = "931280",
    GiftItemName = "BB ZINC",
    GiftItemUOM = "Vi",
    GiftItemQuantity = 5, // Tặng 5 vi BB ZINC
    MinimumQuantity = 50, // Khi mua từ 50 vi CHI NHA KHOA
    Status = 1,
    Notes = "Mua 50 CHI NHA KHOA tặng 5 BB ZINC"
};
```

### Bước 3: Validation Rules cho từng Case

#### Validation cho Case I.1 (Percentage):

- `DiscountPercentage` phải từ 0.01 đến 100
- `DiscountType = 1`
- Các field khác phải null: `FixedDiscountAmount`, `BuyQuantity`, `GiftQuantity`, `GiftItem*`

#### Validation cho Case I.2 (Fixed Amount):

- `FixedDiscountAmount` phải > 0
- `DiscountType = 2`
- `DiscountPercentage = 0`
- Các field khác phải null: `BuyQuantity`, `GiftQuantity`, `GiftItem*`

#### Validation cho Case II (Same Item Gift):

- `BuyQuantity` và `GiftQuantity` phải > 0
- `DiscountType = 3`
- `DiscountPercentage = 0`
- Các field khác phải null: `FixedDiscountAmount`, `GiftItem*`

#### Validation cho Case III (Different Item Gift):

- `GiftItemNumber`, `GiftItemName`, `GiftItemUOM` không được null/empty
- `GiftItemQuantity` phải > 0
- `DiscountType = 4`
- `DiscountPercentage = 0`
- Các field khác phải null: `FixedDiscountAmount`, `BuyQuantity`, `GiftQuantity`

### Bước 4: UI Workflow

#### 4.1. Promotion Header Form

1. Chọn Vendor từ dropdown
2. Nhập Program Name và Description
3. Chọn Start Date và End Date
4. Set ProgramType = 1 (FrontMargin)
5. Chọn AutoApply = true

#### 4.2. Front Margin Lines Form

1. **Chọn Discount Type** từ dropdown:

   - "Chiết khấu theo phần trăm"
   - "Chiết khấu số tiền cố định"
   - "Mua hàng tặng hàng cùng loại"
   - "Tặng hàng khác loại"

2. **Dynamic Form Fields** theo Discount Type:

   - Type 1: Show `DiscountPercentage` field
   - Type 2: Show `FixedDiscountAmount` field
   - Type 3: Show `BuyQuantity`, `GiftQuantity` fields
   - Type 4: Show `GiftItemNumber`, `GiftItemName`, `GiftItemUOM`, `GiftItemQuantity` fields

3. **Common Fields** cho tất cả types:
   - Item Number (với lookup)
   - Item Name (auto-fill)
   - Unit of Measure (auto-fill)
   - Minimum Quantity (optional)
   - Minimum Amount (optional)
   - Maximum Discount Amount (optional)
   - Notes

#### 4.3. Preview & Calculation

- Show calculation preview khi nhập data
- Validate real-time
- Show error messages nếu có

### Bước 5: Business Logic Implementation

#### 5.1. Calculation Service Structure

```csharp
public interface IFrontMarginCalculationService
{
    Task<FrontMarginResult> CalculateAsync(POLineDto poLine, PromotionFrontMargin promotion);
    Task<List<POLineDto>> CreateGiftLinesAsync(PromotionFrontMargin promotion, decimal triggerQuantity);
    Task<bool> IsApplicableAsync(POLineDto poLine, PromotionFrontMargin promotion);
}
```

#### 5.2. Integration Points

- **PO Creation**: Auto-apply khi tạo PO
- **PO Edit**: Re-calculate khi edit PO lines
- **PO Approval**: Finalize Front Margin calculations

## Next Steps

1. **Run Migration**: Execute `AddFrontMarginDiscountTypes.sql`
2. **Create Validation**: Implement validation rules cho từng case
3. **Build UI Forms**: Tạo dynamic forms cho khai báo
4. **Implement Calculators**: Create calculation logic for each type
5. **PO Integration**: Integrate với PO creation workflow
6. **Testing**: Test all scenarios và edge cases

## VÍ DỤ THỰC TẾ - KHAI BÁO HOÀN CHỈNH

### Scenario: Chương trình Front Margin cho SAIKO Q1 2025

#### Step 1: Tạo Header

```json
{
  "programName": "SAIKO_2025_Q1_FM",
  "description": "Chương trình Front Margin SAIKO Quý 1/2025",
  "vendorCode": "SAIKO",
  "programType": 1,
  "startDate": "2025-01-01",
  "endDate": "2025-03-31",
  "status": 1,
  "requireApprovalBeforeReward": false,
  "autoApply": true,
  "maxBudgetAmount": 50000000,
  "priority": 1
}
```

#### Step 2: Khai báo 4 loại Front Margin Lines

**Line 1: Percentage Discount (10% cho BB ZINC)**

```json
{
  "programNumber": "SAIKO_2025_Q1_FM",
  "lineNumber": 1,
  "itemNumber": "931280",
  "itemName": "BB ZINC",
  "unitOfMeasure": "Vi",
  "discountType": 1,
  "discountPercentage": 10.0,
  "minimumQuantity": 50,
  "minimumAmount": 500000,
  "status": 1,
  "notes": "Chiết khấu 10% cho BB ZINC khi mua từ 50 vi"
}
```

**Line 2: Fixed Amount (2 triệu cho đơn hàng CHI NHA KHOA)**

```json
{
  "programNumber": "SAIKO_2025_Q1_FM",
  "lineNumber": 2,
  "itemNumber": "930003",
  "itemName": "CHI NHA KHOA YUMIKO 40M",
  "unitOfMeasure": "Vi",
  "discountType": 2,
  "discountPercentage": 0,
  "fixedDiscountAmount": 2000000,
  "minimumAmount": 10000000,
  "status": 1,
  "notes": "Chiết khấu 2 triệu cho đơn hàng từ 10 triệu"
}
```

**Line 3: Same Item Gift (Mua 10 tặng 1 BB ZINC)**

```json
{
  "programNumber": "SAIKO_2025_Q1_FM",
  "lineNumber": 3,
  "itemNumber": "931280",
  "itemName": "BB ZINC",
  "unitOfMeasure": "Vi",
  "discountType": 3,
  "discountPercentage": 0,
  "buyQuantity": 10,
  "giftQuantity": 1,
  "minimumQuantity": 10,
  "status": 1,
  "notes": "Mua 10 tặng 1 BB ZINC"
}
```

**Line 4: Different Item Gift (Mua CHI NHA KHOA tặng BB ZINC)**

```json
{
  "programNumber": "SAIKO_2025_Q1_FM",
  "lineNumber": 4,
  "itemNumber": "930003",
  "itemName": "CHI NHA KHOA YUMIKO 40M",
  "unitOfMeasure": "Vi",
  "discountType": 4,
  "discountPercentage": 0,
  "giftItemNumber": "931280",
  "giftItemName": "BB ZINC",
  "giftItemUOM": "Vi",
  "giftItemQuantity": 5,
  "minimumQuantity": 100,
  "status": 1,
  "notes": "Mua 100 CHI NHA KHOA tặng 5 BB ZINC"
}
```

## CHECKLIST KHAI BÁO FRONT MARGIN

### ✅ Pre-Setup Checklist

- [ ] Database migration đã chạy thành công
- [ ] Vendor đã được setup trong hệ thống
- [ ] Items đã có trong master data
- [ ] User có quyền tạo promotion

### ✅ Header Setup Checklist

- [ ] Program Name unique và meaningful
- [ ] Vendor Code hợp lệ
- [ ] Start Date < End Date
- [ ] Start Date >= Current Date
- [ ] Program Type = 1 (FrontMargin)
- [ ] Budget amount hợp lý (nếu có)

### ✅ Line Setup Checklist

#### Common cho tất cả types:

- [ ] Item Number tồn tại trong hệ thống
- [ ] Item Name và UOM match với master data
- [ ] Line Number unique trong program
- [ ] Status = 1 (Active)

#### Type 1 (Percentage) Checklist:

- [ ] DiscountType = 1
- [ ] DiscountPercentage: 0.01 - 100
- [ ] Các field khác = null/0: FixedDiscountAmount, BuyQuantity, GiftQuantity, GiftItem\*

#### Type 2 (Fixed Amount) Checklist:

- [ ] DiscountType = 2
- [ ] FixedDiscountAmount > 0
- [ ] DiscountPercentage = 0
- [ ] Các field khác = null/0: BuyQuantity, GiftQuantity, GiftItem\*

#### Type 3 (Same Item Gift) Checklist:

- [ ] DiscountType = 3
- [ ] BuyQuantity > 0 và GiftQuantity > 0
- [ ] DiscountPercentage = 0
- [ ] Các field khác = null/0: FixedDiscountAmount, GiftItem\*

#### Type 4 (Different Item Gift) Checklist:

- [ ] DiscountType = 4
- [ ] GiftItemNumber tồn tại trong hệ thống
- [ ] GiftItemName, GiftItemUOM không empty
- [ ] GiftItemQuantity > 0
- [ ] DiscountPercentage = 0
- [ ] Các field khác = null/0: FixedDiscountAmount, BuyQuantity, GiftQuantity

### ✅ Testing Checklist

- [ ] Validation rules hoạt động đúng
- [ ] Calculation logic chính xác
- [ ] UI hiển thị đúng theo discount type
- [ ] PO integration hoạt động
- [ ] Gift lines được tạo đúng (type 3,4)
- [ ] Performance acceptable với large data

### ✅ Go-Live Checklist

- [ ] User training hoàn thành
- [ ] Documentation cập nhật
- [ ] Backup database trước khi deploy
- [ ] Monitoring setup
- [ ] Rollback plan sẵn sàng

## TROUBLESHOOTING

### Common Issues:

1. **Validation Error: "Invalid discount type"**

   - Check DiscountType = 1,2,3,4
   - Ensure required fields for each type are provided

2. **Calculation Error: "Division by zero"**

   - Check BuyQuantity > 0 for type 3
   - Check total PO amount > 0 for type 2

3. **Gift Item Not Found**

   - Verify GiftItemNumber exists in Items table
   - Check GiftItemUOM matches item's UOM

4. **Performance Issues**
   - Add indexes on frequently queried fields
   - Consider caching for item lookups
   - Optimize calculation queries

## Files Modified/Created

### Modified:

- `PromotionFrontMargin.cs` - Entity updated
- `CreatePromotionFrontMarginDto.cs` - DTO updated
- `GetPromotionFrontMarginDto.cs` - DTO updated
- `UpdatePromotionFrontMarginDto.cs` - DTO updated

### Created:

- `AddFrontMarginDiscountTypes.sql` - Migration script
- `CreatePromotionFrontMarginDtoValidator.cs` - Validation
- `UpdatePromotionFrontMarginDtoValidator.cs` - Validation
- `FrontMarginHelper.cs` - Helper methods
- `PromotionFrontMarginExtensions.cs` - Extension methods
- `FrontMarginDatabaseUpdate.md` - This documentation

## Backward Compatibility

- Existing promotions will automatically have `DiscountType = 1` (Percentage)
- Existing `DiscountPercentage` values are preserved
- All new fields are nullable to maintain compatibility
- AutoMapper profiles use ReverseMap() so they automatically handle new fields
