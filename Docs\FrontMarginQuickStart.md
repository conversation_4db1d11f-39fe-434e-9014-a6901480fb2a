# Front Margin System - Quick Start Guide

## 🚀 Quick Setup

### 1. Database Migration
```sql
-- Run this script to add Front Margin columns
EXEC Server/PurchaseManager.Storage/Migrations/SimpleFrontMarginMigration.sql

-- Verify migration was successful
EXEC Server/PurchaseManager.Storage/Migrations/VerifyFrontMarginMigration.sql
```

### 2. Create Test Data
```sql
-- Create comprehensive test data for all 4 discount types
EXEC Server/PurchaseManager.Storage/Migrations/CreateFrontMarginTestData.sql
```

### 3. Test API Endpoints
Import the Postman collection: `Server/PurchaseManager.Server/Tests/FrontMarginAPI.postman_collection.json`

## 📋 4 Discount Types Overview

### Type 1: Percentage Discount (Chiết khấu theo %)
- **Formula**: `New Price = Original Price × (1 - Discount%)`
- **Example**: 10% discount on orders above 100 units
- **API Example**:
```json
{
  "discountType": 1,
  "discountPercentage": 10.5,
  "minimumQuantity": 100,
  "minimumAmount": 1000000
}
```

### Type 2: Fixed Amount Discount (Chiết khấu số tiền cố định)
- **Formula**: `New Price = Original Price - (Fixed Amount / Quantity)`
- **Example**: 50,000 VND discount on orders above 500,000 VND
- **API Example**:
```json
{
  "discountType": 2,
  "fixedDiscountAmount": 50000,
  "minimumAmount": 500000,
  "maximumDiscountAmount": 200000
}
```

### Type 3: Same Item Gift (Mua hàng tặng hàng cùng loại)
- **Formula**: `New Unit Cost = Total Value / (Purchase Qty + Gift Qty)`
- **Example**: Buy 10 get 2 free
- **API Example**:
```json
{
  "discountType": 3,
  "buyQuantity": 10,
  "giftQuantity": 2,
  "minimumQuantity": 10
}
```

### Type 4: Different Item Gift (Tặng hàng khác loại)
- **Formula**: No price change, adds gift line to PO
- **Example**: Buy 50 Vitamin C, get 30 Vitamin D free
- **API Example**:
```json
{
  "discountType": 4,
  "giftItemNumber": "VITAMIN_D",
  "giftItemName": "Vitamin D3 1000IU",
  "giftItemUOM": "Viên",
  "giftItemQuantity": 30,
  "minimumQuantity": 50
}
```

## 🔧 API Endpoints

### Base URL: `/api/FrontMargin`

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/filtered` | Get Front Margins with filtering |
| GET | `/{number}` | Get Front Margin by number |
| POST | `/` | Create new Front Margin |
| PUT | `/{number}` | Update Front Margin |
| DELETE | `/bulk` | Bulk delete Front Margins |
| GET | `/discount-types` | Get discount types dropdown |
| POST | `/validate` | Validate Front Margin config |

### PO Integration: `/api/po-front-margin`

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/applicable-promotions` | Get applicable promotions for PO |
| POST | `/calculate-line-discount` | Calculate discount for PO line |
| POST | `/apply-to-po` | Apply Front Margin to entire PO |
| POST | `/preview` | Preview calculations |

## 📝 Usage Examples

### 1. Get All Front Margins with Filtering
```http
GET /api/FrontMargin/filtered?discountType=1&pageSize=10&pageIndex=0
```

### 2. Create Percentage Discount
```http
POST /api/FrontMargin
Content-Type: application/json

{
  "programNumber": "FM2025001",
  "lineNumber": 1,
  "itemNumber": "PARACETAMOL500",
  "itemName": "Paracetamol 500mg",
  "unitOfMeasure": "Viên",
  "discountType": 1,
  "discountPercentage": 15.0,
  "minimumQuantity": 100,
  "status": 1
}
```

### 3. Apply Front Margin to PO
```http
POST /api/po-front-margin/apply-to-po
Content-Type: application/json

{
  "poHeader": {
    "poNumber": "PO2025001",
    "buyFromVendorNumber": "VENDOR001"
  },
  "poLines": [
    {
      "itemNumber": "PARACETAMOL500",
      "quantity": 200,
      "unitCost": 1000
    }
  ]
}
```

## 🧪 Testing

### Quick Test with Postman
1. Import collection: `FrontMarginAPI.postman_collection.json`
2. Set variables:
   - `baseUrl`: `https://localhost:7001`
   - `testProgramNumber`: `TEST_FM_2025_001`
3. Run tests in order (1-12)

### SQL Calculation Tests
```sql
-- Test all calculation scenarios
EXEC Server/PurchaseManager.Storage/Migrations/TestFrontMarginCalculations.sql
```

## 🔍 Validation Rules

### Common Rules
- `ProgramNumber`: Required, must exist in PromotionHeaders
- `LineNumber`: Required, must be unique per program
- `ItemNumber`: Required, not empty
- `ItemName`: Required, not empty
- `UnitOfMeasure`: Required, not empty
- `DiscountType`: Required, must be 1-4

### Type-Specific Rules

#### Type 1 (Percentage)
- `DiscountPercentage`: 0.01 - 100.00
- Other discount fields must be null/zero

#### Type 2 (Fixed Amount)
- `FixedDiscountAmount`: > 0
- `DiscountPercentage`: Must be 0
- Gift fields must be null

#### Type 3 (Same Item Gift)
- `BuyQuantity`: > 0
- `GiftQuantity`: > 0
- `DiscountPercentage`: Must be 0
- Gift item fields must be null

#### Type 4 (Different Item Gift)
- `GiftItemNumber`: Required, not empty
- `GiftItemName`: Required, not empty
- `GiftItemUOM`: Required, not empty
- `GiftItemQuantity`: > 0
- `DiscountPercentage`: Must be 0
- Buy/Gift quantity fields must be null

## 🚨 Troubleshooting

### Common Issues

#### Migration Errors
```
Error: Incorrect syntax near 'THROW'
Solution: Use SimpleFrontMarginMigration.sql instead
```

#### Validation Failures
```
Error: "Validation failed"
Solution: Check discount type specific rules above
```

#### Calculation Errors
```
Error: No applicable promotions
Solution: Check vendor code, item number, date range, and minimum requirements
```

### Performance Issues
- Ensure indexes exist on `DiscountType` and `GiftItemNumber`
- Use filtered queries with appropriate page sizes
- Monitor calculation service performance

## 📊 Monitoring

### Key Metrics
- API response times
- Calculation accuracy
- Database query performance
- Validation error rates

### Health Checks
```http
GET /api/FrontMargin/discount-types
# Should return 4 discount types

GET /api/FrontMargin/filtered?pageSize=1
# Should return paged result structure
```

## 🔗 Related Documentation

- [Complete System Documentation](FrontMarginSystem.md)
- [API Reference](../Server/PurchaseManager.Server/Controllers/FrontMarginController.cs)
- [Calculation Engine](../Server/PurchaseManager.Server/Services/Promotions/FrontMarginCalculationService.cs)
- [Test Cases](../Server/PurchaseManager.Server/Tests/)

## 📞 Support

For issues or questions:
1. Check validation rules above
2. Run test scripts to verify setup
3. Review API documentation
4. Check unit tests for examples

---

**Ready to use Front Margin system! 🎯**
